<template>
  <div class="tab">
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23">
        <div class="title_header">离职申请</div>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center" class="search">
      <el-col :span="23">
        <el-row type="flex" justify="space-between">
          <el-col :span="12" style="text-align: left">
            <el-button
              class="btnStyle"
              type="primary"
              style="background-color: #0e61c9"
              size="medium"
              @click="addmenu"
              >新增
            </el-button>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center" style="margin-top: 15px">
      <el-col :span="23">
        <el-table
          :data="tableData"
          class="tableT"
          height="calc(100vh - 268px)"
          style="width: 100%"
          highlight-current-row
        >
          <el-table-column
            prop="quitexplain"
            label="离职原因"
          ></el-table-column>
          <el-table-column prop="statu" label="状态">
            <template slot-scope="prop">
              <span v-show="prop.row.statu == 1">审批中</span>
              <span v-show="prop.row.statu == 2">审批未通过</span>
              <span v-show="prop.row.statu == 3">审批通过</span>
              <span v-show="prop.row.statu == 4">离职确认完成</span>
              <span v-show="prop.row.statu == 5">离职完成</span>
            </template>
          </el-table-column>
          <el-table-column prop="createtime" label="申请时间"></el-table-column>
          <el-table-column label="操作" width="150">
            <template slot-scope="scope">
              <el-button
                type="success"
                size="mini"
                plain
                @click="dialogOpen(scope.$index, scope.row)"
                icon="el-icon-warning-outline"
              >
              </el-button>
              <el-tooltip
                placement="top"
                effect="light"
                v-if="scope.row.statu == 4 || scope.row.statu == 5"
              >
                <div slot="content" style="color: #409eff">
                  下载劳动合同终止书
                </div>
                <el-button
                  type="success"
                  size="mini"
                  plain
                  @click="downLoadContract(scope.$index, scope.row)"
                  icon="el-icon-download"
                >
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <!-- 新增离职申请弹窗 -->
    <el-dialog
      title="新增离职申请"
      :visible.sync="adddialogVisible"
      width="1000px"
      :show-close="false"
      class="addP"
      top="3vh"
    >
      <div style="margin-left: 70%">
        <div
          style="
            color: #409eff;
            cursor: pointer;
            height: 30px;
            line-height: 30px;
          "
          @click="DownloadModel"
        >
          <i class="el-icon-download" type="primary"></i
          >正文导出离职申请（推荐）
        </div>
        <div
          style="color: #409eff; cursor: pointer"
          @click="BlankDownloadModel"
        >
          <i class="el-icon-download" type="primary"></i>空白离职申请
        </div>
      </div>
      <div style="margin-top: 20px">
        <el-form
          label-position="left"
          label-width="150px"
          :rules="addrule"
          ref="addform"
          :model="addform"
        >
          <el-form-item label="正文" prop="quitExplain">
            <el-input
              type="textarea"
              :rows="6"
              placeholder="请输入离职原因"
              v-model.trim="addform.quitExplain"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="离职证明发送方式" prop="quitSend">
            <el-radio v-model="addform.quitSend" label="1">邮寄</el-radio>
            <el-radio v-model="addform.quitSend" label="0">自取</el-radio>
          </el-form-item>
          <el-row>
            <el-col :span="12" v-if="addform.quitSend == 1">
              <el-form-item
                label="收件人"
                prop="addressee"
                :rules="{
                  required: addform.quitSend == 1,
                  message: '请输入收件人姓名',
                  trigger: 'blur',
                }"
              >
                <el-input
                  placeholder="请输入收件人姓名"
                  v-model="addform.addressee"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="addform.quitSend == 1">
              <el-form-item
                label="联系方式"
                prop="telephone"
                :rules="{
                  required: addform.quitSend == 1,
                  message: '请输入正确的联系方式',
                  trigger: 'blur',
                  pattern: /(^0\d{2,4}\-\d{7,8}$)|(^1[3|4|5|6|7|8|9][0-9]{9}$)/,
                }"
              >
                <el-input
                  placeholder="请输入联系方式"
                  v-model="addform.telephone"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="addform.quitSend == 1">
              <el-form-item
                label="联系地址"
                prop="address"
                :rules="{
                  required: addform.quitSend == 1,
                  message: '请输入联系地址',
                  trigger: 'blur',
                }"
              >
                <el-input
                  placeholder="请输入联系地址"
                  v-model="addform.address"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="上传附件" ref="imageUrl" prop="imageUrl">
            <el-upload
              ref="upload"
              class="avatar-uploader"
              :action="actionurl"
              list-type="picture-card"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :on-error="handleError"
              :before-upload="beforeAvatarUpload"
              accept=".jpg,.JPG,.jpeg,.png"
              :before-remove="beforeRemove"
              :limit="1"
              v-model="addform.imageUrl"
            >
              <img v-if="imageUrl" :src="imageUrl" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <div style="font-size: 12px; color: red">
              请上传手签字离职申请图片(保留《离职申请书》手签字原件与后续材料《劳动合同终止书》一并邮寄)
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="OKButton" @click="resumesend('addform')"
          >确定</el-button
        >
        <el-button
          @click="addmenuClose"
          class="CancButton"
          style="margin-left: 10px"
          >取 消</el-button
        >
      </span>
    </el-dialog>
    <!-- 详情页弹窗 -->
    <el-dialog
      title="审批详情"
      :visible.sync="dialogTableVisible"
      class="details"
      id="portlet-body"
    >
      <el-button type="primary" @click="printPage" class="no-print"
        >打印</el-button
      >
      <el-table
        :data="gridData"
        ref="gridData"
        :header-cell-style="headerCellStyle"
        class="elTable"
      >
        <el-table-column
          property="taskName"
          label="审批节点"
          width="140"
        ></el-table-column>
        <el-table-column
          property="approveResult"
          label="审批状态"
          width="140"
        ></el-table-column>
        <el-table-column
          property="staffName"
          label="审批人"
          width="140"
        ></el-table-column>
        <el-table-column
          property="approveResult"
          label="审批意见"
        ></el-table-column>
        <el-table-column
          property="approveRemark"
          label="审批详情"
        ></el-table-column>
      </el-table>
      <el-button @click="closePage" class="close">关闭</el-button>
    </el-dialog>
  </div>
</template>

<script>
import {
  getUserQuit,
  createSubmit,
  getApprovalFlow,
} from "@/api/human/resignApplication";

export default {
  name: "resignApplication",
  data() {
    return {
      tableData: [],
      totalcount: null,
      adddialogVisible: false,
      addform: {
        quitExplain: "",
        quitSend: "0",
        addressee: "",
        telephone: "",
        address: "",
        imageUrl: "",
      },
      addrule: {
        quitExplain: [
          { required: true, message: "请输入内容", trigger: "blur" },
          { min: 30, message: "最小长度为30", trigger: "blur" },
          { max: 400, message: "最多能输入400个字", trigger: "blur" },
        ],
        quitSend: [
          {
            required: true,
            message: "请选择离职证明发送方式",
            trigger: "change",
          },
        ],
        imageUrl: [
          { required: true, message: "请上传附件", trigger: "change" },
        ],
      },
      actionurl: process.env.VUE_APP_BASE_API + "/record/upload",
      imageUrl: "",
      file: "",
      filePath: "",
      dialogTableVisible: false,
      gridData: [],
      procInstId: "",
    };
  },
  created() {
    this.getTreeData();
  },
  methods: {
    // 列表查询
    getTreeData() {
      let userName = window.localStorage.getItem("Susername");
      getUserQuit({
        userName: userName,
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data;
        }
      });
    },
    // 取消
    addmenuClose() {
      this.adddialogVisible = false;
      this.$refs.addform.resetFields();
    },
    // 确定
    resumesend(formname) {
      let userName = window.localStorage.getItem("Susername");
      // let formData = new FormData();
      this.$refs[formname].validate((valid) => {
        if (valid) {
          // formData.append("quitExplain",this.tableData.quitExplain),
          //   formData.append("quitSend",this.addform.quitSend),
          //   formData.append("address",this.addform.address),
          //   formData.append("addressee",this.addform.addressee),
          //   formData.append("telephone",this.addform.telephone),
          //   formData.append("filePath",this.file),
          //   formData.append("userName",userName),
          let pramas = {
            ...this.addform,
            filePath: this.filePath,
            userName: userName,
          };
          // 新增
          createSubmit(pramas).then((res) => {
            this.adddialogVisible = false;
            this.getTreeData();
            this.$refs.addform.resetFields();
            if (res === "success") {
              this.$message({
                message: "离职提交成功",
                type: "success",
              });
            } else if (res == "nothing") {
              this.$message({
                message: "花名册中不存在此账号，请联系管理员！！！",
                type: "warning",
              });
            } else if (res == "repeat") {
              this.$message({
                message: "您已经提交过离职申请了！！！",
                type: "warning",
              });
            } else if (res == "noPicture") {
              this.$message({
                message: "离职申请上传异常，请刷新重试！！！",
                type: "warning",
              });
            } else if (res == "fail") {
              this.$message({
                message: "离职提交失败！！！",
                type: "error",
              });
            }
          });
        } else {
          this.$message({
            message: "请填写完整",
            type: "warning",
          });
          return false;
        }
      });
    },
    //新增角色
    addmenu() {
      this.adddialogVisible = true;
      this.$nextTick(() => {
        this.$refs.addform.resetFields();
      });
    },
    // 上传附件图片格式校验
    beforeAvatarUpload(file) {
      // console.log(file, "file....");
      // this.file = file;
      const isJPG =
        file.type === "image/jpeg" ||
        file.type === "image/jpg" ||
        file.type === "image/png";
      if (!isJPG) {
        this.$message.error("只能上传图片格式!");
      }
      return isJPG;
    },
    // 上传附件成功
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
      this.addform.imageUrl = this.imageUrl
      this.$nextTick(() => {
        this.$refs.imageUrl.clearValidate(); // 关闭校验
        this.filePath = res.data;
      });
    },
    handleError(err) {
      console.error("Error during upload:", err);
      this.addform.imageUrl = ''
    },
    beforeRemove(file) {
      const isDocument =
        file.type === "image/jpeg" ||
        file.type === "image/jpg" ||
        file.type === "image/png";
      if (!isDocument) {
        return;
      } else return this.$confirm(`确定移除 ${file.name}？`);
    },
    // 空白模板下载点击事件
    BlankDownloadModel() {
      // getResignTemplate().then((res) => {
      const downloadUrl =
        process.env.VUE_APP_BASE_API + "/resign/getResignTemplate";
      window.location.href = downloadUrl;
      // });
    },
    // 正文导出离职申请
    DownloadModel() {
      var userName = window.localStorage.getItem("Susername");
      // downLoad({
      //   userName: userName,
      //   reason: this.addform.QuitExplain,
      // }).then((res) => {
      //   window.location.href(res)
      // });
      const downloadUrl =
        process.env.VUE_APP_BASE_API +
        "/resign/downLoad?userName=" +

        userName +
        "&&reason=" +
        this.addform.quitExplain;
      window.location.href = downloadUrl;
    },
    //打开详情
    dialogOpen(index, row) {
      this.dialogTableVisible = true;
      // console.log(('row:',row));
      getApprovalFlow({ procInstId: row.procInstId }).then((res) => {
        this.gridData = res.data;
      });
    },
    // 打印
    printPage() {
      // this.$nextTick(() => {
      //   this.$refs.gridData.doLayout(); // 确保 yourTable 是你的 el-table 的 ref
      // });
      //这里是获取我需要打印的dom 元素
      let newContent = document.getElementById("portlet-body").innerHTML;
      //创建iframe
      const iframeEl = document.createElement("iframe");
      iframeEl.class = "iframe";
      //将元素放置最底层，防止其覆盖了其他元素
      iframeEl.style.position = "fixed";
      iframeEl.style.zIndex = -99;
      //添加到页面
      document.querySelector("body").append(iframeEl);
      const documentEl = iframeEl.contentDocument;
      // 查找iframe中的按钮并移除
      //把打印dom元素赋值给iframe
      documentEl.body.innerHTML = newContent;
      // 查找所有的按钮并隐藏
      var buttons = documentEl.querySelectorAll("button");
      buttons.forEach((item) => {
        item.style.display = "none";
      });
      // 然后执行打印
      iframeEl.contentWindow.print();
    },
    // 下载打印劳动合同终止书
    downLoadContract(index, row) {
      var userName = window.localStorage.getItem("Susername");
      if (row.statu == 4 || row.statu == 5) {
        const downloadUrl =
          "/ssoapi/resign/downLoadContractTermination?userName=" + userName;
        window.location.href = downloadUrl;
      } else {
        return;
      }
    },
    //关闭详情页
    closePage() {
      this.dialogTableVisible = false;
    },
    //详情表头颜色设置
    headerCellStyle() {
      // 返回一个样式对象，其中包含背景颜色和其他样式
      return {
        background: "#f2f3f7", // 设置背景颜色
        color: "#606266", // 设置字体颜色
      };
    },
  },
};
</script>

<style scoped lang="less">
.tab {
  background-color: #21274d;
  margin: -20px 0;
  height: calc(100vh - 90px);

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #21274d;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  .no-print {
    float: right;
    margin-bottom: 5px;
  }
}

.title {
  height: 66px;
  line-height: 66px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  line-height: 148px;
  text-align: center;
}

.avatar {
  width: 148px;
  height: 148px;
  display: block;
}

@media screen and (min-width: 600px) and (max-width: 1300px) {
  .tableT {
    margin-bottom: -5px;
  }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
  .tableT {
    margin-bottom: 30px;
  }
}

.close {
  margin: auto;
  display: block;
  margin-top: 5px;
}
@media print {
  .elTable th.gutter {
    display: table-cell !important;
  }
  /* 其他针对打印的样式调整 */
}
.el-table-column {
  text-align: center !important;
}
</style>