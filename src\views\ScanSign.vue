<template>
  <div class="home" v-loading.fullscreen="loading" element-loading-text="拼命加载中" element-loading-background="rgb(33,39,77, 1)">
    <div class="cartoon">
<!--      左侧小方块-->
      <div class="diamond-box">
        <div class="diamond dia1">
          <div class="light g-anim1"></div>
        </div>
        <div class="diamond dia2">
          <div class="light g-anim3"></div>
        </div>
        <div class="diamond dia3">
          <div class="light g-anim2"></div>
        </div>
<!--        <img class="circle fadexk-k1" src="../assets/images/trends/circle-light.png" />-->
      </div>
<!--      上方大屏幕-->
      <div class="screen-box shake">
        <div class="screen"></div>
        <div class="screen-light g-anim1"></div>
      </div>
<!--      中间大圆台-->
      <div class="terrace">
        <div class="terrace-base">
          <div class="terrace-base-light g-anim3"></div>
          <div class="terrace-stage">
            <div class="box-gs">
              <img class="bimgan11" src="../assets/images/trends/source-RAMS.png"  width="80%"/>
              <img class="bimgan5" src="../assets/images/trends/source-EHR.png"  width="80%"/>
              <img class="bimgan9" src="../assets/images/trends/source-RMS.png"  width="80%"/>
              <!-- <img class="bimgan0" src="../assets/images/trends/center-light2.png"/>
              <img class="bimgan1" src="../assets/images/trends/source-CRM.png"  width="78%"/>
              <img class="bimgan2" src="../assets/images/trends/center-light1.png" />
              <img class="bimgan3" src="../assets/images/trends/center-light2.png" />
              <img class="bimgan4" src="../assets/images/trends/circle-s.png" />
              <img class="bimgan6" src="../assets/images/trends/center-light2.png" />
              <img class="bimgan7" src="../assets/images/trends/source-EOSS.png"  width="80%" />
              <img class="bimgan8" src="../assets/images/trends/circle-s.png" />
              <img class="bimgan10" src="../assets/images/trends/source-TSM.png"  width="80%"/>
              <img class="bimgan12" src="../assets/images/trends/source-PMS.png"  width="80%"/>
                <img class="bimgan13" src="../assets/images/trends/source-TRAIN.png"  width="150%"/>
                <img class="bimgan14" src="../assets/images/trends/source-FMS.png"  width="80%"/>
                <img class="bimgan15" src="../assets/images/trends/source-DM.png"  width="50%"/> -->
            </div>
          </div>
        </div>
      </div>
<!--      右侧建筑物-->
      <div class="build-box">
        <div class="build-base"></div>
        <div class="build-light g-anim2"></div>
      </div>
    </div>
    <div class="logo-box">
      <img class="logo-img" src="../assets/images/logo1.png" alt=""  height="82"/>
      <div class="logo-title">中国企联办公自动化平台</div>
    </div>
    <div class="login_message">
      <div class="form-box" style="position:relative;">
        <div class="iframeDiv">
          <iframe id="iframeId" name="ifr" :src="url" class="iframe" title=""></iframe>
        </div>
        <div style="position:absolute;bottom:20px;right:18px;color: #a9a9a9;cursor: pointer;" @click="toScagin">账号密码登录</div>
        <!-- 调试信息 -->
        <div v-if="showDebugInfo" style="position:absolute;bottom:60px;right:18px;color: #a9a9a9;font-size: 12px;max-width: 300px;">
          <div>当前URL: {{ currentUrl }}</div>
          <div>检测到的Code: {{ detectedCode }}</div>
          <div>SessionStorage Code: {{ sessionCode }}</div>
          <div>扫码登录状态: {{ scanLoginStatus }}</div>
        </div>
        <div style="position:absolute;bottom:40px;right:18px;color: #a9a9a9;cursor: pointer;font-size: 12px;" @click="toggleDebug">{{ showDebugInfo ? '隐藏' : '显示' }}调试信息</div>
        <div style="position:absolute;bottom:20px;left:18px;color: #a9a9a9;cursor: pointer;font-size: 12px;" @click="goToDebug">调试页面</div>
    
      </div>
    </div>
      <div class="starlist">
          <img class="star starflick" src="../assets/images/trends/star.png" />
          <img class="star1 starflick1" src="../assets/images/trends/star.png" />
          <img class="star2 starflick2" src="../assets/images/trends/star.png" />
          <img class="star3 starflick3" src="../assets/images/trends/star.png" />
          <img class="star4 starflick4" src="../assets/images/trends/star.png" />
          <img class="star5 starflick" src="../assets/images/trends/star.png" />
      </div>

      <el-dialog
          title="系统提示"
          :visible.sync="dialogVisible"
          width="30%"
          class="tips"
          :show-close="false"
          :close-on-click-modal="false"
      >
          <div style="display: flex;flex-direction: column;align-items: center;">
            <img src="../assets/images/loudou.png" class="imgTran" alt="loudou"/>
            <el-statistic :value="deadline2" time-indices format="ss" @finish="hilarity">
            <template slot="suffix"> 秒后，即将跳转到新地址... </template>
          </el-statistic>
          <p style="line-height: 2;margin-top: 5px;color: #666;">启用域名形式访问统一门户, IP形式的地址即将停止, 请使用快捷按钮(Ctrl+D)的方式保存新域名方便下次使用。</p>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button  @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleClose">确 定</el-button>
          </span>
        </el-dialog>
</div>
</template>

<script>

import '../assets/css/cartoon.css'
import { mapActions } from "vuex";
import { setCookie } from "@/utils/cookie";
import { leavelogin,thirdLoginQR } from "@/api/login/login";
export default {
  name: "scanSign",
  data() {
    return {
      rememberName: false,
      rememberPwd: false,
      url: "",
      dialogVisible:false,
      deadline2: Date.now() + 1000 * 6,
      loading:true,
      showDebugInfo: false,
      currentUrl: '',
      detectedCode: '',
      sessionCode: '',
      scanLoginStatus: ''
    };
  },
  created() {
    //获取单点登录路径
    
    var test = window.location.href;
    if (test.indexOf("clientid") != -1) {
      let url = test.split("clientid=")[1];
      window.sessionStorage.setItem("clientid", url.split("#")[0]);
    } else {
      window.sessionStorage.removeItem("clientid");
    }
    window.localStorage.setItem("isLogin", false);
    if(window.location.href.indexOf('sso.sino-bridge')<0){
      //  this.dialogVisible=true
    }
  },
  mounted() {
    // 检查是否是企业微信回调
    const hasCallback = this.checkWeChatCallback();

    // 如果不是回调，才加载二维码
    if (!hasCallback) {
      thirdLoginQR().then((res) => {
        window.localStorage.setItem("status", false);
        this.url = res.data.qrUrl;

        console.log(this.url,'url')
        // 回调地址中是服务器测试地址的 https://scrm.sino-bridge.com:8098/oa/oa/#/
       // 这个url是https://open.work.weixin.qq.com/wwopen/sso/qrConnect?appid=ww061f1cdf0b1bb2f4&agentid=1000010&redirect_uri=https%3A%2F%2Fscrm.sino-bridge.com%3A8098%2Foa%2Foa%2F%23%2F&state=state
        // this.url = 'https://open.work.weixin.qq.com/wwopen/sso/qrConnect?appid=wwd7e14e07bf973c80&agentid=1000099&login_type=CorpApp&redirect_uri=https%3A%2F%2Fscrm.sino-bridge.com%3A8098%2Foa%2Foa&state=WWLogin'
        window.localStorage.setItem("scanLogin", true);//扫码登录
      }).catch((error) => {
        console.error('获取企业微信二维码失败:', error);
        this.$message({
          message: '获取企业微信二维码失败，请稍后重试',
          type: 'error'
        });
      });
    }
   
    if (window.location.href.indexOf("leaveOffice") != -1) {
      this.loading = true
      let userName=this.getStringBetween(window.location.href, 'userName=', '&passWord=')
      let passWord=this.getStringBetween(window.location.href, 'passWord=', '&key=')
      leavelogin({
        userName: userName,
        passWord: passWord,
        key: 'leaveOffice'
      }).then((res) => {
        if (res.code == 200) {
          setCookie("access_token", res.data.access_token, 0.125);
          setCookie("refresh_token", res.data.refresh_token, 0.125);
          window.localStorage.setItem("Susername", res.map.UserName);
          window.localStorage.setItem("LoginName", res.map.StaffName);
          // this.$router.push("/sino/index");
          this.loading = false
        }else{
          this.loading = false
          this.$message({
              message: res.message,
              type: "error",
         });
        }
      });
    } else {
      this.loading = false
    }
    //token验证提示--自动触发
    if (window.location.href.indexOf("msg") != -1) {
      let msg = window.location.href.split("msg=")[1];
      this.$message({
        showClose: true,
        message: msg,
        type: "error",
      });
    }
  },
  methods: {
    toScagin() {
      this.$router.replace("/");
    },
    ...mapActions({
      editUsername: "EDIT_USER",
    }),
    handleClose(){
      this.dialogVisible=false;
      this.hilarity()
    },
    hilarity() {
      // window.location.href='https://sso.sino-bridge.com'
      // window.location.href='https://scrm.sino-bridge.com:8098/oa/oa/'
    },
    getStringBetween(str, start, end) {
      const parts = str.split(start);
      if (parts.length < 2) return '';
      const middleParts = parts[1].split(end);
      return middleParts[0];
    },
    // 检查企业微信回调
    checkWeChatCallback() {
      this.updateDebugInfo();

      const urlParams = new URLSearchParams(window.location.search);
      const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);

      // 从URL参数或hash参数中获取code
      let code = urlParams.get('code') || hashParams.get('code');

      this.detectedCode = code || '未检测到';

      if (code) {
        console.log('检测到企业微信回调code:', code);
        // 存储code到sessionStorage
        sessionStorage.setItem('weChatcode', code);
        // 设置登录状态
        window.localStorage.setItem("isLogin", false);
        window.localStorage.setItem("scanLogin", true);

        // 清理URL中的参数，避免重复处理
        const cleanUrl = window.location.origin + window.location.pathname + '#/scanSign';
        window.history.replaceState({}, document.title, cleanUrl);

        // 延迟跳转，确保当前页面处理完成
        setTimeout(() => {
          this.$router.push('/sino/index');
        }, 100);

        return true; // 表示检测到回调
      }
      return false; // 表示没有检测到回调
    },
    // 切换调试信息显示
    toggleDebug() {
      this.showDebugInfo = !this.showDebugInfo;
      this.updateDebugInfo();
    },
    // 更新调试信息
    updateDebugInfo() {
      this.currentUrl = window.location.href;
      this.sessionCode = sessionStorage.getItem('weChatcode') || '无';
      this.scanLoginStatus = window.localStorage.getItem("scanLogin") || '无';
    },
    // 跳转到调试页面
    goToDebug() {
      this.$router.push('/wechatDebug');
    }
  },
  watch: {

  },
};
</script>
<style lang="less" scoped>
.home {
  width: 100%;
  height: 100vh;
  // background-position: center center;
  background: url("../assets/images/background/three.jpg") no-repeat;
  background-size:100% 100%;
  background-attachment:fixed;
  position: relative;
  overflow: hidden;
  /deep/.el-statistic .con .number{
    color: #409eff;
    font-size: 28px;
  }
  .logo-box {
    display: table-cell;
    height: 64px;
    position: absolute;
    left: 4%;
    top: 6%;

    .logo-img {
      display: inline-block;
      vertical-align: middle;
    }

    .logo-title {
      height: 55px;
      vertical-align: middle;
      line-height: 55px;
      font-size: 28px;
      color: rgba(255, 255, 255, 1);
      display: inline-block;
      margin-left: 20px;
      // position: absolute;
      // top: 6.5%;
      // left: 19%;
      border-left: 1px solid rgba(255, 255, 255, 0.65);
      padding-left: 30px;
    }
  }

  .login_message {
    position: absolute;
    right: 16%;
    top: 24%;

    .title {
      width: 600px;
      font-size: 42px;
      color: #fff;
      font-family: "xingkai";
      text-align: center;
      line-height: 70px;
    }

    .form-box {
      width:120%;
      height: 410px;
      background-image: url("../assets/images/login.png");
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      justify-content: center;
    }
  }
}

.iframeDiv {
  width: 78%;
  height: 100%;
  padding-top: 15px;
  overflow: hidden;
}
.imgTran{
  // position:absolute;
  //   z-index:3;
  //   left:45%;
  //   top:18%;
    height: 32px;
    width: 32px;
    //animation: myfirst2 3s infinite linear;
}
@keyframes myfirst2{
   from {transform: rotateX(0deg);}
   to {transform: rotateX(360deg);}
}
.iframe {
  width: 100%;
  height: 100%;
}

.active {
  color: #fff !important;
  text-decoration: underline;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter,
.slide-fade-leave-to

/* .slide-fade-leave-active for below version 2.1.8 */
  {
  transform: translateX(10px);
  opacity: 0;
}
@media screen and (min-width: 600px) and (max-width: 1200px) {
  .home .login_message .form-box {
    width: 100%;
    height: 360px;
  }

  .home .login_message {
    right: 11%;
    top: 15%;
  }
}
@media screen and (min-width: 1201px) and (max-width: 1400px) {
  .home .login_message .form-box {
    width: 115%;
    height: 400px;
  }

  .home .login_message {
    right: 15%;
    top: 15%;
  }
}
@media screen and (min-width: 1401px) and (max-width: 1500px) {
  .home .login_message .form-box {
    width: 130%;
    height: 415px;
  }

  .home .login_message {
    right: 17%;
    top: 18%;
  }
}
@media screen and (min-width: 1501px) and (max-width: 1600px) {
  .home .login_message .form-box {
    width: 130%;
    height: 415px;
  }

  .home .login_message {
    right: 17%;
    top: 21%;
  }
}
@media screen and (min-width: 1601px) and (max-width: 1800px) {
  .home .login_message .form-box {
    width: 135%;
    height: 415px;
  }

  .home .login_message {
    right: 19%;
  }
}
@media screen and (min-width: 1801px) and (max-width: 1920px) {
  .home .login_message .form-box {
    width: 150%;
    height: 420px;
  }
    .home .login_message {
      right: 21%;
      top: 25%;
    }
}

</style>
