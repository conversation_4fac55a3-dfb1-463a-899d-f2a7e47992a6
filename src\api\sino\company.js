import request from "@/utils/request";
import queryString from 'query-string';

export const getCompanyStaff = (param) => {
  const isExport = param.export == 1;
  const url = '/company/getEntryUser?'+ queryString.stringify(param);
  const suffUrl =window.location.href.indexOf('/sso')>-1?'/ssoapi':'/api';
  if (isExport) {
    const win = window.open(suffUrl + url, "_blank");
    win.focus();
    return;
  } else {
    return new Promise((resolve) => {
      request
        .get(
          "/company/getEntryUser?pageSize=" +
            param.pageSize +
            "&&pageNum=" +
            param.pageNum +
            "&&userName=" +
            param.userName +
            "&&dmValue=" +
            param.dmValue
        )
        .then((res) => {
          resolve(res);
        });
    });
  }
};
export const getCompanyList = () => {
  return new Promise((resolve) => {
    request.get("/company/list").then((res) => {
      resolve(res);
    });
  });
};
