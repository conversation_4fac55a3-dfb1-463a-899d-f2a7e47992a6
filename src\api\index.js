import request from '@/utils/request'

export const getNewToken = (refreshToken)=>{
    return new Promise((resolve)=>{
        request.post("/oauth/token",{},{
            params:{
                grant_type:"refresh_token",
                refresh_token:refreshToken,
                client_id:"oauth",
                client_secret:"oauth"
            }
        }).then(res=>{
            resolve(res.data)
        })
    })
}

// 获取员工列表
export const GET_STAFF_LIST = '/staff/getAllStaff';
// 新增员工
export const CREATE_STAFF = '/staff/addStaff';
// 修改员工
export const UPDATE_STAFF = '/staff/updateStaff';
// 获取组织树形列表
export const GET_ORG_LIST = '/organization/listOrganizations';
// 获取部门树形列表
export const GET_DEPT_LIST = '/disdept/listDisdept';
// 获取岗位树形列表
export const GET_POSITION_LIST = '/position/getList';
// 获取公司列表
export const GET_COMPANY_LIST = '/company/list';
// 获取分公司员工列表
export const GET_COMPANY_STAFF_LIST = '/company/getEntryUser';
// 删除员工
export const DELETE_STAFF = '/staff/deleteStaff';
// 新增组织
export const CREATE_ORG = '/organization/saveOrg';
// 修改组织
export const UPDATE_ORG = '/organization/updateOrg';
// 删除组织
export const DELETE_ORG = '/organization/delOrg';
