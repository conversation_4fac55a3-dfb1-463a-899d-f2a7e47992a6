<template>
    <div class="org_wrapper" v-loading="pageLoading" element-loading-background="rgb(33,39,77, 0.8)">
        <el-row type="flex" justify="center" class="title">
            <el-col :span="23">
                <div  class="title_header">工作日志</div>
                <div style="margin-bottom:20px;float: right;">
                    <el-row type="flex" justify="space-between">
                        <div>
                        </div>
                    </el-row>
                </div>
            </el-col>
        </el-row>

        <el-row type="flex" justify="center">
            <el-col :span="23">
                <div class="main_container">

                    <el-row class="search_line">
                        <el-col :span="8"></el-col>
                        <el-col :span="16" style="text-align: right">
                            <el-input placeholder="请输入项目名称"  @keyup.enter.native="onSearchTxtChange" v-model="searchTxt"  style="width: 30%;margin-right: 10px" class="input-with-select"></el-input>
                            <el-date-picker
                                    class="custom-date-picker-bg"
                                    v-model="searchTime"
                                    type="daterange"
                                    range-separator="-"
                                    value-format="yyyy-MM-dd"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                            </el-date-picker>
                            <el-button class="add" size="medium" @click="onSearchTxtChange" style="margin-right: 10px">搜索</el-button>
                        </el-col>
                    </el-row>
                    <el-table :data="tableData" class="tableT" height="calc(100vh - 268px)" style="width: 100%;"
                              element-loading-background="rgb(33,39,77, 0.8)"
                              highlight-current-row v-loading="loading">
                        <el-table-column label="日期" width="150">
                            <template slot-scope="props">
                                <span>{{props.row.diarydate | converTime("YYYY-MM-DD")}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="worktypename" label="工作类型" width="120"></el-table-column>
                        <el-table-column prop="starttime"  label="开始时间" width="100"></el-table-column>
                        <el-table-column prop="endtime"  label="结束时间" width="100"></el-table-column>
                        <el-table-column prop="projectname"  label="项目名称" width="240"></el-table-column>
                        <el-table-column prop="content"  label="工作内容"></el-table-column>
                    </el-table>
                    <div class="paginator">
                        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                                       :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pageSize"
                                       layout="total, sizes, prev, pager, next, jumper" :total="totalcount">
                        </el-pagination>
                    </div>

                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import { getWorkDaily, getCompanyList } from "@/api/human/myLog.js";
    export default {
        name: "logmanage",
        data() {
            return {
                loading: false,
                pageLoading: false,
                userName: '',
                searchTxt: '',
                searchTime: '',
                filterText: '',
                pageSize: 10,
                currentPage: 1,
                formLabelWidth: '120px',
                dmValue: '',
                expandRowKeys: ['200'],
                totalcount: null,
                tableData: [],
                companyList:[]
            }
        },
        watch: {
            filterText(val) {
                this.$refs.tree2.filter(val);//查询
            }
        },
        created() {
            this.userName = window.localStorage.getItem("Susername");
            if (!!this.userName) {
                setTimeout(() => {
                    this.getWorkDailyData();
                }, 500);
            }
        },
        methods: {
            getWorkDailyData() {
                this.loading = true;
                let time1 = ''
                let time2 = ''
                if(!!this.searchTime&&this.searchTime !== null) {
                    time1 = this.searchTime[0]
                    time2 = this.searchTime[1]
                }
                const params = {
                    pageSize: this.pageSize,
                    pageNum: this.currentPage,
                    username: this.userName,
                    projectName: this.searchTxt,
                    startTime: time1,
                    endTime: time2,
                };
                getWorkDaily(params).then(res => {
                    if (res.code == 200) {
                        this.tableData = res.data.workList;
                        this.totalcount = res.data.totalCount;
                        this.loading = false;
                    }
                })
            },
            onSearchTxtChange() {
                this.currentPage = 1;
                this.getWorkDailyData();
            },
            handleSizeChange(val) {
                this.pageSize = val;
                this.getWorkDailyData();
            },
            handleCurrentChange(val) {
                this.currentPage = val;
                this.getWorkDailyData();
            },
        },
        computed: {
        }
    }
</script>

<style scoped lang="less">
    .paginator {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-bottom: 20px;
    }

    .org_wrapper {
        background-color: #21274D;
        margin: -20px 0;
        height: calc(100vh - 90px);
    }

    .add {
        width: 98px;
        //border-radius: 54px;
        background-color: #0e61c9;
        color: #ffffff;
        border: 0px;
    }

    .title {
        height: 66px;
        line-height: 66px;
        // border-bottom: 1px solid #eee;
        /*margin-bottom: 10px;*/
    }

    .main_container {
        display: flex;
        flex-direction: column;
        height:calc(100vh - 160px);
        .search_line {
            /*width: 300px;*/
            /*float: right;*/
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            .custom-date-picker-bg{
                width: 30%;margin-right: 10px;
                height: 38px;
            }
            .custom-date-picker-bg .el-input__inner {
                background-color: rgba(255, 255, 255, 0.25) !important;
            }
            /deep/ .el-input .el-input__inner {
                 //border-radius: 34px !important;
                 background-color: rgba(255, 255, 255, 0.25) !important;
                 color: rgba(199, 199, 199, 1) !important;
                 border: 0px;
             }
            /deep/ .el-range-editor.el-input__inner {
                //border-radius: 34px !important;
                background-color: rgba(255, 255, 255, 0.25) !important;
                color: rgba(199, 199, 199, 1) !important;
                border: 0px;
            }
            /deep/ .el-date-editor .el-range-input{
                background-color: rgba(255, 255, 255, 0) !important;
                color: rgba(199, 199, 199, 1) !important;
            }
            /deep/ .el-date-editor .el-range-separator{
                color: rgba(199, 199, 199, 1) !important;
            }
        }

        ::-webkit-scrollbar {
            width: 6px;
        }

        /* Track */
        ::-webkit-scrollbar-track {
            background: #21274D;
        }

        /* Handle */
        ::-webkit-scrollbar-thumb {
            background: #888;
        }

        /* Handle on hover */
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

    }

</style>
