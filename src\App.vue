<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<style lang="less">
#app{
  width:100%;
  height:100%;
}

.el-table__expand-icon{
  transition: none !important;
  .el-icon-arrow-right {
    transform: rotate(90deg);
  }
  .el-icon-arrow-right:before {
    content: "\002B";
  }
}

.el-table__expand-icon--expanded{
  .el-icon-arrow-right:before {
    content: "\2212";
  }
}
.el-pagination .el-pagination__total,
.el-pagination .el-pagination__jump {
  color: white;
}
  .main_right {
    .el-form-item__label {
      color: white;
    }
    .el-checkbox__label {
      color: white;
    }
    .el-form-item__label {
      display: flex;
    }
    .el-form-item__content {
      left: -40px;
    }
  }

  .main_left {
    .el-tree-node__content {
      // border-bottom: 1px dotted rgb(204, 204, 204);
      height: 40px !important;
      line-height: 30px !important;
      background-color: #21274D !important;
      color: rgba(255, 255, 255, 0.65);
    }
    .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
      background-color: #595d7a !important;
      color: #C7C7C7 100%;
    }
  }
/*.el-tree-node>.el-tree-node__children {*/
  /*overflow: auto !important;*/
/*}*/
</style>
