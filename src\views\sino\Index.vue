<template>
    <div class="container" v-loading="loading">
        <div class="system-box" style="display: none;">
            <div class="sys-container" v-for="(item,index) in systems" :class="{containerActive: index == hoverIndex}"
                 @mouseenter="hoverIndex = index" @mouseleave="hoverIndex = null" :key="item.id"
                 @click="sysClick(index,item.url)">
                <div class="icon-box" :class="index == activeIndex? 'active':''">
                    <img :src="item.icon" alt="">
                </div>
                <i class='el-icon-circle-check' id="clicked" v-if="index == activeIndex"></i>
                <p class="refer">{{item.referred}}</p>
                <p class="name">{{item.name}}</p>
            </div>
        </div>
    </div>
</template>

<script >
    import {getAppList, getWechatUserInfo} from '@/api/app/app'

    export default {
        name: 'index',
        data() {
            return {
                activeIndex: null,
                hoverIndex: null,
                systems: [],
                loginName: "",
                userName: "",
                info: "",
                loading: false
            }
        },
        computed: {
            // ...mapState({
            //   userName:state => state.userName
            // })
        },
        mounted() {
            setTimeout(() => {

                this.userName = window.localStorage.getItem('Susername')
                console.log(this.userName);
                this.getSystem(this.userName);
                this.getUserInfo();
            }, 3000);
            setTimeout(() => {
                if (this.userName == null) {
                    this.userName = window.localStorage.getItem('Susername')
                    console.log(this.userName);
                    this.getSystem(this.userName);
                    this.getUserInfo();
                }
            }, 4000);

            setTimeout(() => {
                if (this.userName == null) {
                    this.userName = window.localStorage.getItem('Susername')
                    console.log(this.userName);
                    this.getSystem(this.userName);
                    this.getUserInfo();
                }
            }, 5000);
            setTimeout(() => {
                if (this.userName == null) {
                    this.userName = window.localStorage.getItem('Susername')
                    console.log(this.userName);
                    this.getSystem(this.userName);
                    this.getUserInfo();
                }
            }, 6000);
            setTimeout(() => {
                if (this.userName == null) {
                    this.userName = window.localStorage.getItem('Susername')
                    console.log(this.userName);
                    this.getSystem(this.userName);
                    this.getUserInfo();
                }
            }, 7000);
            setTimeout(() => {
                if (this.userName == null) {
                    this.userName = window.localStorage.getItem('Susername')
                    console.log(this.userName);
                    this.getSystem(this.userName);
                    this.getUserInfo();
                }
            }, 8000);
            setTimeout(() => {
                if (this.userName == null) {
                    this.userName = window.localStorage.getItem('Susername')
                    console.log(this.userName);
                    this.getSystem(this.userName);
                    this.getUserInfo();
                }
            }, 9000);

            //window.open("http://172.16.4.63:8088/signOn?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsaWNlbnNlIjoibWFkZSBieSBudGRoIiwidXNlcl9uYW1lIjoiYWRtaW4iLCJzY29wZSI6WyJzZXJ2ZXIiXSwiZXhwIjoxNTc1NTUxMzgwLCJ1c2VySWQiOiJhZG1pbiIsImF1dGhvcml0aWVzIjpbIuezu-e7n-euoeeQhuWRmCIsIlJPTEVfVVNFUiJdLCJqdGkiOiI1Zjg5OGZlOS04NTkzLTQ4ZTEtOTE1Ny1hOTgzNTQzNmE5NjYiLCJjbGllbnRfaWQiOiJvYXV0aCJ9.Gpog1rFdWczjkzKTMDB4JNixNBOWz_wRirsojFC7bI0")
        },
        methods: {
            getUserInfo() {
                getWechatUserInfo(this.userName).then((res) => {
                    console.log(res)
                    let obj = res;
                    obj.flag = "reim";
                    this.loginName = res.data.userId;
                    window.localStorage.removeItem("userInfo");
                    window.localStorage.setItem("userInfo", JSON.stringify(obj));
                    this.info = window.localStorage.getItem('userInfo')
                })
            },
            sysClick(index, url) {
                this.getUserInfo();
                this.activeIndex = index;
                var reg = new RegExp("(^| )access_token=([^;]*)(;|$)");
                url = url.replace(/\${username}/, this.userName);
                url = url.replace(/\${token}/, document.cookie.match(reg)[2]);
                this.loading = true;
                if (this.loginName == this.userName) {
                    this.loading = false;
                    url = url.replace(/\${userinfo}/, this.info);
                    window.open(url)
                }

                // url += `?username=${this.userName}&dbflag=0&token=${}`;

            },
            getSystem(user) {
                getAppList(user).then(res => {
                    this.systems = res.data
                })
            },
            getUserName() {
                window.localStorage.getItem('Susername')
            }
        },
    }
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
    .container {
        .system-box {
            margin-top: 70px;
            margin-left: 70px;

            .sys-container {
                width: 260px;
                border-radius: 10px;
                background-color: rgba(59, 72, 176, 0.45);
                float: left;
                margin-right: 30px;
                margin-top: 30px;
                text-align: center;
                padding-top: 30px;
                padding-bottom: 37px;
                cursor: pointer;
                position: relative;

                #clicked {
                    font-size: 24px;
                    position: absolute;
                    top: 6px;
                    right: 10px;
                    color: #fff;
                }

                .icon-box {
                    width: 60px;
                    height: 60px;
                    overflow: hidden;
                    border-radius: 50%;
                    margin: 0 auto;
                    background-color: #fff;
                    display: flex;
                    flex-direction: row;
                    justify-content: center;
                    align-items: center;

                    img {
                        width: 60px;
                        height: 60px;
                    }
                }

                .refer {
                    color: #fff;
                    font-size: 26px;
                    margin-top: 28px;
                }

                .name {
                    margin-top: 19px;
                    color: #fff;
                    font-size: 24px;
                }
            }
        }
    }

    .sys-box::after {
        content: "";
        display: block;
        clear: both;
    }

    .active {
        background-color: #f3a21f !important;
    }

    .active i {
        color: #fff !important;
    }

    .containerActive {
        box-shadow: 0 0 10px #aabdff;
    }
</style>
