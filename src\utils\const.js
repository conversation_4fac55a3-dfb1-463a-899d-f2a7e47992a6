export const PHONE_REG = /^(13[0-9]|14[01456879]|15[0-3,5-9]|16[2567]|17[0-8]|18[0-9]|19[0-3,5-9])\d{8}$/;

export const ntdhccUrl='http://erp.ntdhcc.com:8088/flow/procinst/view';

//测试
// export const eossUrl='http://192.168.175.18:8806/loginToken';//待办--eoss跳转路径

// export const pmsUrl='http://192.168.175.21:8082/userLoginToken';//待办--pms跳转路径

// export const ntdhUrl='http://172.16.4.17:8080/loginToken';//待办--ntdh跳转路径

// export const quickUrl='http://192.168.177.31:8080/approval';//待办--快捷审批跳转路径

export const quitRmsUrl='https://scrm.sino-bridge.com:8098/oa/quik-reim2/';//待办--快捷报销跳转路径

//正式
// export const eossUrl='https://eoss.sino-bridge.com/loginToken';//待办--eoss跳转路径

// export const pmsUrl='https://pms.sino-bridge.com/userLoginToken';//待办--pms跳转路径

export const ntdhUrl='http://erp.ntdhcc.com:8088/loginToken';//待办--ntdh跳转路径

export const quickUrl='https://nwechat.sino-bridge.com:8866/approval';//待办--快捷审批跳转路径

// export const quitRmsUrl='https://nwechat.sino-bridge.com:8866/role';//待办--快捷报销跳转路径
