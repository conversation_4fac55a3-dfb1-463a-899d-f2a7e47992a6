import request from "@/utils/request";
import queryString from "query-string";
export const listOrganizations = () => {
  return new Promise((resolve) => {
    request.get("organization/listOrganizations").then((res) => {
      resolve(res);
    });
  });
};
//员工管理
export const getAllStaff = (qs) => {
  return new Promise((resolve) => {
    request
      .get("/staff/getAllStaff?" + queryString.stringify(qs))
      .then((res) => {
        resolve(res);
      });
  });
};
//获取通讯录
export const selectAllDept = (qs) => {
  return new Promise((resolve) => {
    request
      .get("/organization/selectAllDept?" + queryString.stringify(qs))
      .then((res) => {
        resolve(res);
      });
  });
};
export const getAllStaffEmp = (qs) => {
  return new Promise((resolve) => {
    request
      .get("/staff/getAllStaffEmp?" + queryString.stringify(qs))
      .then((res) => {
        resolve(res);
      });
  });
};
export const getList = () => {
  return new Promise((resolve) => {
    request.get("/position/getList?orgId=1").then((res) => {
      resolve(res);
    });
  });
};
//新增员工
export const addStaff = (param) => {
  return new Promise((resolve) => {
    request.post("/staff/addStaff", param).then((res) => {
      resolve(res);
    });
  });
};
//修改员工
export const updateStaff = (param) => {
  return new Promise((resolve) => {
    request.post("/staff/updateStaff", param).then((res) => {
      resolve(res);
    });
  });
};
//修改员工
export const getPositionData = () => {
  return new Promise((resolve) => {
    request.get("/dept/selectDept").then((res) => {
      resolve(res);
    });
  });
};
