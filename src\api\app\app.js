import request from '@/utils/request'
import qs from 'qs'

//获取应用列表
export const getAppList = (username)=>{
    return new Promise((resolve)=>{
        let param = {
            username : username
        }
        let headerParams = {
            headers : {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        request.post("/appPerm/getApp",qs.stringify(param),headerParams).then(res=>{
            resolve(res)
        })
    })
}


//获取用户信息
export const getWechatUserInfo = (username) => {
    return new Promise((resolve)=>{
        let param = {
            userId : username
        }
        let headerParams = {
            headers : {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        request.post("/pendingProcTask/getWechatUserInfo",qs.stringify(param),headerParams).then(res=>{
            resolve(res)
        })
    })
}
//获取用户数据
export const getCountUser = (param) => {
    return new Promise((resolve) => {
      request.get("/countUser?pageSize=" +param.pageSize +"&pageNo=" +param.currentPage).then((res) => {
        resolve(res);
      });
    });
};

//获取新闻信息
export const getNews = (type) => {
    return new Promise((resolve) => {
        request.get("/articleTopic/getAllPublishNews?topicType="+ type).then((res) => {
            resolve(res);
        });
    });
};

//新增浏览记录
export const addviewLog = (param) => {
    console.log(param,100)
    return new Promise((resolve) => {
        let headerParams = {
            headers : {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        request.post("/articleTopic/addViewLog" ,qs.stringify(param),headerParams).then((res) => {
            resolve(res);
        });
    });
};

//根据月份获取日志数据
export const getlistWorkDaily = (param) => {
    return new Promise((resolve) => {
        let headerParams = {
            headers : {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        request.post("/workDiary/listWorkDailyForMonth", qs.stringify(param),headerParams).then((res) => {
            resolve(res);
        });
    });
};

//根据日期获取日志数据
export const getlistWorkDailyForDay = (param) => {
    return new Promise((resolve) => {
        let headerParams = {
            headers : {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        request.post("/workDiary/listWorkDailyForDay", qs.stringify(param),headerParams).then((res) => {
            resolve(res);
        });
    });
};

//参看日志详情
export const getWorkDailyInfo = (param) => {
    return new Promise((resolve) => {
        let headerParams = {
            headers : {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        request.post("/workDiary/getWorkDailyInfo", qs.stringify(param),headerParams).then((res) => {
            resolve(res);
        });
    });
};
//获取应用列表
export const getDomainList = (username)=>{
    return new Promise((resolve)=>{
        let param = {
            username : username
        }
        let headerParams = {
            headers : {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        request.post("/workDiary/getDomainvalueList",qs.stringify(param),headerParams).then(res=>{
            resolve(res)
        })
    })
}
//获取应用列表
export const getProList = (username)=>{
    return new Promise((resolve)=>{
        let param = {
            username : username
        }
        let headerParams = {
            headers : {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        request.post("/workDiary/getProjectList",qs.stringify(param),headerParams).then(res=>{
            resolve(res)
        })
    })
}
export const createTopic = (param) => {
    return new Promise((resolve) => {
        let str = `/workDiary/saveWorkDaily`;
        request.post(str, param).then((res) => {
            resolve(res);
        });
    });
};
//新增时上一次日志送达人回显
export const lastDeliverer = (userName) => {
    return new Promise((resolve) => {
        request.get("/workDiary/getDeliverer?username=" + userName).then((res) => {
            resolve(res);
        });
    });
};
// 保存日志
export const saveWorkDaily = (param) => {
    return new Promise((resolve) => {
        request.post("/workDiary/saveWorkDaily", param).then((res) => {
            resolve(res);
        });
    });
};
// 编辑日志
export const editWorkDaily = (param) => {
    return new Promise((resolve) => {
        request.post("/workDiary/updateWorkDaily", param).then((res) => {
            resolve(res);
        });
    });
};
// 删除日志
export const delWorkDaily = (param) => {
    return new Promise((resolve) => {
        let headerParams = {
            headers : {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        request.post("/workDiary/deleteWorkDaily",qs.stringify(param),headerParams).then((res) => {
            resolve(res);
        });
    });
};
//是否第一次登录
export const isFirstLogin = (username,articleTopicId) => {
    return new Promise((resolve)=>{
        let param = {
            userName : username,
            articleTopicId:articleTopicId
        }
        let headerParams = {
            headers : {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        request.post("/firstLogin",qs.stringify(param),headerParams).then(res=>{
            resolve(res)
        })
    })
};
// export const addViewLog = (param) => {
//     return new Promise((resolve)=>{
//         let headerParams = {
//             headers : {
//                 'Content-Type': 'application/x-www-form-urlencoded'
//             }
//         }
//         request.post("/addViewLog",qs.stringify(param),headerParams).then(res=>{
//             resolve(res)
//         })
//     })
// };
export const getPopUpArticle = (username) => {
    let param = {
        userName : username
    }
    return new Promise((resolve) => {
        request.get("/articleTopic/getPopUpArticle?userName=" + param.userName).then((res) => {
            resolve(res);
        });
    });
};
