* {
    margin: 0;
    padding: 0;
}

.cartoon{
    position: absolute;
    width: 60%;
    height: 100vh;
    /*background-color: #fffdef;*/
}
.diamond {
    opacity: 0;
    position: absolute;
    width: 53px;
    height: 70px;
    background: url('../images/trends/diamond.png') top no-repeat;
}

.dia1 {
    top: 85%;
    left: 8%;
    animation: dia-an1 2.5s infinite;
    animation-delay: 0s;
}
.dia2 {
    top: 75%;
    left: 15%;
    animation: dia-an2 2.5s infinite;
    animation-delay: 0.6s;
}
.dia3 {
    top: 82%;
    left: 23%;
    animation: dia-an3 2.5s infinite;
    animation-delay: 1s;
}
@keyframes dia-an1 {
    0% {
        top: 85%;
    }
    40% {
        top: 85%;
        opacity: 1;
    }
    100% {
        top: 88%;
        opacity: 0;
    }
}
@keyframes dia-an2 {
    0% {
        top: 75%;
    }
    40% {
        top: 75%;
        opacity: 1;
    }
    100% {
        top: 78%;
        opacity: 0;
    }
}
@keyframes dia-an3 {
    0% {
        top: 82%;
    }
    40% {
        top: 82%;
        opacity: 1;
    }
    100% {
        top: 85%;
        opacity: 0;
    }
}
.diamond .light {
    position: absolute;
    /*bottom: 26px;*/
    bottom: 65%;
    /*left: 78px;*/
    width: 54px;
    height: 176px;
    background: url('../images/trends/dia-light.png') no-repeat;
    z-index: 1;
}
.screen-box{
    position: absolute;
    width: 250px;
    height: 250px;
    top: 25%;
    left: 14%;

}
.screen{
    position: relative;
    /*top: 25%;*/
    /*left: 14%;*/
    width: 224px;
    height: 231px;
    background: url('../images/trends/screen.png') top no-repeat;
}
.screen-light{
    position: relative;
    width: 222px;
    height: 231px;
    top: -245px;
    left: -15px;
    background: url('../images/trends/screen-light.png') top no-repeat;
}
.terrace{
    position: absolute;
    width: 526px;
    height: 100vh;
    /*top: 25%;*/
    left: 23%;
}
.terrace-base {
    position: absolute;
    top: 53%;
    /*left: 148px;*/
    width: 526px;
    height: 257px;
    background: url('../images/trends/center-base.png')no-repeat;
    /*background-size:88%;*/
    /*z-index: 1;*/
}
.terrace-base-light{
    position: relative;
    width: 528px;
    height: 605px;
    top: 45%;
    left: 2px;
    background: url('../images/trends/center-base-light.png') no-repeat;
}

.terrace-stage{
    position: absolute;
    width: 400px;
    height: 615px;
    bottom: 22%;
    left: 12%;
    background: url('../images/trends/center-stage.png') no-repeat;
}
.build-box{
    position: absolute;
    width: 350px;
    height: 100vh;
    left: 65%;
}
.build-base {
    position: absolute;
    top: 45%;
    /*left: 148px;*/
    width: 280px;
    height: 250px;
    background: url('../images/trends/build-base.png') bottom no-repeat;
    /*background-size:88%;*/
    /*z-index: 1;*/
}

.build-light {
    position: absolute;
    width: 265px;
    /*height: 0;*/
    height: 580px;
    left: 3%;
    bottom: 31%;
    background: url('../images/trends/builds.png') bottom no-repeat;
    /*animation: turnhigh 3s infinite;*/
}
@keyframes turnhigh
{
    0% {opacity: 0;
        transform: translateY(-100px);}
    100% {opacity: 1;
        transform: translateY(0);}
}

.star{
    position: absolute;
    top: 43.8%;
    left:2%;
}
.star1{
    position: absolute;
    top: 28.5%;
    right:3.6%;
}
.star2{
    position: absolute;
    top: 39.8%;
    right:7.9%;
}
.star3{
    position: absolute;
    top: 55.5%;
    right:8%;
}
.star4{
    position: absolute;
    top: 75.5%;
    right:10.5%;
}
.star5{
    position: absolute;
    top: 14.1%;
    right:10.4%;
}
.logo-title{
    /* 本例12个文字(加标点符号)；有多少个文字，width就是多少个em */
    width:11em;
    /* 加上两个动画，一个是打字动画，使用steps让字一个一个的出现，
     注意step和字数保持一致，光标动画也是同理，*/
    animation: typingWords 1s steps(9);
    /* 要设置不允许换行，且溢出隐藏 */
    white-space: nowrap;
    overflow: hidden;
    /* 使用右边框作为打印的指针光标 */
    /*border-right: 1px solid #fff;*/
}

@keyframes typingWords {
    0% {
        width: 0;
    }
}

@keyframes cursor {
    50% {
        border-color: transparent;
    }
}
.shake {
    transform-origin: center bottom;
    animation: shake 2s infinite;
}
@-webkit-keyframes updown{
    25%{
        -webkit-transform:translateY(-10px);
        transform:translateY(-10px);
    }
    50%{
        -webkit-transform:translateY(0);
        transform:translateY(0);
    }
    75%{
        -webkit-transform:translateY(-10px);
        transform:translateY(-10px);
    }
}
@keyframes shake {
    0% {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
    10%,
    20% {
        -webkit-transform: scale3d(0.95, 0.95, 0.95) rotate3d(0, 0, 1, -1deg);
        transform: scale3d(0.95, 0.95, 0.95) rotate3d(0, 0, 1, -1deg);
    }
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: scale3d(1.05, 1.05, 1.05) rotate3d(0, 0, 1, 1deg);
        transform: scale3d(1.05, 1.05, 1.05) rotate3d(0, 0, 1, 1deg);
    }
    40%,
    60%,
    80% {
        -webkit-transform: scale3d(1.05, 1.05, 1.05) rotate3d(0, 0, 1, -1deg);
        transform: scale3d(1.05, 1.05, 1.05) rotate3d(0, 0, 1, -1deg);
    }
    100% {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }
}
.fadexk-k1 {
    animation: fadexk 1s infinite;
    animation-delay: 0s;
    animation-direction: alternate;
}
.fadexk-k2 {
    animation: fadexk 1s infinite;
    animation-delay: 0.4s;
    animation-direction: alternate;
}
.fadexk-k3 {
    animation: fadexk 1s infinite;
    animation-delay: 0.8s;
    animation-direction: alternate;
}
.box-gs {
    position: relative;
    /*bottom: 5%;*/
    left: 40%;
    display: inline-block;
    width: 10%;
    height: 250px;
}
.box-gs img {
    position: absolute;
    bottom: 0;
    opacity: 0;
}

.box-gs .bimgan0 {
    left: -18px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 0s;
}
.box-gs .bimgan1 {
    left: -12px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 0.8s;
}
.box-gs .bimgan2 {
    left: 16px;
    animation: fadeup-yunxq 3s infinite;
    animation-delay: 0.4s;
}
.box-gs .bimgan3 {
    left: 24px;
    animation: fadeup-yunxq 2s infinite;
    animation-delay: 1.0s;
}
.box-gs .bimgan4 {
    left: 36px;
    animation: fadeup-yunxq 3s infinite;
    animation-delay: 0.2s;
}
.box-gs .bimgan5 {
    left: 39px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 1.8s;
}
.box-gs .bimgan6 {
    left: 84px;
    animation: fadeup-yunxq 2s infinite;
    animation-delay: 0.6s;
}
.box-gs .bimgan7 {
    left: 58px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 1.4s;
}
.box-gs .bimgan8 {
    left: 64px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 2.6s;
}
.box-gs .bimgan9 {
    left: -6px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 0.2s;
}
.box-gs .bimgan10 {
    left: 60px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 2.2s;
}
.box-gs .bimgan11 {
    left: 28px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 1.6s;
}
.box-gs .bimgan12 {
    left: 12px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 1s;
}
.box-gs .bimgan13 {
    left: 12px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 0.6s;
}
.box-gs .bimgan14 {
    left: 6px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 1.9s;
}
.box-gs .bimgan15 {
    left: -12px;
    animation: fadeup-yunxq 2.5s infinite;
    animation-delay: 1.1s;
}
@keyframes fadeup-yunxq {
    0% {
        transform: translateY(0);
        opacity: 0;
    }
    40% {
        opacity: 1;
    }
    60% {
        opacity: 1;
    }
    100% {
        transform: translateY(180px);
        opacity: 0;
    }
}
@keyframes fadexk {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}/*星星发光*/
.starflick {
    -webkit-animation: starFlick 1s ease-out infinite;
    animation: starFlick 1s ease-out infinite;
    -webkit-animation-direction: alternate;
    animation-direction: alternate;
}
.starflick1 {
    -webkit-animation: starFlick 0.5s ease-out infinite;
    animation: starFlick 0.5s ease-out infinite;
    -webkit-animation-direction: alternate;
    animation-direction: alternate;
}
.starflick2 {
    -webkit-animation: starFlick 0.8s ease-out infinite;
    animation: starFlick 0.8s ease-out infinite;
    -webkit-animation-direction: alternate;
    animation-direction: alternate;
}
.starflick3 {
    -webkit-animation: starFlick 1.1s ease-out infinite;
    animation: starFlick 1.1s ease-out infinite;
    -webkit-animation-direction: alternate;
    animation-direction: alternate;
}
.starflick4 {
    -webkit-animation: starFlick 1.2s ease-out infinite;
    animation: starFlick 1.2s ease-out infinite;
    -webkit-animation-direction: alternate;
    animation-direction: alternate;
}
@keyframes starFlick {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}
.g-anim1 {
    animation: fadexk 1s infinite;
    animation-delay: 0s;
    animation-direction: alternate;
}
.g-anim2 {
    animation: fadexk 1s infinite;
    animation-delay: 0.2s;
    animation-direction: alternate;
}
.g-anim3 {
    animation: fadexk 1s infinite;
    animation-delay: 0.4s;
    animation-direction: alternate;
}

@media screen and (min-width: 600px) and (max-width: 1400px) {
    .diamond ,.diamond .light ,.screen ,.screen-light ,.terrace-base, .terrace-base-light ,.terrace-stage ,.build-base,.build,.build-light{
        background-size: 68%;
    }
    .diamond .light {
        bottom: -18px;
        left: 8px;
    }
    .terrace-base-light{
        top: 30%;
    }
    .terrace-stage{
        bottom: -30%;
        left: 7.5%;
    }
    .box-gs {
        bottom: 20%;
        left: 25%;
    }
    .build-base{
        top:32%;
        left: -8%;
    }
    .build-light {
        left: -5.2%;
        bottom: 27.5%;
    }
}
@media screen and (min-width: 1401px) and (max-width: 1500px) {
    .diamond ,.diamond .light ,.screen ,.screen-light ,.terrace-base, .terrace-base-light ,.terrace-stage ,.build-base,.build,.build-light{
        background-size: 75%;
    }
    .diamond .light {
        bottom: -15px;
        left: 6px;
    }
    .terrace-base-light{
        top: 35%;
    }
    .terrace-stage{
        bottom: -20%;
        left: 8%;
    }
    .box-gs {
        bottom: 15%;
        left: 28%;
    }
    .build-area{
        top:32%;
        left: 12%;
    }
    .build2 {
        bottom: 16%;
        left: 12%;
    }
    .build3 {
        bottom: 8%;
        left: 25%;
    }
    .build-base{
        top:36%;
        left: -6%;
    }
    .build-light {
        left: -3.4%;
        bottom: 28%;
    }
}
@media screen and (min-width: 1501px) and (max-width: 1600px) {
    .diamond ,.diamond .light ,.screen ,.screen-light ,.terrace-base, .terrace-base-light ,.terrace-stage ,.build-base,.build,.build-light{
        background-size: 83%;
    }
    .diamond .light {
        bottom: 25%;
        left: 4px;
    }
    .terrace-base-light{
        top: 38%;
    }
    .terrace-stage{
        bottom: -10%;
        left: 10%;
    }
    .box-gs {
        bottom: 10%;
        left: 32%;
    }
    .build-base{
        top:40%;
        left: -4%;
    }
    .build-light {
        left: -1%;
        bottom: 28%;
    }
}
@media screen and (min-width: 1601px) and (max-width: 1800px) {
    .diamond ,.diamond .light ,.screen ,.screen-light ,.terrace-base, .terrace-base-light ,.terrace-stage ,.build-base,.build,.build-light{
        background-size: 90%;
    }
    .diamond .light {
        bottom: 50%;
        left: 2px;
    }
    .terrace-base-light{
        top: 40%;
    }
    .terrace-stage{
        bottom: 0%;
        left: 10%;
    }
    .box-gs {
        bottom: 5%;
        left: 35%;
    }
    .build-base{
        top:42%;
        left: -2%;
    }
    .build-light {
        left: 1%;
        bottom: 28.5%;
    }
}
@media screen and (min-width: 1801px) and (max-width: 1860px) {
    .build-light {
        left: 3%;
        bottom: 31%;
    }
}
@media screen and (min-width: 1861px) and (max-width: 1920px) {
    .build-light {
        left: 3%;
        bottom: 30%;
    }
}
