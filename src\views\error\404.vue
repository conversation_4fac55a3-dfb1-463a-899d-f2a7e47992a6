<template>
    <div class="bg-img">
        <div class="container">
            <div>
                <img src="../../assets/images/404.png" alt=""  width="60%"/>
            </div>
            <el-button type="primary" class="returnBtn" @click="returnIndex">返回首页</el-button>
        </div>
    </div>
</template>

<script>
export default {
    name: "404",
    data() {
        return {};
    },
    methods: {
        returnIndex() {
            this.$router.push("/");
        },
    },
};
</script>

<style scoped>
    *{
        padding: 0;
        margin: 0;
    }
    .bg-img{
        width: 100%;
        height: 100vh;
        background: url("../../assets/images/error.png") no-repeat;
        background-size: 100% 100%;
        overflow: hidden;
        display: flex;
        align-items: center;
        text-align:center;
    }
    .container {
        margin:0 auto;
        text-align: center;
    }
/*.container {*/
/*    display: flex;*/
/*    flex-direction: row;*/
/*    justify-content: center;*/
/*    align-items: center;*/
/*}*/

.returnBtn {
    width: 100px;
    height: 40px;
    line-height: 40px;
    margin-top: 30px;
    /*position: absolute;*/
    /*left: 50%;*/
    /*margin-left: -49px;*/
}
</style>
