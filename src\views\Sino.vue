<template>
  <div>
    <div
      v-show="loading"
      v-loading.fullscreen.lock="loading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgb(33,39,77, 1)"
    ></div>
    <div v-show="!loading">
      <el-container class="bgStyle">
        <el-header>
          <div style="display: flex; align-items: center">
            <img
              src="../assets/images/logo1.png"
              class=""
              height="54"
              alt
              style="padding-top: 2px"
            />
            <div style="padding-left: 8px; color: #c9cce5">
              中国企联办公自动化平台
            </div>
          </div>

          <div class="person-message">
            <span
              v-if="sfzState"
              style="cursor: pointer; display: flex; align-items: center"
              @click="editSfz"
            >
              <img
                src="../assets/images/sfztixing.jpg"
                :class="{ turnBig: isshake }"
                style="
                  width: 20px !important;
                  height: 20px !important;
                  margin-right: 8px;
                "
                alt
              />
              <!--              <i :class="{'turnBig':isshake}" class="iconfont icon-gongzuotai-xiaoxitixing" style=" margin-right: 10px;color: #4094F3"></i>-->
            </span>
            <span style="display: flex; align-items: center" @click="editPass"
              >欢迎，{{ LoginName }}</span
            >
            <span style="margin: 0 15px">|</span>
            <span
              style="display: flex; align-items: center; cursor: pointer"
              @click="openTip"
              >查看个人资料
              <img
                src="../assets/images/qiehuan.png"
                style="width: 25px; height: 25px; margin-left: 2px"
                alt="qiehuan"
              />
            </span>
            <span style="margin: 0 15px">|</span>
            <span style="display: flex; align-items: center; cursor: pointer">
              <a
                :href="soUrl"
                target="_blank"
                style="
                  text-decoration: none;
                  display: flex;
                  align-items: center;
                "
              >
                <span style="color: #c9cce5">帮助</span>
                <img
                  src="../assets/images/help.png"
                  style="width: 18px; height: 18px; margin-left: 2px"
                  alt="help"
              /></a>
            </span>
            <span style="margin: 0 15px">|</span>
            <span style="display: flex; align-items: center"
              >当前在线人数：{{ userNum }}</span
            >
            <span style="margin: 0 15px">|</span>
            <span
              style="cursor: pointer; display: flex; align-items: center"
              :class="isActive ? 'active' : ''"
              @mouseenter="isActive = true"
              @mouseleave="isActive = false"
              @click="quitSystem"
            >
              <img
                src="../assets/images/quit.png"
                style="width: 20px; height: 18px"
                alt="quit"
                v-if="!isActive"
              />
              <img
                src="../assets/images/quitclick.png"
                style="width: 20px; height: 18px"
                alt="quit"
                v-if="isActive"
              />
              退出登录</span
            >
          </div>
          <div class="iframeDiv">
            <iframe
              id="rmsId"
              :src="quitRmsUrl"
              title="rms"
              name="rms"
            ></iframe>
          </div>
          <div class="iframeDiv">
            <iframe
              id="ramsId"
              :src="quitRamsUrl"
              title="rams"
              name="rams"
            ></iframe>
          </div>
        </el-header>
        <el-container>
          <el-aside width="200px">
            <el-menu
              :default-active="activePath"
              background-color="#21274d"
              text-color="#c4bcff"
              active-text-color="#0080BE"
              :router="true"
              class="el-menu-vertical-demo"
              :collapse-transition="true"
              unique-opened
              @select="handleSelect"
            >
              <!-- 遍历循环导航菜单 -->
              <div v-for="item in navMenu" :key="item.menuId">
                <el-menu-item
                  v-if="item.children == null"
                  :key="item.menuId"
                  :index="item.menuUrl + ''"
                  style="height: 56px; line-height: 52px"
                >
                  <i
                    :class="item.icon"
                    v-if="!item.children"
                    class="iconStyle"
                  ></i>
                  <span slot="title" v-if="!item.children" class="titleStyle">{{
                    item.menuName
                  }}</span>
                </el-menu-item>
                <el-submenu
                  v-if="item.children != null"
                  :key="item.menuId"
                  :index="item.menuUrl + ''"
                >
                  <template slot="title" v-if="item.children">
                    <i :class="item.icon" class="iconStyle"></i>
                    <span class="titleStyle">
                      {{ item.menuName }}
                    </span>
                    <el-badge
                      :value="storeDialogs"
                      class="item"
                      v-if="item.menuName === '文件传阅' && storeDialogs > 0"
                    >
                    </el-badge>
                    <el-badge
                      :value="storeNoticeNum"
                      class="item"
                      v-if="item.menuName === '通知公示' && storeNoticeNum > 0"
                    >
                    </el-badge>
                  </template>
                  <el-menu-item-group v-if="item.children">
                    <el-menu-item
                      v-for="i in item.children"
                      :key="i.menuId"
                      :index="i.menuUrl + ''"
                    >
                      <span class="titleStyle">{{ i.menuName }}</span>
                    </el-menu-item>
                  </el-menu-item-group>
                </el-submenu>
              </div>
            </el-menu>
          </el-aside>
          <el-main>
            <transition enter-active-class="animated fadeInLeft">
              <router-view />
            </transition>
          </el-main>
        </el-container>
      </el-container>

      <div class="switchStyle">
        <el-dialog
          title="修改密码"
          class="addpup"
          :visible.sync="editPassVisible"
          width="30%"
          :before-close="closeEditPass"
          :close-on-click-modal="false"
        >
          <div class="input-box">
            <el-form
              ref="editPassForm"
              :model="editPassForm"
              :rules="editPassRules"
              label-position="right"
              label-width="80px"
            >
              <el-form-item prop="oldpassword" label="原密码">
                <el-input
                  v-model="editPassForm.oldpassword"
                  placeholder="请输入原密码"
                  show-password
                ></el-input>
              </el-form-item>
              <el-form-item prop="newpassword" label="新密码">
                <el-input
                  v-model.trim="editPassForm.newpassword"
                  placeholder="请输入新密码"
                  show-password
                ></el-input>
              </el-form-item>
              <el-form-item prop="surepassword" label="确认密码">
                <el-input
                  v-model.trim="editPassForm.surepassword"
                  placeholder="请输入确认密码"
                  show-password
                ></el-input>
              </el-form-item>
              <!-- <div style="color: red;text-align: center;font-size: 12px;"
                v-if="editPassForm.surepassword != editPassForm.newpassword">请确认新密码两次输入一致！</div> -->
            </el-form>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button
              type="primary"
              @click="editPassSubmit()"
              :disabled="editPassDis"
              >修改</el-button
            >
            <el-button
              v-if="!isEditPassExpiration"
              @click="editPassVisible = false"
              class="cancelbtn"
              >取消</el-button
            >
          </span>
        </el-dialog>
      </div>
      <div class="tipStyle">
        <el-dialog
          title="详细信息"
          :visible.sync="dialogTipVisible"
          width="74%"
          class="addpup"
          :before-close="handleTipClose"
        >
          <div class="input-box">
            <el-descriptions
              class="margin-top"
              title=""
              :column="3"
              size="medium"
              border
            >
              <el-descriptions-item>
                <template slot="label"> 姓名 </template>
                {{ ruleForm.staffname }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 性别 </template>
                {{ xingbie }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 部门 </template>
                {{ ruleForm.deptName }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 职位 </template>
                {{ ruleForm.positionName }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 籍贯 </template>
                {{ ruleForm.registeredResidence }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 民族 </template>
                {{ ruleForm.nation }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 身份证 </template>
                {{ ruleForm.identitynumber }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 出生日期 </template>
                {{ ruleForm.birthdate }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 年龄 </template>
                {{ ruleForm.age }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 参加工作时间 </template>
                {{ ruleForm.firstworktime }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label"> 工龄 </template>
                {{ ruleForm.workYears }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 政治面貌 </template>
                {{ zhengzhimianmao }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 学历 </template>
                {{ xueli }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 专业 </template>
                {{ zhuanye }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 毕业时间 </template>
                {{ ruleForm.graduationTime }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 行政职务 </template>
                {{ ruleForm.adminDuties }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 任职时间 </template>
                {{ ruleForm.timeOfAppointment }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 职级 </template>
                {{ zhiji }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 职级时间 </template>
                {{ ruleForm.timeOfRank }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label"> 专业技术职称 </template>
                {{ ruleForm.profTitle }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 级别 </template>
                {{ ruleForm.profRank }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 取得时间 </template>
                {{ ruleForm.timeOfProfRank }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label"> 来会时间 </template>
                {{ ruleForm.timeToSociety }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label"> 人员类别 </template>
                {{ renyuanleibie }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 是否结婚 </template>
                {{ ruleForm.marry }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 结婚时间 </template>
                {{ ruleForm.marryTime }}
              </el-descriptions-item>

              <el-descriptions-item>
                <template slot="label"> 备注 </template>
                {{ ruleForm.remark }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogTipVisible = false" class="cancelbtn"
              >关闭</el-button
            >
          </span>
        </el-dialog>
      </div>

      <!--      身份证-->
      <div class="switchStyle">
        <el-dialog
          title="身份证到期提醒"
          :close-on-press-escape="false"
          class="addP"
          :visible.sync="dialogVisibleSfz"
          width="800px"
          :before-close="handleClose1"
          :close-on-click-modal="false"
        >
          <div class="input-box">
            <el-form
              ref="sfzForm"
              :model="sfzForm"
              :rules="sfzRules"
              label-position="right"
              label-width="170px"
            >
              <div class="titleSfz" style="color: red">
                您的身份证{{ isSfzState ? "已过期" : "即将到期" }}({{
                  identityValiditySfz
                }})，请及时更新，以免影响系统正常使用!
              </div>
              <div class="contentSfz">
                <div class="boxSfz" style="margin-left: 25px">
                  <div
                    style="
                      height: 10%;
                      line-height: 100%;
                      font-size: 15px;
                      color: #919294;
                      margin-top: 5px;
                    "
                  >
                    请上传身份证正面(人像面)
                  </div>
                  <div style="height: 60%" v-if="!imageUrl">
                    <img
                      src="../assets/images/sfzFront.jpg"
                      style="width: 90%; height: 90%"
                      alt="提醒"
                    />
                  </div>
                  <div class="image-container" v-if="imageUrl">
                    <div v-if="loadingsfz1" class="loading-spinner"></div>
                    <img :src="imageUrl" style="width: 100%; height: 100%" />
                  </div>

                  <el-upload
                    class="upload-demo"
                    ref="upload"
                    :action="actionurl"
                    style="line-height: 16%; height: 16%"
                    :show-file-list="false"
                    v-model="sfzForm.card1"
                    :on-preview="handlePreview"
                    :on-remove="handleRemove"
                    :on-success="handleASuccess"
                    :before-upload="beforeUpload"
                    :file-list="fileList"
                    accept=".jpg,.jpeg,.png"
                  >
                    <el-button
                      class="btnStyle"
                      type="primary"
                      style="
                        margin-top: 10px;
                        background-color: #0e61c9;
                        width: 80px;
                      "
                      size="medium"
                      >上传
                    </el-button>
                  </el-upload>
                </div>
                <div class="boxSfz">
                  <div
                    style="
                      height: 10%;
                      line-height: 100%;
                      font-size: 15px;
                      color: #919294;
                      margin-top: 5px;
                    "
                  >
                    请上传身份证反面(国徽面)
                  </div>
                  <div style="height: 60%" v-if="!imageUrl1">
                    <img
                      src="../assets/images/sfzBack.jpg"
                      style="width: 90%; height: 90%"
                      alt="提醒"
                    />
                  </div>
                  <div class="image-container" v-if="imageUrl1">
                    <div v-if="loadingsfz2" class="loading-spinner"></div>
                    <img :src="imageUrl1" style="width: 100%; height: 100%" />
                  </div>
                  <el-upload
                    class="upload-demo"
                    ref="upload"
                    :action="actionurl"
                    style="line-height: 16%; height: 16%"
                    :show-file-list="false"
                    v-model="sfzForm.card2"
                    :on-preview="handlePreview1"
                    :on-remove="handleRemove1"
                    :on-success="handleASuccess1"
                    :before-upload="beforeUpload"
                    :file-list="fileList1"
                    accept=".jpg,.jpeg,.png"
                  >
                    <el-button
                      class="btnStyle"
                      type="primary"
                      style="
                        margin-top: 10px;
                        background-color: #0e61c9;
                        width: 80px;
                      "
                      size="medium"
                      >上传
                    </el-button>
                  </el-upload>
                </div>
              </div>
              <div class="titleSfz1" style="color: #ec7676">
                只能上传jpg/png/jpeg格式的身份证正/反面图片
              </div>
              <el-form-item label="证件到期时间" prop="identityValidity">
                <el-date-picker
                  :picker-options="pickerOptions"
                  style="width: 375px"
                  value-format="yyyy-MM-dd"
                  type="date"
                  v-model="sfzForm.identityValidity"
                  placeholder="请选择证件到期时间"
                ></el-date-picker>
              </el-form-item>
            </el-form>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button
              class="OKButton"
              type="primary"
              @click="onSfzSubmit('sfzForm')"
              >确 定</el-button
            >
            <el-button @click="handleClose1" class="CancButton"
              >取 消</el-button
            >
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script>
import { clearCookie, getCookie, setCookie } from "@/utils/cookie";
import { oauthMenu } from "@/api/menu/menu";
import {
  // thirdPartyLogin,
  loginOut,
  getUserNum,
  saveRemFlag,
  getHelpPDF,
  thirdLogin,
  updPassword,
  saveIdentityCard,
  getIdentityCardState,
  getUserInfoDetail,
} from "@/api/sino/sino";
import { getAttachPassByUserId } from "@/api/sino/newRelease";
import { sm2 } from "sm-crypto";
import { randomLenNum } from "@/utils/randomNum";
import { switchLogin, createToken, switchLoginCheck } from "@/api/login/login";
import { mapActions ,mapState } from "vuex";
import { quitRmsUrl, quickUrl } from "../utils/const";
// import Tesseract from 'tesseract.js';
import { passwordExpiration } from "@/api/login/login";
export default {
  name: "sino",
  data() {
    const validatePass2 = (rule, value, callback) => {
      // console.log(value, this.editPassForm.newpassword)
      if (value !== this.editPassForm.newpassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      xingbie: "",
      isActive: false,
      inputValue: "",
      allUrl: "",
      activePath: "",
      userName: "",
      navMenu: [],
      badgeValue: 3,
      LoginName: "",
      dialogVisible: false,
      randomStr: "",
      ruleForm: {
        staffname: "",
        age: "",
        sex: null,
        nation: "",
        birthdate: "",
        workYears: "",
        adminDuties: "",
        timeOfAppointment: "",
        profTitle: "",
        profRank: "",
        timeOfProfRank: "",
        timeToSociety: "",
        graduationTime: "",
      },
      zhengzhimianmao: "",
      xueli: "",
      zhuanye: "",
      zhiji: "",
      renyuanleibie: "",
      form: {
        name: "",
        pwd: "",
        valid: "",
      },
      rules: {
        name: [{ required: true, message: "请输入用户名", trigger: "blur" }],
        pwd: [{ required: true, message: "请输入密码", trigger: "blur" }],
        valid: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      loading: false,
      status: false, //点击切换账号
      quitRmsUrl: "", //报销路径
      quitRamsUrl: "", //审批路径
      subDis: false, //切换账号验证
      soUrl: "",
      userNum: "", //当前在线人数
      dialogTipVisible: false, //切换账号提示
      tipChecked: false, //下次不再提示
      editPassVisible: false, //修改密码
      editPassDis: false, //修改密码验证
      editPassForm: {
        newpassword: "",
        oldpassword: "",
        surepassword: "",
      },
      editPassRules: {
        oldpassword: [
          { required: true, message: "请输入原密码", trigger: "blur" },
        ],
        newpassword: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          { min: 8, message: "密码长度不能小于8位", trigger: "blur" },
          {
            pattern:
              /^(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,./]).{8,}$/,
            message: "密码必须包含数字、大小写字母和特殊符号",
            trigger: "blur",
          },
        ],
        surepassword: [
          { required: true, message: "请输入确认密码", trigger: "blur" },
          { validator: validatePass2, trigger: "blur" },
        ],
      },
      dialogVisibleSfz: false,
      sfzForm: {
        card1: "",
        card2: "",
        identityValidity: "",
      },
      sfzRules: {
        identityValidity: [
          { required: true, message: "请选择证件到期时间", trigger: "change" },
        ],
      },
  
      actionurl: "/ssoapi/sysAttach/insertSysAttach",
      fileList: [],
      imageUrl: "",
      fileList1: [],
      isEditPassExpiration: false,
      imageUrl1: "",
      recognizedDate: "",
      sfzState: false,
      isshake: true,
      loadingsfz1: false,
      loadingsfz2: false,
      identityValiditySfz: "",
      isSfz: false,
      isSfzState: false,
      pickerOptions: {
        disabledDate(time) {
          // 这里time是一个Date对象，表示当前鼠标悬停的日期
          // 返回true表示禁用该日期，返回false表示可用
          const currentDate = new Date();
          // 设置日期选择的上限为当前日期
          return time.getTime() < currentDate.getTime() - 8.64e7;
        },
      },
    };
  },
  computed: {
    ...mapState({
      storeDialogs: state => state.dialogs,
      storeNoticeNum: state => state.noticeNum,
    })
  },
  created() {
    var sUrl = window.location.href;
    let Url1 = sUrl.split("?")[0];
    let Url2 = sUrl.split("#")[1];
    this.allUrl = Url1 + "#" + Url2;
    //处于切换账号状态，刷新页面时，不自动调用此方法
    let switchStatus =
      window.localStorage.getItem("status") == "false" ||
      window.localStorage.getItem("status") == false;
    let scanLogin =
      window.localStorage.getItem("scanLogin") == "true" ||
      window.localStorage.getItem("scanLogin") == true;

    var userName = window.localStorage.getItem("Susername");
    this.loading = true;
    //
    if (switchStatus && scanLogin) {
      alert('sino1')
      this.thirdPartyLogin();
    } else {
      console.log("sino----created", userName);
      this.loginApp(userName);
    }
  },
  mounted() {
    // alert('sino2')
    this.activePath = this.$route.path;
    var userName = window.localStorage.getItem("Susername");
    this.userName = userName;

    passwordExpiration(userName).then((res) => {
      if (res.code == 200) {
        if (res.data) {
          this.$confirm("您的密码已过期，请修改密码？", "提示", {
            confirmButtonText: "确定",
            type: "warning",
            showCancelButton: false,
          }).then(() => {
            this.isEditPassExpiration = true;
            this.editPassVisible = true;
          });
        }
      }
    });

    var LoginName = window.localStorage.getItem("LoginName");
    this.LoginName = LoginName;
    if (userName) {
      oauthMenu(userName).then((res) => {
        this.navMenu = res.data;
      });
      //请把一进页面就要调的接口写在这个里面，排排队
      this.getHelpPDF();
      this.randomNum();
      this.getUserNum();
      this.getSfzState();
      this.getUnreadFiles();
    }

    getUserInfoDetail(userName).then((res) => {
      this.ruleForm = res.data;
      this.zhuanye = res.data.major;
      this.zhengzhimianmao = res.data.politicaloutlookValue;
      this.xueli = res.data.academicDegree;
      this.renyuanleibie = res.data.personType;
      this.zhiji = res.data.positionRank;
      this.xingbie = res.data.sexValue;
    });

  },
  
 
 
  methods: {
    ...mapActions({
      editUsername: "EDIT_USER",
      SET_DIALOGS: "SET_DIALOGS",
      SET_NOTICE_NUM: "SET_NOTICE_NUM",
    }),
    // 获取url？后边的参数
    GetQueryString(name) {
      var after = window.location.search;
      // 由于#原因search可能为"，所以地址参数先通过search取值如果取不到就通过hash来取
      after = after.substr(1) || window.location.hash.split("?")[1];
      if (after) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = after.match(reg);
        if (r != null) {
          return decodeURIComponent(r[2]);
        } else {
          return null;
        }
      }
    },
    getHelpPDF() {
      //获取帮助文档路径
      getHelpPDF().then((res) => {
        if (res.code == 200) {
          this.soUrl =
            window.location.href.indexOf("//sso") > -1
              ? `${res.data.path}`
              : `/sso${res.data.path}`;
        }
      });
    },
    //扫码登陆获取code
    thirdPartyLogin() {
      var isLogin = window.localStorage.getItem("isLogin");
      // var code = this.GetQueryString('code');
      var code = sessionStorage.getItem("weChatcode");

      console.log('thirdPartyLogin - isLogin:', isLogin, 'code:', code);

      var status = isLogin == "false" ? false : isLogin ? true : false;
      // console.log(code, !status)
      if (code && !status) {
        console.log('Start WeChat login, code:', code);
        window.localStorage.removeItem("Susername");
        window.localStorage.removeItem("LoginName");

        // 显示加载状态
        this.loading = true;

        thirdLogin({ code: code }).then((res) => {
          console.log('WeChat login response:', res);
          this.loading = false;

          if (res.code == 200) {
            window.localStorage.setItem("Susername", res.data.userId);
            var userName = window.localStorage.getItem("Susername");
            this.$store.commit("edit_username", res.data.userId);
            this.userName = res.data.userId;

            if (res.map && res.map.data && res.map.data.name) {
              window.localStorage.setItem("LoginName", res.map.data.name);
            } else {
              window.localStorage.setItem("LoginName", res.data.userId);
            }
            this.LoginName = window.localStorage.getItem("LoginName");
            // this.$store.commit("edit_access_token",res.data.access_token)
            // this.$store.commit("edit_refresh_token",res.data.refresh_token)
            setCookie("access_token", res.data.access_token, 0.125);
            setCookie("refresh_token", res.data.refresh_token, 0.125);
            // this.$message({
            //   showClose: true,
            //   message: "登录成功",
            //   type: "success",
            // });
            //单点登录
            // if (window.sessionStorage.getItem("clientid")) {
            //   let url = window.sessionStorage.getItem("clientid");
            //   createToken({
            //     userName: this.userName,
            //     clientid: url,
            //   }).then((result) => {
            //     let openUrl = `${result.data.url}?token=${result.data.token}`
            //     console.log(openUrl);
            //     window.open(openUrl);
            //     window.sessionStorage.removeItem("clientid");
            //   });
            // }
            window.localStorage.setItem("ifFirstLogin", true); //第一次登陆
            // this.$router.push("/sino/index");
            setTimeout(() => {
              window.location.reload();
            }, 500);
            window.localStorage.setItem("isLogin", true);
            // this.editUsername(res.data.userId)
            oauthMenu(userName).then((result) => {
              if (result.data.code == 200) {
                this.navMenu = result.data.data;
              }
            });
          } else {
            console.error('WeChat login failed:', res);
            this.$message({
              message: res.message || "企业微信登录失败，请重新扫码",
              type: "error",
            });
            this.loading = false;
            // 清除code，避免重复尝试
            sessionStorage.removeItem("weChatcode");
            this.$router &&
              this.$router.push("/scanSign").catch((error) => {
                console.log(error);
              });
            window.localStorage.setItem("scanLogin", false); //扫码登录
          }
        }).catch((error) => {
          console.error('WeChat login request failed:', error);
          this.loading = false;
          this.$message({
            message: "网络错误，请检查网络连接后重试",
            type: "error",
          });
          // 清除code，避免重复尝试
          sessionStorage.removeItem("weChatcode");
          this.$router &&
            this.$router.push("/scanSign").catch((err) => {
              console.log(err);
            });
          window.localStorage.setItem("scanLogin", false);
        });
      } else if (isLogin) {
        var userName = window.localStorage.getItem("Susername");
        this.LoginName = window.localStorage.getItem("LoginName");
        this.$store.commit("edit_username", userName);
        this.userName = userName;
        this.loginApp(userName);
        //用户名为空则自动登出
        if (this.LoginName == null) {
          this.$router &&
            this.$router.push("/scanSign").catch((error) => {
              console.log(error);
            });
          window.localStorage.setItem("scanLogin", false); //扫码登录
          // if (window.localStorage.getItem("scanLogin") == true || window.localStorage.getItem("scanLogin") == 'true') {
          //   this.$router && this.$router.push("/scanSign").catch(error => { console.log(error) });
          //   window.localStorage.setItem("scanLogin", false);//扫码登录
          // } else {
          //   this.$router && this.$router.push("/accLogin");
          //   window.localStorage.setItem("scanLogin", false);//非扫码登录
          // }
        }
        oauthMenu(userName).then((res) => {
          if (res.data.code == 200) {
            this.navMenu = res.data.data;
          }
        });
      }
    },
    loginApp(userName) {
      // alert('sino3')
      console.log(87654);

      let ifFirstLogin = window.localStorage.getItem("ifFirstLogin"); //非第一次登陆
      if (window.sessionStorage.getItem("clientid")) {
        let url = window.sessionStorage.getItem("clientid");
        let identity = "";
        if (url.indexOf("identity") > -1) {
          identity = url.split("identity=")[1];
          url = url.substring(url, url.indexOf("&"));
        }
        let obj = {
          userName: userName,
          clientid: url,
          identity: identity,
        };
        console.log(456789, obj);

        // alert('sino4')
        createToken({
          userName: userName,
          clientid: url,
          identity: identity,
        })
          .then((result) => {
            console.log("result---", result);
            let openUrl = `${result.data.url}?token=${result.data.token}`;
            console.log(9999, openUrl);
            if (ifFirstLogin == true || ifFirstLogin == "true") {
              //只有第一次扫码进入是跳转

              //window.open(openUrl);
              //window.location.href = openUrl;
              this.refresh();
            } else {
              // alert(9990)
              window.location.href = openUrl;
            }
            window.sessionStorage.removeItem("clientid");
            window.localStorage.setItem("ifFirstLogin", false); //非第一次登陆
          })
          .catch((error) => {
            console.log(error);
          });
      } else {
        this.refresh();
      }
    },
    refresh() {
      this.loading = false;
      window.location.replace(this.allUrl);
      if (this.$route.path == "/sino/index") {
        this.activePath = "/sino/index";
        this.$router.push("/sino/index");
      } else {
        this.activePath = this.$route.path;
        this.$router.push(this.$route.path);
      }
    },
    //系统推出
    quitSystem() {
      let buleiToken = getCookie("saber3-access-token");
      // console.log(buleiToken,11)
      // return
      loginOut(this.userName, buleiToken).then((res) => {
        window.localStorage.setItem("status", false); //不处于切换账号的状态
        window.sessionStorage.removeItem("clientid");
        if (res.code == 200) {
          this.quitDone(res);
          window.localStorage.setItem("isLogin", false);
          window.localStorage.removeItem("userInfo"); //清除人员信息
          window.localStorage.removeItem("Susername");
          window.localStorage.removeItem("LoginName");
          clearCookie("saber3-access-token");
        }
        //定时器
        setTimeout(
          function () {
            //定时器
            this.$router &&
              this.$router.push("/scanSign").catch((error) => {
                console.log(error);
              });
            window.localStorage.setItem("scanLogin", false); //扫码登录
          }.bind(this),
          600
        ); //关闭该窗口
        // if (window.localStorage.getItem("scanLogin") == true || window.localStorage.getItem("scanLogin") == 'true') {
        //   setTimeout(function () {  //定时器
        //     this.$router && this.$router.push("/scanSign").catch(error => { console.log(error) });
        //     window.localStorage.setItem("scanLogin", false);//扫码登录
        //   }.bind(this), 600);  //关闭该窗口
        // } else {
        //   setTimeout(function () {  //定时器
        //     this.$router && this.$router.push("/accLogin");
        //     window.localStorage.setItem("scanLogin", false);//非扫码登录
        //   }.bind(this), 600);  //关闭该窗口
        // }
        //清除Cookie
        clearCookie("access_token");
        clearCookie("refresh_token");
      });
    },
    closeWindow(key, url) {
      let windowName = [];
      windowName[key] = window.open(
        url,
        "_blank",
        "width=1,height=1,left=10000,top=10000,"
      );
      windowName[key] && windowName[key].focus();
      setTimeout(function () {
        windowName[key] && windowName[key].close();
      }, 300);
    },
    //单点登录登出
    quitDone(res) {
      for (var key in res.data) {
        if (key == "RAMS" || key == "RAMS_TEST") {
          this.quitRamsUrl = quickUrl;
          // var ramsWindow = window.open(res.data[key], '_blank', 'width=1,height=1,left=10000,top=10000,');
          // ramsWindow&&ramsWindow.focus();
          // setTimeout(function () {  //定时器
          //   ramsWindow&&ramsWindow.close();
          // }, 300);  //关闭该窗口
          setTimeout(function () {
            var ramsframe = document.getElementById("ramsId");
            ramsframe &&
              ramsframe.contentWindow &&
              ramsframe.contentWindow.postMessage("删除数据", quickUrl);
            window.localStorage.removeItem("userInfo");
          }, 300);
        } else if (key == "RMS" || key == "RMS_TEST") {
          this.quitRmsUrl = quitRmsUrl;
          // var rmsWindow = window.open(res.data[key], '_blank', 'width=1,height=1,left=20000,top=20000,');
          // rmsWindow&&rmsWindow.focus();
          // setTimeout(function () {  //定时器
          //   rmsWindow&&rmsWindow.close();
          // }, 300);  //关闭该窗口
          setTimeout(function () {
            //定时器
            var rmsiframe = document.getElementById("rmsId");
            rmsiframe &&
              rmsiframe.contentWindow &&
              rmsiframe.contentWindow.postMessage("删除数据", quitRmsUrl);
            window.localStorage.removeItem("userInfo");
          }, 300); //传送数据
        }
        // else if (key == 'TSM_TEST' || key == 'TSM') {
        //   console.log("key++++++++", res.data[key])
        //   var tsmWindow = window.open(res.data[key], '_blank', 'width=1,height=1,left=20000,top=20000,');
        //   tsmWindow&&tsmWindow.focus();
        //   setTimeout(function () {  //定时器
        //     tsmWindow&&tsmWindow.close();
        //   }, 300);  //关闭该窗口

        // }
        // else if (key == 'EHR_TEST' || key == 'EHR') {
        //   console.log("key++++++++", res.data[key])
        //   var ehrWindow = window.open(res.data[key], '_blank', 'width=1,height=1,left=20000,top=20000,');
        //   ehrWindow&&ehrWindow.focus();
        //   setTimeout(function () {  //定时器
        //     ehrWindow&&ehrWindow.close();
        //   }, 300);  //关闭该窗口
        // }
        // else if (key == 'CRM_TEST' || key == 'CRM') {
        //   console.log("key++++++++", res.data[key])
        //   var crmWindow = window.open(res.data[key], '_blank', 'width=1,height=1,left=20000,top=20000,');
        //   crmWindow&&crmWindow.focus();
        //   setTimeout(function () {  //定时器
        //     crmWindow&&crmWindow.close();
        //   }, 300);  //关闭该窗口
        // }
        // else if (key == 'PMS_TEST' || key == 'PMS') {
        //   console.log("key++++++++", res.data[key])
        //   var pmsWindow = window.open(res.data[key], '_blank', 'width=1,height=1,left=20000,top=20000,');
        //   pmsWindow&&pmsWindow.focus();
        //   setTimeout(function () {  //定时器
        //     pmsWindow&&pmsWindow.close();
        //   }, 300);  //关闭该窗口
        // }
        // else if (key == 'EOSS_TEST' || key == 'EOSS') {
        //   console.log("key++++++++", res.data[key])
        //   var eossWindow = window.open(res.data[key], '_blank', 'width=1,height=1,left=20000,top=20000,');
        //   eossWindow&&eossWindow.focus();
        //   setTimeout(function () {  //定时器
        //     eossWindow&&eossWindow.close();
        //   }, 300);  //关闭该窗口
        // }
        // else if (key == 'NTDH_TEST' || key == 'NTDH') {
        //   console.log("key++++++++", res.data[key])
        //   var ntdhWindow = window.open(res.data[key], '_blank', 'width=1,height=1,left=20000,top=20000,');
        //   ntdhWindow&&ntdhWindow.focus();
        //   setTimeout(function () {  //定时器
        //     ntdhWindow&&ntdhWindow.close();
        //   }, 300);  //关闭该窗口
        // }
        // else {
        //   this.closeWindow(key, res.data[key])
        // }
      }
      this.$store.commit("edit_username", "");
    },
    handleSelect(key) {
      this.activePath = key;
    },
    jumpToDo() {
      this.$router.push("/sino/waiting");
      this.activePath = "/sino/waiting";
    },
    //身份证到期提醒
    editSfz() {
      this.dialogVisibleSfz = true;
      this.isshake = false;
    },
    getSfzState() {
      getIdentityCardState()
        .then((res) => {
          this.identityValiditySfz = res.data.identityValidity;
          const currentDate = new Date();
          const expiryDate = new Date(this.identityValiditySfz);
          if (expiryDate < currentDate) {
            this.isSfzState = true;
          } else {
            this.isSfzState = false;
          }
          if (res.data.guoqi && !res.data.xitong) {
            this.sfzState = true;
            this.isSfz = false;
            this.editSfz();
          } else if (res.data.guoqi && res.data.xitong) {
            this.sfzState = true;
            this.isSfz = true;
            this.editSfz();
          } else {
            this.isSfz = false;
            this.dialogVisibleSfz = false;
            this.sfzState = false;
          }
        })
        .catch((err) => {
          this.sfzState = false;
        });
    },
    // recognizeDate() {
    //   this.loadingsfz1 = true
    //   // 假设你已经处理好图像，并且图像中仅包含身份证背面的日期
    //   Tesseract.recognize(
    //       this.imageUrl,
    //       'chi_sim',
    //       {
    //         logger: m => console.log(m) // 用于调试
    //       }
    //   ).then(({ data: { text } }) => {
    //     console.log('2222',text)
    //     if (this.isFrontSideRecognized(text)) {
    //       console.log('身份证正面已识别');
    //       this.loadingsfz1 = false
    //       // // 处理识别到的背面信息
    //       // Tesseract.recognize(
    //       //     this.imageUrl,
    //       //     'eng',
    //       //     {
    //       //       logger: m => console.log(m) // 用于调试
    //       //     }
    //       // ).then((res) => {
    //       //   console.log('000',res.data.text)
    //       //   let resultText = res.data.text
    //       //   const datePattern = /\d{4}\.\d{2}\.\d{2}-\d{4}\.\d{2}\.\d{2}/g;
    //       //   // 查找所有匹配的日期
    //       //   const matches = resultText.match(datePattern);
    //       //   if (matches) {
    //       //     matches.forEach(match => {
    //       //       this.recognizedDate = match
    //       //     });
    //       //   } else {
    //       //     console.log("未找到日期");
    //       //   }
    //       //   console.log('111',this.recognizedDate)
    //       // })
    //     } else {
    //       this.loadingsfz1 = false
    //       this.$message.info('请上传正确的身份证正面图片')
    //       this.imageUrl = ''
    //       this.fileList = []
    //     }
    //
    //   }).catch(error => {
    //     console.error('识别出错:', error);
    //   });
    // },
    // recognizeDate1() {
    //   this.loadingsfz2 = true
    //   // 假设你已经处理好图像，并且图像中仅包含身份证背面的日期
    //   Tesseract.recognize(
    //       this.imageUrl1,
    //       'chi_sim',
    //       {
    //         logger: m => console.log(m) // 用于调试
    //       }
    //   ).then(({ data: { text } }) => {
    //     if (this.isBackSideRecognized(text)) {
    //       this.loadingsfz2 = false
    //       console.log('身份证反面已识别');
    //       // // 处理识别到的背面信息
    //       // Tesseract.recognize(
    //       //     this.imageUrl,
    //       //     'eng',
    //       //     {
    //       //       logger: m => console.log(m) // 用于调试
    //       //     }
    //       // ).then((res) => {
    //       //   console.log('000',res.data.text)
    //       //   let resultText = res.data.text
    //       //   const datePattern = /\d{4}\.\d{2}\.\d{2}-\d{4}\.\d{2}\.\d{2}/g;
    //       //   // 查找所有匹配的日期
    //       //   const matches = resultText.match(datePattern);
    //       //   if (matches) {
    //       //     matches.forEach(match => {
    //       //       this.recognizedDate = match
    //       //     });
    //       //   } else {
    //       //     console.log("未找到日期");
    //       //   }
    //       //   console.log('111',this.recognizedDate)
    //       // })
    //     } else {
    //       this.loadingsfz2 = false
    //       this.$message.info('请上传正确的身份证反面图片')
    //       this.imageUrl1 = ''
    //       this.fileList1 = []
    //     }
    //
    //   }).catch(error => {
    //     console.error('识别出错:', error);
    //   });
    // },
    // isBackSideRecognized(text) {
    //   // 假设背面都包含"公安局"这个关键字
    //   return text.includes('有 效 期 限');
    // },
    // isFrontSideRecognized(text) {
    //   // 假设背面都包含"公安局"这个关键字
    //   return text.includes('公民');
    // },
    // -上传服务器之前逻辑处理
    beforeUpload(file) {
      //上传的文件只能是图片，且大小不能超过10MB
      const isJPGOrPdf =
        file.type === "image/jpeg" ||
        file.type === "image/png" ||
        file.type === "image/jpg";
      const isLt2M = file.size / 1024 / 1024 < 10;
      if (!isJPGOrPdf) {
        this.$message.error("只能上传jpg/png/jpeg格式的图片!");
      }
      // if (!isLt2M) {
      //   this.$message.error('上传图片大小不能超过 10MB!');
      // }
      return isJPGOrPdf;
    }, //成功上传
    handleASuccess(res, file) {
      this.sfzForm.card1 = res.data.attachId;
      this.imageUrl = URL.createObjectURL(file.raw);
      this.fileList.push({
        name: file.name,
        url: res.data,
      });
      // this.recognizeDate()
    },
    //上传
    handlePreview(file) {
      let URL = window.URL || window.webkitURL;
      if (!!window.ActiveXObject || "ActiveXObject" in window) {
        this.$message.warning("暂不支持预览");
      } else {
        if (file.raw) {
          window.open(URL.createObjectURL(file.raw)); //blob格式地址
        } else {
          window.open(file.url); //blob格式地址
        }
      }
    },
    handleRemove(file) {
      this.sfzForm.card1 = "";
      this.fileList = [];
    },
    handleRemove1(file) {
      this.sfzForm.card2 = "";
      this.fileList1 = [];
    },
    handleASuccess1(res, file) {
      this.sfzForm.card2 = res.data.attachId;
      this.imageUrl1 = URL.createObjectURL(file.raw);
      this.fileList1.push({
        name: file.name,
        url: res.data,
      });
      // this.recognizeDate1()
    },
    //上传
    handlePreview1(file) {
      let URL = window.URL || window.webkitURL;
      if (!!window.ActiveXObject || "ActiveXObject" in window) {
        this.$message.warning("暂不支持预览");
      } else {
        if (file.raw) {
          window.open(URL.createObjectURL(file.raw)); //blob格式地址
        } else {
          window.open(file.url); //blob格式地址
        }
      }
    },
    onSfzSubmit(formname) {
      this.$refs[formname].validate((valid) => {
        if (valid) {
          if (!this.sfzForm.card1 || !this.sfzForm.card2) {
            this.$message.error("请上传身份证正/反面图片！");
            return;
          }
          // if(this.loadingsfz2 || this.loadingsfz1){
          //   this.$message.error('身份证正/反面图片还未上传成功')
          //   return
          // }
          saveIdentityCard(this.sfzForm)
            .then((res) => {
              this.$message.success("操作成功");
              this.isSfz = false;
              this.handleClose1();
              this.sfzState = false;
              this.isshake = false;
            })
            .catch((err) => {
              this.sfzState = true;
              this.$message.error("操作失败");
            });
        } else {
          this.$message.error("请检查必填项");
        }
      });
    },
    editPass() {
      this.editPassDis = false;
      this.editPassVisible = true;
      this.editPassForm = {
        newpassword: "",
        oldpassword: "",
        surepassword: "",
      };
    },
    closeEditPass() {
      if (this.isEditPassExpiration) {
        this.$message({
          message: "请修改登录密码！",
          type: "error",
        });
      } else {
        this.editPassVisible = false;
      }
    },
    encrypt(text) {
      let publicKey =
        "042136fe42541b34a589a158382b5a2f65593c181af2b388f18745818d5a74350387e8bdd8d44828f4b94180cc4b5cd8143da5324ee9ba86a0fd7d75572a1f4e11";

      return "04" + sm2.doEncrypt(text, publicKey);
    },
    editPassSubmit() {
      this.$refs["editPassForm"].validate((valid) => {
        if (
          valid &&
          this.editPassForm.surepassword == this.editPassForm.newpassword
        ) {
          this.editPassDis = true;
          updPassword({
            oldpassword: this.encrypt(this.editPassForm.oldpassword), //密码用户名
            userName: this.userName, //用户名
            newpassword: this.encrypt(this.editPassForm.newpassword),
          }).then((res) => {
            if (res.code == 200) {
              this.editPassDis = false; //切换账号不可点击
              this.editPassVisible = false;
              this.$message({
                message: "修改成功！",
                type: "success",
              });
              this.quitSystem();
            } else {
              this.editPassDis = false;
              this.$message({
                message: res.message,
                type: "error",
              });
            }
          });
        } else {
          this.$message({
            message: "请正确输入密码",
            type: "error",
          });
        }
      });
    },
    openTip() {
      this.dialogTipVisible = true;
    },
    handleTipClose() {
      this.dialogTipVisible = false;
      this.tipChecked = false;
    },
    open() {
      if (this.tipChecked) {
        saveRemFlag({ userName: this.userName });
      }
      this.dialogTipVisible = false;
      this.form = {
        name: "",
        pwd: "",
        valid: "",
      };
      this.dialogVisible = true;
      this.randomNum();
      window.localStorage.setItem("status", true);
    },
    handleClose() {
      this.dialogVisible = false;
    },
    handleClose1() {
      // if(this.isSfz){
      //   this.dialogVisibleSfz = false
      // }else{
      this.dialogVisibleSfz = false;
      this.sfzForm = {
        card1: "",
        card2: "",
        identityValidity: "",
      };
      this.fileList = [];
      this.imageUrl = "";
      this.imageUrl1 = "";
      this.$refs.sfzForm.resetFields();
      this.isshake = true;
      // }
    },
    randomNum() {
      this.randomStr = randomLenNum(4, true);
      this.randomUrl = "ssoapi/code/" + this.randomStr;
    },
    //切换账号
    onSubmitTest(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          switchLoginCheck({
            currentUserName: this.userName, //正在登陆的用户名
            userName: this.form.name, //要切换的的用户名
            password: this.form.name + this.form.pwd,
            code: this.form.valid,
            randomStr: this.randomStr,
            grant_type: "password",
            scope: "server",
            client_id: "oauth",
            client_secret: "oauth",
          }).then((res) => {
            if (res.code == 200) {
              this.onSubmit(formName, res.map.checkCode, res.data);
              this.subDis = true; //切换账号不可点击
            } else {
              this.randomNum();
              this.$message({
                message: res.message,
                type: "error",
              });
            }
          });
        }
      });
    },

    onSubmit(formName, checkCode, userData) {
      let token = getCookie("access_token");
      if (token) {
        let buleiToken = getCookie("saber3-access-token");
        loginOut(this.userName, buleiToken).then((result) => {
          if (result.code == 200) {
            //this.loading = true;
            // clearCookie("access_token");
            // clearCookie("refresh_token");
            this.$refs[formName].validate((valid) => {
              if (valid) {
                switchLogin({
                  checkCode: checkCode,
                }).then((res) => {
                  if (res.code == 200) {
                    this.quitDone(result);
                    window.localStorage.setItem("isLogin", false);
                    window.localStorage.removeItem("userInfo");
                    window.localStorage.removeItem("Susername");
                    window.localStorage.removeItem("LoginName");
                    setCookie("access_token", userData.access_token, 0.125);
                    setCookie("refresh_token", userData.refresh_token, 0.125);
                    window.localStorage.setItem("Susername", userData.userId);
                    if (res.map && res.map.data && res.map.data.name) {
                      window.localStorage.setItem(
                        "LoginName",
                        res.map.data.name
                      );
                    } else {
                      window.localStorage.setItem("LoginName", userData.userId);
                    }
                    this.editUsername(userData.userId);
                    oauthMenu(userData.userId).then((result) => {
                      if (result.data.code == 200) {
                        this.navMenu = result.data.data;
                      }
                    });
                    if (this.$route.path == "/sino/index") {
                      setTimeout(() => {
                        window.location.reload();
                      }, 500);
                    } else {
                      this.$router.replace("/sino/index");
                      this.activePath = "/sino/index";
                      setTimeout(() => {
                        window.location.reload();
                      }, 500);
                    }
                    this.subDis = false; //切换账号可点击
                    this.loading = false;
                    this.dialogVisible = false;
                  } else {
                    this.subDis = false; //切换账号可点击
                    this.loading = false;
                    this.randomNum();
                    this.$message({
                      message: "验证失败，请重新登录",
                      type: "error",
                    });
                  }
                });
              }
            });
          }
        });
      } else {
        this.subDis = false;
        this.$message({
          message: "登录已过期，请重新登陆",
          type: "error",
        });
      }
    },
    getUserNum() {
      //获取当前在线人数
      getUserNum().then((res) => {
        if (res.code == 200) {
          this.userNum = res.data.num;
        }
      });
    },
    getUnreadFiles() {
      let param = {
        title:'',
        pageNum: 1,
        pageSize: 10000,
        userId: window.localStorage.getItem("Susername")
      }
      getAttachPassByUserId({...param,type:'type_1'}).then(res => {
        if (res.code === 200) {
          let len = res.data.subject.filter((item) => item.readFlag == "0");
          // console.log(len,'len')
          this.SET_DIALOGS(len.length);
        }
      });


      getAttachPassByUserId({...param,type:'type_2'}).then(res => {
        if (res.code === 200) {
          let len = res.data.subject.filter((item) => item.readFlag == "0");
          this.SET_NOTICE_NUM(len.length);
        }
      });

    },
  },
  watch: {},
};
</script>
<style lang="less" scoped>
.iframeDiv {
  width: 0%;
  height: 0%;
  overflow: hidden;
}

.el-header {
  // background-color: #3b48b0;
  color: #333;
  height: 60px !important;
  position: relative;
  // box-shadow: 0 3px 5px #23253a;
  border: 1px solid rgba(255, 255, 255, 0.14);
  z-index: 5;
}

.logo {
  position: absolute;
  top: 50%;
  margin-top: -20px;
}

.titleStyle {
  font-size: 16px;
}

.search {
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -165px;
  margin-top: -20px;
}

.el-aside {
  background-color: #21274de6 !important;
  color: #333;
  text-align: center;
  min-height: calc(100vh - 80px);
}

.el-menu {
  border: none;

  .iconStyle {
    vertical-align: middle;
    margin-right: 5px;
    width: 24px;
    text-align: center;
    font-size: 18px;
  }

  i {
    color: #c4bcff;
  }

  .el-menu-item.is-active i {
    color: #0080be;
  }
}

.el-main {
  // background: #2c26a3 url("../assets/images/background/one.jpg") no-repeat 100% 100%;
  // background: #2c26a3 url("../assets/images/indexBg.png") no-repeat 100% 100%;
  color: rgb(228, 209, 209);
  min-height: calc(100vh - 80px);
  padding: 20px 20px 0 20px;

  ::-webkit-scrollbar {
    width: 0px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #21274d;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.asideSty {
  ::-webkit-scrollbar {
    width: 0px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #21274d;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.bgStyle {
  background: url("../assets/images/background/one.jpg");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 100vh;
  min-width: 1280px;
}

.el-container:nth-child(5) .el-aside,
.el-container:nth-child(6) .el-aside {
  line-height: 260px;
}

.el-container:nth-child(7) .el-aside {
  line-height: 320px;
}

.person-message {
  color: #c9cce5;
  font-size: 14px;
  line-height: 24px;
  // width: 590px;
  position: absolute;
  right: 4%;
  top: 50%;
  margin-top: -12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  /deep/ .el-badge__content {
    border: 0px !important;
  }

  /deep/ .el-badge__content.is-fixed {
    right: 0px !important;
  }
}

.turnBig {
  animation: turnBig 0.5s infinite;
}

@keyframes turnBig {
  0%,
  100% {
    transform: scale(1);
    /* 原始大小 */
    -webkit-transform: scale(1);
    /* Safari 和较早版本的 Chrome */
    -moz-transform: scale(1);
    /* Mozilla Firefox */
    -ms-transform: scale(1);
    /* Internet Explorer */
    -o-transform: scale(1);
    /* Opera*/
  }

  25% {
    transform: scale(1.1);
    /* 放大到110% */
    -webkit-transform: scale(1.1);
    /* Safari 和较早版本的 Chrome */
    -moz-transform: scale(1.1);
    /* Mozilla Firefox */
    -ms-transform: scale(1.1);
    /* Internet Explorer */
    -o-transform: scale(1.1);
    /* Opera*/
  }

  50% {
    transform: scale(1.05);
    /* 轻微缩放保持位置 */
    -webkit-transform: scale(1.05);
    /* Safari 和较早版本的 Chrome */
    -moz-transform: scale(1.05);
    /* Mozilla Firefox */
    -ms-transform: scale(1.05);
    /* Internet Explorer */
    -o-transform: scale(1.05);
    /* Opera*/
  }

  75% {
    transform: scale(1.1);
    /* 再次放大到110% */
    -webkit-transform: scale(1.1);
    /* Safari 和较早版本的 Chrome */
    -moz-transform: scale(1.1);
    /* Mozilla Firefox */
    -ms-transform: scale(1.1);
    /* Internet Explorer */
    -o-transform: scale(1.1);
    /* Opera*/
  }
}

.active {
  color: #8f9cff;
}

.el-menu-vertical-demo > li.el-menu-item {
  padding-left: 40px !important;
}

.el-menu-item.is-active {
  background-color: #1b335f !important;
}

.todoStyle :hover {
  color: #8f9cff;
}

.el-menu-vertical-demo > li.el-submenu {
  padding-left: 20px !important;
}

.el-menu-vertical-demo > li.el-submenu {
  overflow: hidden;
}

/deep/ .el-menu-item-group__title {
  padding: 0 !important;
}

.badgeStyle {
  /deep/ .el-badge__content,
  .el-progress.is-exception .el-progress-bar__inner {
    background-color: #9e9e9e !important;
  }
}

@media screen and (min-width: 600px) and (max-width: 1300px) {
  //13寸
  .inputBox {
    width: 55%;
  }

  .titleStyle {
    font-size: 14px;
  }

  .person-message {
    font-size: 12px;
  }
}

@media screen and (min-width: 1301px) and (max-width: 1920px) {
  //15寸
  .inputBox {
    width: 60%;
  }
}

.titleSfz {
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  color: #333;
  text-align: center;
  margin-top: 10px;
}

.titleSfz1 {
  height: 20px;
  line-height: 20px;
  font-size: 14px;
  text-align: left;
  margin-left: 47px;
  margin-bottom: 25px;
}

.contentSfz {
  height: 300px;
  line-height: 300px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;

  .boxSfz {
    width: 48%;
    margin-right: 25px;
    height: 90%;
    border: 1px dashed #ccc;
    border-radius: 5px;
    background-color: #fbfdff;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
}

/* 加载动画样式 */
.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #09f;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 30%;
  left: 40%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 图片容器样式 */
.image-container {
  position: relative;
  width: 90%;
  height: 60%;
  /* 设置容器高度为60% */
}

/* 图片样式 */
.image-container img {
  width: 90%;
  height: 90%;
  display: block;
}
</style>
