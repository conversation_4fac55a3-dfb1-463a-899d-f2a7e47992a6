<template>
    <div class="tab">
        <el-row type="flex" justify="center" class="title">
            <el-col :span="23">
                <div  class="title_header">应用访问管理</div>
            </el-col>
        </el-row>
        <el-row type="flex" justify="center">
            <el-col :span="23">
                <div style="margin-bottom: 15px;">
                    <el-button class="btnStyle" type="primary" style=" background-color: #0E61C9;"
                               size="medium" @click="addmenu">新增
                    </el-button>
                </div>
          
 
                <el-table :data="tableData" class="tableT" ref="multipleTable" style="width: 100%; margin-bottom: 20px"
                    :row-key="getRowKey" height="calc(100vh - 210px)">
                    <el-table-column prop="roleName" label="应用logo" width="100">
                        <template slot-scope="scope">
                            <img :src=" baseImgUrl + scope.row.appLogo" class="appLogo" alt="logo" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="appName" label="应用名称"> </el-table-column>
                    <el-table-column prop="appShortName" label="应用简称">
                    </el-table-column>
                    <el-table-column prop="appDomainUrl" label="域名网址" width="360">
                    </el-table-column>
                    <el-table-column prop="num" label="排序" width="100"></el-table-column>
                    <el-table-column label="操作" width="250">
                        <template slot-scope="scope">
                            <span>
                                <el-button type="success" plain size="mini" @click="() => powerSelect(scope.row)">
                                    访问权限配置
                                </el-button>
                                <el-button type="success" plain size="mini" @click="edit(scope.row)"
                                    icon="el-icon-edit">
                                </el-button>
                                <el-button type="danger" plain size="mini" @click="deletemenu(scope.row)"
                                    icon="el-icon-delete">
                                </el-button>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-col>
        </el-row>

        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="560px" :before-close="addmenuClose"
            class="addP">
            <div style="text-align: left">
                <el-form ref="form" :model="form" label-width="100px" :rules="rules">
                    <el-form-item label="应用名称" prop="appName">
                        <el-input v-model="form.appName" placeholder="请输入应用名称" maxlength="20"
                            show-word-limit></el-input>
                    </el-form-item>
                    <el-form-item label="应用简称" prop="appShortName">
                        <el-input v-model="form.appShortName" placeholder="请输入应用简称" maxlength="20"
                            show-word-limit></el-input>
                    </el-form-item>
                    <el-form-item label="域名网址" prop="appDomainUrl">
                        <el-input v-model="form.appDomainUrl" placeholder="请输入域名网址" maxlength="100"
                            show-word-limit></el-input>
                    </el-form-item>
                    <el-form-item label="排序" prop="num">
                        <el-input-number v-model="form.num" :min="1" :max="9999" placeholder="请输入1-9999之间的数字"
                            style="width:100%"></el-input-number>
                        <div style="font-size: 12px">数字越大，排名越靠前(请输入1-9999之间的数字)</div>
                    </el-form-item>
                    <el-form-item label="应用logo" :required="true">
                        <el-upload class="avatar-uploader" :action="upAppUrl" :show-file-list="false"
                            :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
                            <img v-if="imageUrl" :src="baseImgUrl +'/'+ imageUrl" class="avatarLogo" alt="logo" />
                            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                        <div style="font-size: 12px">建议同比例尺寸的图片</div>
                    </el-form-item>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="OKButton" :disabled="adddis" @click="resumesend('form')">确定</el-button>
                <el-button @click="addmenuClose" class="CancButton" style="margin-left: 10px">取 消</el-button>
            </span>
        </el-dialog>

        <el-dialog title="访问权限配置" append-to-body :visible.sync="innerDialogVisible" :before-close="cancel">
            <div class="left-box">
                <el-menu :default-active="innerActiveIndex" class="el-menu-demo" mode="horizontal"
                    @select="handleSelect">
                    <el-menu-item index="1">组织架构</el-menu-item>
                    <el-menu-item index="2">角色</el-menu-item>
                </el-menu>
                <!-- <el-tree v-if="index == 1" :data="treeData" :props="defaultProps" @node-click="handleNodeClick"
                    check-strictly @check="handleNodeCheck" show-checkbox :default-expand-all="true" :accordion="true"
                    ref="tree" node-key="text" :expand-on-click-node="false"
                    style="height: 300px;overflow-y: scroll;"></el-tree> -->
                    <el-tree v-show="index == 1"  :data="transformedOrgData"  :props="defaultProps"
                        @node-click="handleNodeClick" check-strictly show-checkbox  @check="handleNodeCheck"
                        :check-on-click-node="true"
                         default-expand-all node-key="deptcode"
                        :expand-on-click-node="false"  ref="tree"   
                        style="height: 300px;overflow-y: scroll;">
                        <span class="custom-tree-node" slot-scope="{ node,data }">
                            <div v-if="!data.children" class="team_bg_image"></div>
                            <div v-else class="zuzhiOpen"></div>
                            <span>{{ node.label }}</span>
                        </span>
                    </el-tree>
                <div v-show="index == 2" style="height: 300px;overflow-y: scroll;">

                    <el-checkbox-group v-model="checkList" v-for="item in tags" :key="item.roleId"
                        @change="handleCheckedChange">
                        <el-checkbox :label="item.roleName" class="tag"></el-checkbox>

                    </el-checkbox-group>

                </div>
            </div>
            <div class="right-box">
                <p class="title">已选项</p>
                <div v-for="(item, index) in activeTags" class="tag" :key="index">
                    <div class="tagClass" v-if="item.powerName"><span>{{ item.powerName }}
                            <i class="el-icon-close" style="float:right;text-align:center"
                                @click="clickedItem(item.powerName)"></i></span></div>
                </div>
            </div>
            <div style="clear:both"></div>
            <div class="button-box">
                <el-button class="OKButton" @click="confirm" :disabled="saveStatus">确定</el-button>
                <el-button class="CancButton" @click="cancel">取消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { searchApp, listAppAll, deleteAll, saveApp, updateAppAndPower, getAllStaffByOrgs } from "@/api/sino/application";
import { listOrganizations} from "@/api/sino/organization";
import { listRoles } from "@/api/sino/roleUrl";
export default {
    name: "accessmanage",
    data() {
        //应用地址正则校验
        var checkHttp = (rule, value, callback) => {
            const httpReg = /(http|https):\/\/([\w.]+\/?)\S*/;
            if (!value) {
                return callback(new Error("网址不能为空"));
            }
            setTimeout(() => {
                if (httpReg.test(value)) {
                    callback();
                } else {
                    callback(
                        new Error("这网址不是以http://、https://开头，或者不是网址！")
                    );
                }
            }, 100);
        };
        //域名网址正则校验
        var checkDomainHttp = (rule, value, callback) => {
            const httpReg = /(http|https):\/\/([\w.]+\/?)\S*/;
            if (value&&!httpReg.test(value)) {
                callback(
                    new Error("这网址不是以http://、https://开头，或者不是网址！")
                );
            } else {
                callback();
            }
        };
        return {
            showbasicinfo: true,
            transferData: [],
            value: [],
            renderFunc(h, option) {
                return <span>{option.label}</span>;
            },
            errorInfo: "",
            tableData: [],
            currentPage: 1,
            pagesize: 10,
            totalcount: null,
            dialogVisible: false,
            form: {
                appName: "",
                appShortName: "",
                // appUrl: "",
                num: "",
                appTestUrl:"",//测试网址
                appDomainUrl:"",//域名网址
            },
            imageUrl: "",//图片地址
            postUrl: "",
            rules: {
                appName: [{ required: true, message: "请输入应用名称", trigger: "blur" }],
                appShortName: [
                    { required: true, message: "请输入应用简称", trigger: "blur" },
                ],
                //appUrl: [{ validator: checkHttp, required: true, trigger: "blur" }],
                appTestUrl: [{ validator: checkHttp, required: true, trigger: "blur" }],//测试网址
                appDomainUrl: [{ validator: checkDomainHttp, required: false, trigger: "blur" }],//域名网址
                num: [{ required: true, message: "请输入排序", trigger: "blur" }],
            },
            orgTreeList: [],
            adddis: false,
            dialogTitle: "",
            appPower: [],
            activeId: '',//详情id
            innerDialogVisible: false,
            innerActiveIndex: "1",
            index: 1,
            activeTags: [],
            treeData: [],
            defaultProps: {
                children: "children",
                label: "label"
            },
            tags: [
                {
                    id: 1,
                    name: "总经理",
                    isClick: false
                },
                {
                    id: 2,
                    name: "部门经理",
                    isClick: false
                },
                {
                    id: 3,
                    name: "项目经理",
                    isClick: false
                }
            ],
            baseImgUrl:process.env.VUE_APP_BASE_URL,
            detailItem: {},
            checkList: [],//标签数据
            checkTagData: [],//标签选中数组
            checkNodeData: [],//组织架构选中数组
            otherList: [],
            saveStatus: false,//保存按钮禁止重复点击
            bollen:0,//判断是否重复
            upAppUrl:  process.env.VUE_APP_BASE_UPLOAD + 'appPerm/upAppLog',
        };
    },
    created() {
      
        this.getTreeData();
        this.getOrgList()
    },
    computed: {
        //对获取的组织数据进行封装
        transformedOrgData: function () {
            if (Array.isArray(this.orgTreeList)) {
                this.transformForestData(this.orgTreeList, 'deptname', 'deptcode');
            } else {
                this.transformNodeData(this.orgTreeList, 'deptname', 'deptcode');
            }
            
            return this.orgTreeList;
        },
    },
    methods: {
        getOrgList() {
            this.orgTreeLoading = true;
            // /dept/selectDept
            listOrganizations().then(res => {
                this.orgTreeLoading = false;
                const orgTreeList = res && res.data || [];
                this.orgTreeList = orgTreeList;
            });
        },
         //封装数据,符合组件对数据的要求
         transformForestData(arr, labelName, valueName) {
            for (let node of arr) {
                this.transformNodeData(node, labelName, valueName);
            }
        },
        transformNodeData(node, labelName, valueName) {
            if (node) {
                if (node.children && node.children.length > 0) {
                    this.transformForestData(node.children, labelName, valueName);//封装数据,符合组件对数据的要求
                } else {
                    //console.log(node)
                }
                node.label = node[labelName];
                node.value = node[valueName];
            }
        },
        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;
        },
        getTreeData() {
            searchApp().then((res) => {
                if (res) {
                    res.data.forEach((ele) => {
                        if (ele.appLogo) {
                            ele.appLogo = ele.appLogo.substr(ele.appLogo.indexOf("=") + 1);
                        }
                    });
                    this.tableData = res.data;
                }
            });
        },
        addmenuClose() {
            this.dialogVisible = false;
            this.adddis = false;
            this.$refs.form.resetFields();
            // 清空表单校验，避免再次进来会出现上次校验的记录
            this.$refs['form'].clearValidate();
            this.form = {
                appName: "",
                appShortName: "",
                // appUrl: "",
                num: "",
                appTestUrl:"",//测试网址
                appDomainUrl:"",//域名网址
            };
            this.imageUrl = "";
            this.appPower = [];
            this.activeId = '';
        },

        addmenu() {
            this.dialogVisible = true;
            this.adddis = false;
            this.dialogTitle = "应用新增";
        },

        edit(currentRow) {
            this.dialogTitle = "应用修改";
            this.activeId = currentRow.id
            listAppAll({
                id: currentRow.id,
            }).then((res) => {
                if (res) {
                    this.form.appName = res.data.appName;
                    this.form.appShortName = res.data.appShortName;
                    // this.form.appUrl = res.data.appUrl;
                    this.form.appTestUrl = res.data.appTestUrl;
                    this.form.appDomainUrl = res.data.appDomainUrl;
                    this.imageUrl =
                        res.data.appLogo &&
                        res.data.appLogo.substr(res.data.appLogo.indexOf("=") + 1);//对图标路径的截取
                    this.postUrl = res.data.appLogo;
                    this.appPower = res.data.appPower
                    this.dialogVisible = true;
                    this.adddis = false;
                    this.form.num = res.data.num
                }
            });
        },
        //应用管理的新增或者修改
        resumesend() {
            this.adddis = true;
            this.$refs["form"].validate((valid) => {
                if (valid && this.imageUrl) {
                    if (this.dialogTitle == '应用新增') {
                        saveApp({
                            appName: this.form.appName,
                            appShortName: this.form.appShortName,
                            appLogo: this.postUrl,
                            // appUrl: this.form.appUrl,
                            appTestUrl: this.form.appTestUrl,
                            appDomainUrl: this.form.appDomainUrl,
                            appPower: [],
                            num: this.form.num
                        }).then((res) => {
                            if (res.code == 200) {
                                this.$message.success("添加成功！");
                                this.form = {
                                    appName: "",
                                    appShortName: "",
                                    // appUrl: "",
                                    appTestUrl: "",//测试网址
                                    appDomainUrl: "",//域名网址
                                    num: "",
                                };
                                this.getTreeData();
                                this.dialogVisible = false;
                                this.imageUrl = "";
                                this.$refs.form.resetFields();
                                this.adddis = false;
                            } else {
                                this.adddis = false;
                                this.$message.error(res.message)
                            }
                        });
                    }
                    else {
                        updateAppAndPower({
                            id: this.activeId,
                            appName: this.form.appName,
                            appShortName: this.form.appShortName,
                            appLogo: this.postUrl,
                            // appUrl: this.form.appUrl,
                            appTestUrl: this.form.appTestUrl,
                            appDomainUrl: this.form.appDomainUrl,
                            num: this.form.num,
                            appPower: this.appPower
                        }).then(res => {
                            if (res.code == 200) {
                                this.dialogVisible = false;
                                this.$message.success("修改成功！");
                                this.form = {
                                    appName: "",
                                    appShortName: "",
                                    // appUrl: "",
                                    num: "",
                                    appTestUrl: "",//测试网址
                                    appDomainUrl: "",//域名网址
                                };
                                this.adddis = false;
                                this.imageUrl = "";
                                this.getTreeData();
                                this.$refs.form.resetFields();
                            } else {
                                this.adddis = false;
                                this.$message.error(res.message)
                            }

                        })

                    }
                }
                else {
                    this.adddis = false;
                    this.$message.error("请将信息填写完整!")
                }
            })
        },
        handleAvatarSuccess(res, file) {
            // this.imageUrl = URL.createObjectURL(file.raw);
            this.imageUrl = res.data;
            this.postUrl = "/" + res.data;
        },
        //上传图片以及对图片的限制
        beforeAvatarUpload(file) {
            const isJPG = file.type === "image/png" || file.type === "image/jpeg";
            const isLt2M = file.size / 1024 / 1024 < 2;
            const isMt1K = file.size / 1024 > 1;
            if (!isJPG) {
                this.$message.error("上传的系统logo请选择png、jpg、jpeg等图片格式!");
            }
            if (!isLt2M) {
                this.$message.error("上传系统logo大小不能超过 2MB!");
            }
            if (!isMt1K) {
                this.$message.error("上传系统logo过小，请重新上传!");
            }
            return isJPG && isLt2M && isMt1K;
        },

        deletemenu(currentRow) {
            let that = this;
            this.$confirm(
                "是否确认删除应用名称为" + currentRow.appName + "的数据项?",
                "系统提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }
            )
                .then(() => {
                    deleteAll({ id: currentRow.id }).then((res) => {
                        if (res && res.code == 200) {
                            this.getTreeData();
                            this.$message({
                                message: "删除成功！",
                                type: "success",
                            });
                        } else {
                            that.$message.error({
                                message: res.message || "删除失败",
                            });
                        }
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
        getRowKey(row) {
            return row.roleId;
        },
        //访问权限配置
        powerSelect(row) {

            this.innerDialogVisible = true;
            this.activeId = row.id;
            this.getDetail(row.id);
            // this.getStaffData();
            this.getTags();

        },
        //获取行内信息
        getDetail(id) {
            listAppAll({
                id
            }).then((res) => {
                if (res) {
                    this.detailItem = res.data;
                    this.activeTags = res.data.appPower;
                    let orgName = [];
                    let tagName = [];
                    let otherName = [];
                    res.data.appPower.forEach((ele) => {//根据状态分配数据
                        if (ele.powerType == 'department'||ele.powerType == 'account') {
                            orgName.push(ele.powerValue)
                            this.checkNodeData.push(ele)
                        } else if (ele.powerType == 'role') {
                            this.checkTagData.push(ele)
                            tagName.push(ele.powerName)
                        } else {
                            otherName.push(ele)
                        }

                    })
                    this.$nextTick(() => {
                 
                        this.$refs.tree&&this.$refs.tree.setCheckedKeys(orgName);//组织树的数据勾选
                    });
                    this.checkList = tagName
                    this.otherList = otherName
                }
            });
        },
        getStaffData() {
         
        },
        getTags() {
            listRoles().then(res => {
                if (res) {
                    res.data.forEach(v => {
                        v.isClick = false
                    })
                    this.tags = res.data
                }
            })
        },
        //去重
        removeDuplicate(arr) {
            let len = arr.length
            for (let i = 0; i < len; i++) {
                for (let j = i + 1; j < len; j++) {
                    if (arr[i].powerValue === arr[j].powerValue) {
                        arr.splice(j, 1)
                        len-- // 减少循环次数提高性能
                        j-- // 保证j的值自加后不变
                    }
                }
            }
            return arr
        },

        handleSelect(key, keyPath) {
            this.index = key;
            let orgId = [];
            this.checkNodeData = this.removeDuplicate(this.checkNodeData)//重复组织去重
            console.log(this.checkNodeData,100)
            this.checkNodeData.forEach((ele) => {
                orgId.push(ele.powerValue)
            })
            console.log('handleSelect', this.checkNodeData)
            this.$nextTick(() => {
                this.$refs.tree && this.$refs.tree.setCheckedKeys(orgId);//已有组织数据默认选中
            });


        },

        //已选项的删除,以及对应取消选中
        clickedItem(item) {
            console.log(item, this.checkNodeData)
            this.activeTags.forEach((v, k) => {
                if (v.powerName == item) {
                    this.activeTags.splice(k, 1);
                }
            });
            this.checkList.forEach((ele, k) => {//已选中标签重新勾选
                if (ele == item) {
                    this.checkList.splice(k, 1);

                }
            })
            this.checkTagData.forEach((v, k) => {
                if (v.powerName == item) {
                    this.checkTagData.splice(k, 1);
                }
            });
            //组织重新勾选
            this.checkNodeData.forEach((v, k) => {
                if (v.powerName == item) {
                    this.checkNodeData.splice(k, 1);
                }
            });
            let orgName = []
            this.checkNodeData.forEach((ele) => {
                orgName.push(ele.powerValue)
            })
            this.$nextTick(() => {
                this.$refs.tree && this.$refs.tree.setCheckedKeys(orgName);//设置目前勾选的节点
            });
        },
        forE(arr, res, id) {  //递归调用函数
            if (arr) {
                arr.forEach(v => {
                    if (v.id == id) {
                        JSON.parse(res.data).forEach(item => {
                            item.isClick = false;
                        })
                        this.$set(v, "children", JSON.parse(res.data))
                        return
                    } else {
                        this.forE(v.children, res, id)
                    }
                })
            } else {
                return
            }
        },
        cancel() {//关闭弹框，清空数据
            this.innerDialogVisible = false;
            this.activeTags = []
            this.checkNodeData = [];
            this.checkTagData = [];
            this.$nextTick(() => {
                this.$refs.tree && this.$refs.tree.setCheckedKeys([]);////设置目前勾选的节点为空
            });
            this.checkList = [];
        },
        //配置权限
        confirm() {
            this.saveStatus = true;
            updateAppAndPower({
                ...this.detailItem,
                id: this.activeId,
                appPower: this.activeTags
            }).then(res => {
                if (res.code == 200) {
                    this.saveStatus = false;
                    this.innerDialogVisible = false;
                    this.$message.success("修改成功！")
                    this.getTreeData();

                } else {
                    this.saveStatus = false;
                    this.$message.error(res.message)
                }

            })
        },
        handleNodeClick(data) {
            this.getStaffData(data.id)
        },
        //访问权限配置--组织架构选中获取取消选中数据的处理
        handleNodeCheck(data) {
            // console.log(data, this.checkNodeData,this.checkNodeData.length)
         
            this.bollen = 0;
            this.activeTags = [];
        
            this.checkNodeData.forEach((v, k) => {
                if (v.powerName == data.label) {
                    this.bollen = 1
                    this.checkNodeData.splice(k, 1);
                }
            });
            if (this.bollen == 0) {
                this.checkNodeData = [...this.checkNodeData, { powerName: data.deptname,powerValue:data.deptcode, powerType:'department' }]
            }
            // this.activeTags = [];
            // this.checkNodeData = [];
            // let checkNode = this.$refs.tree.getCheckedNodes();
            // console.log('checkNode',checkNode)
            // checkNode.forEach((ele) => {
            //     this.checkNodeData.push({
            //         powerName: ele.text,
            //         powerValue: ele.orgcode||ele.id,
            //         powerType: ele.type
            //     })
            // })
            this.checkNodeData = Array.from(new Set(this.checkNodeData.map(a => a.powerValue)))  
                    .map(id => {  
                        return this.checkNodeData.find(a => a.powerValue === id);  
                    }); 
            // console.log(this.otherList,'this.otherList')
            let sumData = this.checkTagData.concat(this.otherList)
            // let sumData = this.checkTagData.concat({
            //     powerName:data.deptname,
            //     powerValue:data.deptcode,
            //     powerType:'department'
            // })
            let activeTags1 = this.checkNodeData.concat(sumData)
            this.activeTags = Array.from(new Set(activeTags1.map(a => a.powerValue)))  
                    .map(id => {  
                        return activeTags1.find(a => a.powerValue === id);  
                    }); 
        },
        //标签选中的数据的处理
        handleCheckedChange(val) {
            console.log(val)
            this.activeTags = [];
            this.checkTagData = [];
            val.forEach((ele) => {
                this.checkTagData.push({
                    powerName: ele,
                    powerType: "role",
                    powerValue: ele
                })
            })
            this.checkTagData = Array.from(new Set(this.checkTagData.map(a => a.powerValue)))  
                    .map(id => {  
                        return this.checkTagData.find(a => a.powerValue === id);  
                    }); 
            let sumData = this.checkNodeData.concat(this.otherList)
         
            let activeTags2 = this.checkTagData.concat(sumData)
            this.activeTags = Array.from(new Set(activeTags2.map(a => a.powerValue)))  
                    .map(id => {  
                        return activeTags2.find(a => a.powerValue === id);  
                    }); 
        }
    },
};
</script>

<style scoped lang="less">
.tab {
    margin: -20px 0;
    background-color: #21274D;
    height: calc(100vh - 90px);

    ::-webkit-scrollbar {
        width: 6px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #21274D;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #888;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

}

.title {
    height: 66px;
    line-height: 66px;
    // margin-bottom: 10px;
    // border-bottom: 1px solid #eee;
}

.tree_img {
    display: inline-block;
    width: 16px;
    height: 18px;
    vertical-align: middle;
}

.sure {
    background: none repeat scroll 0 0 #61b0e9;
    border-color: #389ae2;
    color: #fff;
    text-shadow: none;
    text-transform: uppercase;
}

.pager {
    margin-top: 20px;
}

.pager {
    width: 100%;
    overflow: hidden;
    position: relative;
    margin-top: 10px;
    margin-bottom: 10px;
}

.el-pagination {
    float: right;
}

.modal-body {
    position: relative;
    max-height: 370px;
    padding: 15px;
    overflow-y: auto;
}

.basicinfo,
.moreinfo {
    float: left;
    width: 49%;
    height: 30px;
    color: #333;
    margin-bottom: 5px;
    border-bottom: 1px solid #f4f4f4;
    text-align: center;
    line-height: 30px;
    cursor: pointer;
    font-family: "microsoft yahei";
    font-size: 16px;
}

.basicinfo {
    border-right: 1px solid #f4f4f4;
}

.account {
    color: #389ae2 !important;
    font-weight: bold;
}

.appLogo {
    width: 35px;
    height: 35px;
}

.left-box {
    width: 50%;
    float: left;
    max-height: 370px;
    padding: 15px;

    /deep/ .el-tree-node__content {
        height: 32px !important;
    }
}

.tag {
    font-size: 16px;
    line-height: 26px;
    cursor: pointer;
    margin: 5px 0 2px 0px;
}

.right-box {
    margin-left: 20px;
    float: left;
    max-height: 370px;
    padding: 15px;
    overflow-y: auto;
    min-height: 100px;
    padding-left: 30px;
    border-left: 1px solid #949494;
}

.button-box {
    margin-top: 30px;
    margin: 0 auto;
    text-align: right;
}

.viewtree {
    text-align: left;

    ::-webkit-scrollbar {
        width: 6px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #21274D;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #999;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #777;
    }

}

.tagClass {
    width: 220px;
    background-color: rgba(247, 247, 247, 1);
    padding: 6px 12px;
}
.avatarLogo {
    height: 150px;
    width: 150px;
    display: block;
}
@media screen and (min-width: 600px) and (max-width: 1300px) {
    .tagClass {
        font-size: 12px;
    }

}
</style>
