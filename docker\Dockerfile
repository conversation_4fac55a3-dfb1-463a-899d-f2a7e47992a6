FROM 192.168.176.182/library/nginx:1.20.0
ENV TimeZone=Asia/Shanghai   
RUN ln -snf /usr/share/zoneinfo/$TimeZone /etc/localtime && echo $TimeZone > /etc/timezone
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
COPY sso.sino-bridge.com.key /etc/nginx/sso.sino-bridge.com.key
COPY sso.sino-bridge.com_bundle.pem /etc/nginx/sso.sino-bridge.com_bundle.pem

CMD ["nginx", "-g", "daemon off;"]
EXPOSE 443
