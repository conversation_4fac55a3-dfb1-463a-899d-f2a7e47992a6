<template>
    <div class="org_wrapper">
        <el-row type="flex" justify="center" class="title">
            <el-col :span="23">
                <div class="title_header">组织结构管理</div>
            </el-col>
        </el-row>

        <el-row type="flex" justify="center">
            <el-col :span="23">
                <div class="main_container">
                    <div class="main_left">
                        <div style="margin-bottom:10px;">
                            <el-row type="flex" justify="space-between">
                                <el-col :span="6" style="line-height: 40px">
                                    <el-button class="add" size="medium" @click="onAddStaff"  :disabled="selectedTreeNode.deptcode && selectedTreeNode.deptcode==11">新增
                                    </el-button>
                                </el-col>
                                <el-col :span="17" :offset="1">
                                    <el-input placeholder="请输入组织名称" v-model="filterText" class="inputStyle" suffix-icon="el-icon-search">
                                    </el-input>
                                </el-col>
                            </el-row>
                        </div>
                        <div style="margin-bottom: 20px;"></div>
                        <div style="overflow: auto; border: 0px solid rgb(235, 238, 245)" class="leftHeight">
                            <el-tree v-loading="orgTreeLoading" element-loading-background="rgb(33,39,77, 0.8)"
                                class="filter-tree" @node-click="onDeptTreeNodeClick" highlight-current
                                :data="transformedOrgData" :props="defaultProps" default-expand-all node-key="deptcode"
                                :expand-on-click-node="false" :filter-node-method="filterNode" ref="tree">
                                <span class="custom-tree-node" slot-scope="{ node,data }">
                                    <div v-if="!data.children" class="team_bg_image"></div>
                                    <div v-else class="zuzhiOpen"></div>
                                    <span>{{ node.label }}</span>
                                </span>
                            </el-tree>
                        </div>
                    </div>
                    <div class="main_right">
                        <el-form :model="orgForm" :rules="rules" ref="orgForm" label-width="160px"
                            label-position="right">
                            <el-form-item label="组织名称：" prop="deptname">
                                <el-input v-model="orgForm.deptname" autocomplete="off" maxlength="40" show-word-limit
                                    @change="changeName()"></el-input>
                            </el-form-item>
                            <el-form-item label="组织编码：" prop="deptcode" v-if="clickStatus == 'edit'">
                                <el-input v-model.trim="orgForm.deptcode" autocomplete="off" maxlength="20"
                                    show-word-limit disabled></el-input>
                            </el-form-item>
                            <el-form-item label="直属副总裁" prop="principalid"
                                v-if="selectedTreeNode.level == 2 && clickStatus == 'add'"
                                :rules="{ required: selectedTreeNode.level == 2 && clickStatus == 'add', message: '请选择直属副总裁', trigger: 'change' }">
                                <el-select v-model="orgForm.principalid" clearable placeholder="请选择直属副总裁"
                                    style="width: 100%">
                                    <el-option v-for="item in principalData" :key="item.PrincipalId"
                                        :label="item.PrincipalName" :value="item.PrincipalId">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="档案号" prop="filenumber"
                                v-if="selectedTreeNode.level == 2 && clickStatus == 'add'"
                                :rules="{ required: selectedTreeNode.level == 2 && clickStatus == 'add', message: '输入档案号', trigger: 'change' }">
                                <el-input v-model="orgForm.filenumber" maxlength="30"></el-input>
                                <span style="color: red">例：GJ-</span>
                            </el-form-item>
                            <el-form-item label="组织全称：" prop="deptfullname">
                                <el-input v-model="orgForm.deptfullname" :disabled="clickStatus == 'add'"></el-input>
                            </el-form-item>
                            <el-form-item label="负责人：" prop="leaderusername">
                                <el-select v-model="orgForm.leaderusername" filterable placeholder="请选择"
                                    style="width: 100%" clearable>
                                    <el-option v-for="item in localStaffList" :key="item.username"
                                        :label="item.staffname" :value="item.username">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <div style="text-align:center">
                            <el-button class="buttonSty" @click="onConfirm" :disabled="saveStatus">保存</el-button>
                            <el-button class="buttonSty"
                                :disabled="clickStatus == 'add' || (selectedTreeNode.deptname && selectedTreeNode.deptname.indexOf('集团公司') > -1)"
                                style="background-color: #9F9F9F;margin-left: 37px;" @click="deleteForReal">删除</el-button>
                        </div>
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import { listOrganizations, selectUser, updateOrg, addOrg, deleteOrg, selectPrincipal } from "@/api/sino/organization";
export default {
    name: "orgStructure",
    data() {
        return {
            orgTreeLoading: false,
            selectedTreeNode: {},
            orgTreeList: [],
            deptcode: '',
            defaultProps: {
                children: 'children',
                label: 'label'
            },
            filterText: '',
            orgForm: {
                deptname: '',
                deptfullname: '',
                leaderusername: '',
                deptcode: ''
            },
            rules: {
                leaderusername: [
                    { required: true, message: '请选择负责人', trigger: 'change' }
                ],
                deptfullname: [
                    { required: true, message: '请输入组织全称', trigger: 'change' }
                ],
                deptname: [
                    { required: true, message: '请输入组织名称', trigger: 'change' }
                ],

                deptcode: [
                    { required: true, message: '请输入组织编码', trigger: 'change' },
                    { pattern: /^\d+$/, message: '只能输入数字', trigger: 'change' }
                ]
            },
            loadingStaff: false,
            localStaffList: [],
            clickStatus: 'edit',//点击状态
            saveStatus: false,//保存按钮禁止重复点击
            principalData: [],
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
    },
    created() {
        this.getOrgList()
        this.loadingStaff = true;
        selectUser().then(res => {
            this.localStaffList = res.data;
            this.loadingStaff = false;
        });
        selectPrincipal().then(res => {
            this.principalData = res.data;
        });
    },
    methods: {
        getOrgList() {
            this.orgTreeLoading = true;
            // /dept/selectDept
            listOrganizations().then(res => {
                this.orgTreeLoading = false;
                const orgTreeList = res && res.data || [];
                this.orgTreeList = orgTreeList;
                if(this.orgTreeList.length === 0) return
                this.$nextTick(() => {
                    this.$refs["tree"].setCurrentKey("11");//对树赋值
                    this.orgForm = orgTreeList[0];
                    this.selectedTreeNode = orgTreeList[0]
                });
            });
        },
        changeName() {
            let that = this;
            if (that.clickStatus == 'add') {
                that.orgForm.deptfullname = ''
                that.orgForm.deptfullname = that.selectedTreeNode.deptfullname + that.orgForm.deptname
            }
        },
        //节点被点击时的回调
        onDeptTreeNodeClick(data) {
            this.selectedTreeNode = data;
            this.deptcode = data.deptcode;
            this.orgForm = { ...data };
            this.clickStatus = 'edit';
            // if (data.parentcode != 11) {
            //     const parentCodeLen = data.parentcode && data.parentcode.length;
            //     if (parentCodeLen) {
            //         this.orgForm.deptcode = data.deptcode.substring(parentCodeLen);//截取相应的orgCode
            //     }
            // }
        },

        //对输入的数据进行筛选
        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;
        },

        //封装数据,符合组件对数据的要求
        transformForestData(arr, labelName, valueName) {
            for (let node of arr) {
                this.transformNodeData(node, labelName, valueName);
            }
        },
        transformNodeData(node, labelName, valueName) {
            if (node) {
                if (node.children && node.children.length > 0) {
                    this.transformForestData(node.children, labelName, valueName);//封装数据,符合组件对数据的要求
                } else {
                    //console.log(node)
                }
                node.label = node[labelName];
                node.value = node[valueName];
            }
        },
        onAddStaff() {
            this.clickStatus = 'add';
            this.orgForm = {
                deptname: '',
                deptfullname: '',
                leaderusername: '',
                deptcode: ''
            };
        },
        onConfirm() {
            this.$refs.orgForm.validate(async valid => {
                if (valid) {//必填
                    this.saveStatus = true;
                    const orgForm = { ...this.orgForm };
                    let param = {
                        deptCode: orgForm.deptcode,
                        deptName: orgForm.deptname,
                        deptFullName: orgForm.deptfullname,
                        leaderUsername: orgForm.leaderusername,
                    }
                    let addParam = {
                        applydeptcode: this.selectedTreeNode.deptcode,
                        deptname: orgForm.deptname,
                        deptfullname: orgForm.deptfullname,
                        leaderusername: orgForm.leaderusername,
                        principalid: orgForm.principalid,
                        filenumber: orgForm.filenumber,
                    }
                    this.localStaffList.forEach((ele) => {
                        if (ele.username == orgForm.leaderusername) {
                            param.leaderName = ele.staffname
                            addParam.leaderstaffname = ele.staffname
                        }
                    })
                    this.principalData.forEach((ele) => {
                        if (ele.PrincipalId == orgForm.principalid) {
                            addParam.principalname = ele.PrincipalName
                        }
                    })
                    if (this.clickStatus == 'edit') {//编辑
                        updateOrg(param).then((res) => {
                            if (res && res.code == 200) {
                                this.saveStatus = false;
                                this.getOrgList();
                                this.$message({
                                    message: "保存成功",
                                    type: "success",
                                });
                            } else {
                                this.saveStatus = false;
                                this.$message({
                                    message: res.message || "保存失败",
                                    type: "error",
                                });
                            }
                        })
                    } else {
                        addOrg(addParam).then((res) => {
                            if (res && res.code == 200) {
                                this.saveStatus = false;
                                this.getOrgList();
                                this.$message({
                                    message: "保存成功",
                                    type: "success",
                                });
                            } else {
                                this.saveStatus = false;
                                this.$message({
                                    message: res.message || "保存失败",
                                    type: "error",
                                });
                            }
                        })
                    }
                }
            });
        },
        deleteForReal() {
            this.$confirm(
                "是否确认删除组织名称为" + this.selectedTreeNode.deptname + "的数据项?",
                "系统提示",
                {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }
            )
                .then(() => {
                    deleteOrg({ deptcode: this.selectedTreeNode.deptcode }).then((res) => {
                        if (res && res.code == 200) {
                            this.getOrgList();
                            this.$message({
                                message: "删除成功！",
                                type: "success",
                            });
                        } else {
                            this.$message.error({
                                message: res.message || "删除失败",
                            });
                        }
                    });
                })
                .catch(() => {
                    this.$message({
                        type: "info",
                        message: "已取消删除",
                    });
                });
        },
    },
    computed: {
        //对获取的组织数据进行封装
        transformedOrgData: function () {
            if (Array.isArray(this.orgTreeList)) {
                this.transformForestData(this.orgTreeList, 'deptname', 'deptcode');
            } else {
                this.transformNodeData(this.orgTreeList, 'deptname', 'deptcode');
            }
            return this.orgTreeList;
        },
    }
}
</script>

<style scoped lang="less">
.team_bg_image {
    background: url(../../assets/images/team.svg);
    background-size: cover;
    height: 25px;
    float: left;
    width: 25px;
}

.zuzhiOpen {
    background: url(../../assets/images/zuzhi-open.svg);
    background-size: cover;
    height: 15px;
    float: left;
    width: 15px;
    position: relative;
    top: 8px;
    left: 2px;
    margin-right: 5px
}

.custom-tree-node {
    font-size: 15px;
}

.org_wrapper {
    background-color: #21274D;
    margin: -20px 0;
    height: calc(100vh - 90px);

    /deep/ .el-tree__empty-block {
        background-color: #21274d;
    }
}

.main_container {
    display: flex;

    .main_left {
        min-width: 30%;
        margin-right: 10px;

        /deep/ .el-input .el-input__inner {
            //border-radius: 54px !important;
            background-color: rgba(255, 255, 255, 0.25) !important;
            color: rgba(199, 199, 199, 1) !important;
            border: 0px
        }

    }

    ::-webkit-scrollbar {
        width: 6px;
        /*滚动条宽度*/
        height: 10px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #21274D;
    }

    /*定义滑块 内阴影+圆角*/
    ::-webkit-scrollbar-thumb {
        box-shadow: inset 0 0 0px white;
        -webkit-box-shadow: inset 0 0 0px white;
        background-color: rgb(193, 193, 193);
        /*滚动条的背景颜色*/
        border-radius: 20px;
    }

    /*!* Handle *!*/
    /*::-webkit-scrollbar-thumb {*/
    /*    background: #888;*/
    /*}*/

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    .main_right {
        width: calc(100% - 250px);
        border-left: 1px solid rgba(187, 187, 187, 0.55);
        padding: 4% 10% 0% 10%;

        /deep/ .el-input .el-input__inner {
            background-color: rgba(255, 255, 255, 0.25) !important;
            color: rgba(245, 245, 245, 1) !important;
            border: 0px
        }

        /deep/ .el-input .el-input__count .el-input__count-inner {
            background: rgba(255, 255, 255, 0.01) !important;
        }

        /deep/ .el-form-item {
            margin-bottom: 18px !important;
        }
    }
}

.add {
    width: 83px;
    //border-radius: 54px;
    background-color: rgba(14, 97, 201, 1);
    color: rgba(255, 255, 255, 1);
    border: 0px;
}

.title {
    height: 66px;
    line-height: 66px;
    // border-bottom: 1px solid #eee;
    /*margin-bottom: 10px;*/
}

.buttonSty {
    width: 98px;
    //border-radius: 54px;
    background-color: rgba(14, 97, 201, 1);
    color: rgba(255, 255, 255, 1);
    border: 0px;
}

/* 13寸 */
@media screen and (min-width: 600px) and (max-width: 1300px) {
    .leftHeight {
        max-height: 48vh;
    }

    .custom-tree-node {
        font-size: 12px;
    }

    .main_container .main_right {
        padding: 0% 20% 0% 20%;
    }

    .buttonSty {
        width: 88px;
        height: 35px;
        line-height: 12px;
        font-size: 12px;
        border-radius: 4px;
    }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
    .leftHeight {
        max-height: 52vh;
    }
}
</style>
