<template>
  <div>
    <div class="systems">
      <span
        class="every-system"
        v-for="(item, index) in system"
        :class="{ active: activeIndex == index }"
        :key="index"
        @click="systemClick(item, index)"
        >{{ item }}</span
      >
    </div>
    <div>
      <span
        class="kinds-of-events"
        v-for="(item, index) in options"
        :key="item.id"
        :class="{ active: indexActive == index }"
        @click="optionsClick(item.label, index)"
        >{{ item.label }}</span
      >
    </div>
    <el-row>
      <el-col :span="5">
        <span class="input-title">检索：</span>
        <el-input
          v-model="checkContent"
          placeholder="名称编号模糊查询"
          style="width: 80%"
        ></el-input>
      </el-col>
      <el-col :span="5" :offset="1">
        <span class="input-title">创建人：</span>
        <el-input
          v-model="start"
          placeholder="按账号姓名搜索"
          style="width: 75%"
        ></el-input>
      </el-col>
      <el-col :span="5" :offset="1">
        <el-button type="primary" @click="search">查询</el-button>
        <el-button @click="reload">重置</el-button>
      </el-col>
    </el-row>
    <el-row
      style="margin-top: 50px; background-color: #21274d; padding-bottom: 10px"
    >
      <el-table
        :data="tableData"
        v-if="!isQuick"
        v-loading="loading"
        stripe
        highlight-current-row
        style="width: 100%"
        class="tableT"
      >
        <el-table-column label="工单标题" width="380">
          <template slot-scope="props">
            <span
              style="
                display: inline-block;
                width: 100%;
                cursor: pointer;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              "
              @click="newPage(props.row.url, props.row.appToDoType, props.row)"
              >{{
                props.row.title ? props.row.title : props.row.procInstTitle
              }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="procInstKey"
          label="编号"
          width="170"
          :key="1"
        ></el-table-column>
        <el-table-column prop="processName" label="流程名称">
          <template slot-scope="props">
            <span>{{
              props.row.processName
                ? props.row.processName
                : props.row.procDefGroupName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="procDefGroupName" label="类别"></el-table-column>
        <el-table-column
          prop="taskName"
          label="当前任务"
          :key="2"
        ></el-table-column>
        <el-table-column label="到达时间">
          <template slot-scope="props">
            <span>{{
              props.row.taskCreateTime | converTime("YYYY-MM-DD hh:mm")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="prevHandler"
          label="最后处理人"
        ></el-table-column>
        <el-table-column label="时间">
          <template slot-scope="props">
            <span>{{
              props.row.procInstCreateTime | converTime("YYYY-MM-DD hh:mm")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发起人">
          <template slot-scope="props">
            <span>{{
              props.row.creator ? props.row.creator : props.row.procInstCreator
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="时间">
          <template slot-scope="props">
            <span>{{
              props.row.procInstLastModTime | converTime("YYYY-MM-DD hh:mm")
            }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="!isQuick"
        style="margin-top: 10px"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        @prev-click="prevClick"
        @next-click="nextClick"
        :current-page="currentPage"
        :page-sizes="[5, 10, 15]"
        :page-size="currentPageSize"
        layout="total, sizes, prev, pager, next,jumper"
        :total="totalPage"
      >
      </el-pagination>
      <el-table
        :data="quickSysData"
        v-if="isQuick"
        v-loading="quickLoading"
        stripe
        highlight-current-row
        style="width: 100%"
        class="tableT"
      >
        <el-table-column label="工单标题" width="380">
          <template slot-scope="props">
            <span
              style="
                display: inline-block;
                width: 100%;
                cursor: pointer;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
              "
              @click="quickSysClick(props.row)"
              >{{ props.row.reimName }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="金额" width="170">
          <template slot-scope="props">
            <span>{{ "￥" + props.row.reimAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="类型">
          <template slot-scope="props">
            <span>{{
              props.row.reimbursementType == "rc" ? "日常" : "差旅"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="reimbursementDesc"
          label="用途"
        ></el-table-column>
        <el-table-column label="到达时间">
          <template slot-scope="props">
            <span>{{
              props.row.arriveTime | converTime("YYYY-MM-DD hh:mm")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="taskNode" label="任务节点"></el-table-column>
        <el-table-column label="发起时间">
          <template slot-scope="props">
            <span>{{
              props.row.createTime | converTime("YYYY-MM-DD hh:mm")
            }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="isQuick"
        style="margin-top: 10px"
        @size-change="handleQuickSizeChange"
        @current-change="handleQuickCurrentChange"
        @prev-click="prevQuickClick"
        @next-click="nextQuickClick"
        :current-page="currentQuickPage"
        :page-sizes="[5, 10, 15]"
        :page-size="currentQuickPageSize"
        layout="total, sizes, prev, pager, next,jumper"
        :total="totalQuickPage"
      >
      </el-pagination>
    </el-row>
  </div>
</template>
<script>
import { getWechatUserInfo, todoList, todoListQUICK } from "@/api/sino/sino";
// import { mapState } from 'vuex'
import lodash from "lodash";
export default {
  name: "waiting",
  data() {
    return {
      activeIndex: 0,
      indexActive: 0,
      value: "全部",
      checkContent: "",
      start: "",
      currentPage: 1,
      loading: true,
      quickLoading: true,
      currentPageSize: 15,
      totalPage: 0,
      currentQuickPage: 1,
      currentQuickPageSize: 15,
      totalQuickPage: 0,
      currentSys: "EOSS",
      system: ["EOSS", "PMS", "EHR", "QUICK", "NTDH"],
      isQuick: false,
      flag: false,
      options: [
        {
          id: 1,
          label: "全部",
        },
        {
          id: 2,
          label: "合同审批",
        },
        {
          id: 3,
          label: "合同变更",
        },
        {
          id: 4,
          label: "合同发票",
        },
        {
          id: 5,
          label: "合同盖章",
        },
        {
          id: 6,
          label: "订单审批",
        },
        {
          id: 7,
          label: "内部采购",
        },
        {
          id: 8,
          label: "付款审批",
        },
        {
          id: 9,
          label: "发票审批",
        },
        {
          id: 12,
          label: "财务借款",
        },
        {
          id: 14,
          label: "投标审批",
        },
      ],
      tableData: [],
      quickSysData: [],
      ntdhUrl: "http://erp.ntdhcc.com:8088/flow/procinst/view",
    };
  },
  computed: {
    // ...mapState({
    //   userName:state => state.userName
    // })
  },
  created() {
    this.userName = window.localStorage.getItem("Susername");
    this.getData(
      this.currentSys,
      this.currentPage,
      this.currentPageSize,
      this.userName
    );
    //window.localStorage.setItem("userInfo",JSON.stringify(obj))
    //window.open("http://172.16.4.4:8086/approval/#/reimDetail/?id=11400&sid=427597&processInstanceId=427583&userName=hehua&flag=false")
  },
  mounted() {
    let that = this;
    window.addEventListener("visibilitychange", function () {
      if (!document.hidden) {
        if (that.currentSys == "QUICK") {
          that.quickLoading = true;
          that.quickData(
            that.currentQuickPage,
            that.currentQuickPageSize,
            that.userName,
            that.value,
            that.checkContent,
            that.start
          );
        } else {
          that.loading = true;
          that.getData(
            that.currentSys,
            that.currentPage,
            that.currentPageSize,
            that.userName,
            that.value,
            that.checkContent,
            that.start
          );
        }
      }
    });
  },
  methods: {
    getData: lodash.debounce(function (
      sys,
      pageNo,
      pageSize,
      username,
      value,
      checkContent,
      start
    ) {
      value = value || "";
      checkContent = checkContent || "";
      start = start || "";
      if (value === "全部") {
        value = "";
      }
      todoList(sys, {
        pageNo,
        pageSize,
        username,
        processName: value,
        title: checkContent,
        creator: start,
      }).then((res) => {
        if (res.code == 200) {
          if (res.data.list) {
            this.tableData = res.data.list;
          } else {
            this.tableData = res.data.records;
          }
          this.loading = false;
          this.totalPage = res.data.total;
        }
      });
    },
    400),
    quickData: lodash.debounce(function (
      pageNo,
      pageSize,
      username,
      value,
      checkContent,
      start
    ) {
      value = value || "";
      checkContent = checkContent || "";
      start = start || "";
      if (value === "全部") {
        value = "";
      }
      todoListQUICK({
        pageNo,
        pageSize,
        username,
        processName: value,
        title: checkContent,
        creator: start,
      }).then((res) => {
        this.quickSysData = res.data.data;
        this.totalQuickPage = res.data.count;
        this.flag = res.data.flag;
        this.quickLoading = false;
      });
    },
    400),
    prevClick(val) {
      this.currentPage = val;
      this.getData(
        this.currentSys,
        this.currentPage,
        this.currentPageSize,
        this.userName
      );
    },
    nextClick(val) {
      this.currentPage = val;
      this.getData(
        this.currentSys,
        this.currentPage,
        this.currentPageSize,
        this.userName
      );
    },
    handleSizeChange(val) {
      this.currentPageSize = val;
      this.getData(
        this.currentSys,
        this.currentPage,
        this.currentPageSize,
        this.userName
      );
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData(
        this.currentSys,
        this.currentPage,
        this.currentPageSize,
        this.userName
      );
    },
    prevQuickClick(val) {
      this.currentQuickPage = val;
      this.quickData(
        this.currentQuickPage,
        this.currentQuickPageSize,
        this.userName
      );
    },
    nextQuickClick(val) {
      this.currentQuickPage = val;
      this.quickData(
        this.currentQuickPage,
        this.currentQuickPageSize,
        this.userName
      );
    },
    handleQuickSizeChange(val) {
      this.currentQuickPageSize = val;
      this.quickData(
        this.currentQuickPage,
        this.currentQuickPageSize,
        this.userName
      );
    },
    handleQuickCurrentChange(val) {
      this.currentQuickPage = val;
      this.quickData(
        this.currentQuickPage,
        this.currentQuickPageSize,
        this.userName
      );
    },
    search() {
      this.loading = true;
      this.quickLoading = true;
      if (this.currentSys == "QUICK") {
        this.quickData(
          this.currentQuickPage,
          this.currentQuickPageSize,
          this.userName,
          this.value,
          this.checkContent,
          this.start
        );
      } else {
        this.getData(
          this.currentSys,
          this.currentPage,
          this.currentPageSize,
          this.userName,
          this.value,
          this.checkContent,
          this.start
        );
      }
    },
    reload() {
      this.loading = true;
      this.quickLoading = true;
      this.value = "";
      this.checkContent = "";
      this.start = "";
      this.currentSys = "EOSS";
      this.currentPage = 1;
      this.indexActive = 0;
      this.activeIndex = 0;
      this.isQuick = false;
      this.getData(
        this.currentSys,
        this.currentPage,
        this.currentPageSize,
        this.userName,
        this.value,
        this.checkContent,
        this.start
      );
    },
    newPage(url, systemType, row) {
      let cookie = document.cookie;
      let accessToken = "";
      if (cookie) {
        let arr = cookie.split("; ");
        arr.forEach((v) => {
          let arr2 = v.split("=");
          if (arr2[0] == "access_token") {
            accessToken = arr2[1];
          }
        });
      }
      let openUrl = "";
      if (systemType == "EOSS") {
        //58.221.144.130:8806
        openUrl = `http://eoss.sino-bridge.com/othersLogin?dbflag=1&dburl=${url}&username=${this.userName}&token=${accessToken}`;
      } else if (systemType == "PMS") {
        openUrl = `http://pms.sino-bridge.com/userLoginNoPwd?dbflag=1&username=${this.userName}&prove=${accessToken}&taskId=${row.taskId}`;
      } else if (systemType == "NTDH") {
        openUrl = `http://erp.ntdhcc.com:8088/othersLogin?dbflag=1&dburl=${this.ntdhUrl}?taskId=${row.taskId}%26procInstId=${row.procInstId}&username=${this.userName}&token=${accessToken}`;
      }
      window.open(openUrl);
    },
    systemClick(sys, ind) {
      switch (sys) {
        case "EOSS":
          this.options = [
            {
              id: 1,
              label: "全部",
            },
            {
              id: 2,
              label: "合同审批",
            },
            {
              id: 3,
              label: "合同变更",
            },
            {
              id: 4,
              label: "合同发票",
            },
            {
              id: 5,
              label: "合同盖章",
            },
            {
              id: 6,
              label: "订单审批",
            },
            {
              id: 7,
              label: "内部采购",
            },
            {
              id: 8,
              label: "付款审批",
            },
            {
              id: 9,
              label: "发票审批",
            },
            {
              id: 12,
              label: "财务借款",
            },
            {
              id: 14,
              label: "投标审批",
            },
          ];
          break;
        case "PMS":
          this.options = [
            {
              id: 1,
              label: "全部",
            },
            {
              id: 10,
              label: "项目",
            },
          ];
          break;
        case "EHR":
          this.options = [
            {
              id: 1,
              label: "全部",
            },
            {
              id: 13,
              label: "人力",
            },
          ];
          break;
        case "QUICK":
          this.options = [
            {
              id: 1,
              label: "全部",
            },
            {
              id: 11,
              label: "财务报销",
            },
          ];
          break;
        case "NTDH":
          this.options = [
            {
              id: 1,
              label: "全部",
            },
            {
              id: 2,
              label: "合同审批",
            },
            {
              id: 3,
              label: "合同变更",
            },
            {
              id: 4,
              label: "合同发票",
            },
            {
              id: 5,
              label: "合同盖章",
            },
            {
              id: 6,
              label: "订单审批",
            },
            {
              id: 7,
              label: "内部采购",
            },
            {
              id: 8,
              label: "付款审批",
            },
            {
              id: 9,
              label: "发票审批",
            },
            {
              id: 10,
              label: "项目",
            },
            {
              id: 11,
              label: "财务报销",
            },
            {
              id: 12,
              label: "财务借款",
            },
            {
              id: 13,
              label: "人力",
            },
            {
              id: 14,
              label: "投标审批",
            },
          ];
          break;
      }
      this.loading = true;
      this.quickLoading = true;
      this.activeIndex = ind;
      this.currentSys = sys;
      if (sys === "QUICK") {
        this.quickData(this.currentPage, this.currentPageSize, this.userName);
        this.isQuick = true;
        getWechatUserInfo({
          params: { userId: this.userName },
        }).then((res) => {
          let obj = res.data;
          obj.flag = "reim";
          window.localStorage.removeItem("userInfo");
          window.localStorage.setItem("userInfo", JSON.stringify(obj.data));
        });
      } else {
        this.getData(
          this.currentSys,
          this.currentPage,
          this.currentPageSize,
          this.userName
        );
        this.isQuick = false;
      }
    },
    optionsClick(item, index) {
      this.indexActive = index;
      this.value = item;
      this.search();
    },
    quickSysClick(row) {
      let url = `http://nwechat.sino-bridge.com:8866/approval/#/reimDetail/?id=${row.reimId}&sid=${row.taskId}&processInstanceId=${row.processInstanceId}&userName=hehua&flag=${this.flag}`;
      window.open(url);
    },
  },
  watch: {},
};
</script>
<style scoped>
.el-pagination {
  float: right;
}

.active {
  background-color: rgb(56, 52, 255) !important;
  color: #fff !important;
}

.every-system {
  display: inline-block;
  margin-right: 26px;
  width: 80px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: rgb(243, 243, 243);
  color: #333;
  margin-bottom: 14px;
  cursor: pointer;
  border-radius: 4px;
}

.kinds-of-events {
  display: inline-block;
  margin-right: 26px;
  width: 80px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: rgb(243, 243, 243);
  color: #333;
  margin-bottom: 14px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
}

.input-title {
  color: #fff;
  font-size: 16px;
}
</style>
