import axios from 'axios'
import { clearC<PERSON>ie,set<PERSON><PERSON>ie,getCookie } from "@/utils/cookie";
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
// axios.defaults.withCredentials = true;// 设置反向代理，前端请求默认发送到 http://localhost:8090/api
//创建axios实例
const service =axios.create({
    // axios中请求配置有baseURL选项，表示请求URL公共部分
    baseURL: process.env.VUE_APP_BASE_API,
    // 超时
    timeout:3000000
})
//配置request拦截器
service.interceptors.request.use(config => {
    console.log('Request:', config.method && config.method.toUpperCase(), config.url, config.data || config.params);
    return config;
}, error => {
    console.error('Request error:', error);
    return Promise.reject(error);
})
//配置响应器
service.interceptors.response.use(res => {
    console.log('Response:', res.status, res.config.url, res.data);
    setCookie("access_token", getCookie("access_token"), 0.125);
    setCookie("refresh_token",getCookie("refresh_token"), 0.125);
    //未设置状态码，默认成功
    const code = res.data.code || 200;
    // console.log("请求返回",res)
    if(code === 200){
        return res.data;
    }else if (code == -1) {
        console.log("登录过期，请重新登录",JSON.stringify(res))

        clearCookie("access_token");
        clearCookie("refresh_token");
        setTimeout(() => {
          window.location.reload()
        }, 300);
      }
    else {
        return res.data;
    }
}, error => {
    console.error('Response error:', error);
    console.error('Error details:', {
        message: error.message,
        status: error.response && error.response.status,
        statusText: error.response && error.response.statusText,
        url: error.config && error.config.url,
        data: error.response && error.response.data
    });
    return Promise.reject(error);
})

export default service
