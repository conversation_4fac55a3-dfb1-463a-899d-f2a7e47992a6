import request from "@/utils/request";
//获取所有角色
export const getAllRole = (param) => {
    return new Promise((resolve) => {
        request.get("/role/getAllRole?pageSize=" + param.pagesize + "&&pageNum=" +
            param.currentPage + "&&sSearch=" + param.searchword
        ).then((res) => {
            resolve(res);
        });
    });
};
//新增
export const addRole = (param) => {
    return new Promise((resolve) => {
        request.post("/role/addRole", param).then((res) => {
            resolve(res);
        });
    });
};
//离职空白模板申请
// export const getResignTemplate = () => {
//     return new Promise((resolve) => {
//         request.get("/resign/getResignTemplate").then((res) => {
//             resolve(res);
//         });
//     });
// };
// //离职空白模板申请
// export const downLoad = (param) => {
//     return new Promise((resolve) => {
//         request.get("/resign/downLoad?userName="+ param.userName+"&&reason="+ param.reason).then((res) => {
//             resolve(res);
//         });
//     });
// };
//

// 确定提交
export const createSubmit = (param) => {
    // console.log(param, "param.......");
    return new Promise((resolve) => {
        request.post("/resign/create", param).then((res) => {
            resolve(res);
        });
    });
};

//表格渲染
export const getUserQuit = (param) => {
    return new Promise((resolve) => {
        request.get("/resign/getUserQuit?userName="+ param.userName
        ).then((res) => {
            resolve(res);
        });
    });
};

// 审批详情
export const getApprovalFlow = (param) => {
    return new Promise((resolve) => {
        request.get("/resign/getApprovalFlow?procInstId="+ param.procInstId).then((res) => {
            resolve(res);
        });
    });
};
