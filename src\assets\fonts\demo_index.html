<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4086147" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xee0d;</span>
                <div class="name">工作台-消息提醒</div>
                <div class="code-name">&amp;#xee0d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe677;</span>
                <div class="name">消息提醒选中</div>
                <div class="code-name">&amp;#xe677;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">消息中心_消息_有消息提醒</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6be;</span>
                <div class="name">消息提醒</div>
                <div class="code-name">&amp;#xe6be;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f2;</span>
                <div class="name">费用概览_icon_消息提醒</div>
                <div class="code-name">&amp;#xe6f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">消息提醒-有提醒</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">身份证-copy</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">身份证-copy-copy</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">统计</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">规章制度发布</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">查看最新消息1</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe72d;</span>
                <div class="name">最新消息</div>
                <div class="code-name">&amp;#xe72d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71c;</span>
                <div class="name">消息中心_最新消息</div>
                <div class="code-name">&amp;#xe71c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">new</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe667;</span>
                <div class="name">new</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">部门</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bd;</span>
                <div class="name">职位</div>
                <div class="code-name">&amp;#xe7bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe780;</span>
                <div class="name">部门</div>
                <div class="code-name">&amp;#xe780;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">职位</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">部门</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe809;</span>
                <div class="name">个人事项</div>
                <div class="code-name">&amp;#xe809;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe807;</span>
                <div class="name">岗位</div>
                <div class="code-name">&amp;#xe807;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8f7;</span>
                <div class="name">菜单-个人事项</div>
                <div class="code-name">&amp;#xe8f7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">上传</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66c;</span>
                <div class="name">组织</div>
                <div class="code-name">&amp;#xe66c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">待办</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">岗位</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">通讯录</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">电视</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b7;</span>
                <div class="name">应用-填充</div>
                <div class="code-name">&amp;#xe7b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">待办</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62d;</span>
                <div class="name">团队</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">权限管理</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1730883784975') format('woff2'),
       url('iconfont.woff?t=1730883784975') format('woff'),
       url('iconfont.ttf?t=1730883784975') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-gongzuotai-xiaoxitixing"></span>
            <div class="name">
              工作台-消息提醒
            </div>
            <div class="code-name">.icon-gongzuotai-xiaoxitixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiaoxitixingxuanzhong"></span>
            <div class="name">
              消息提醒选中
            </div>
            <div class="code-name">.icon-xiaoxitixingxuanzhong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiaoxizhongxin_xiaoxi_youxiaoxitixing"></span>
            <div class="name">
              消息中心_消息_有消息提醒
            </div>
            <div class="code-name">.icon-xiaoxizhongxin_xiaoxi_youxiaoxitixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiaoxitixing"></span>
            <div class="name">
              消息提醒
            </div>
            <div class="code-name">.icon-xiaoxitixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-feiyonggailan_icon_xiaoxitixing"></span>
            <div class="name">
              费用概览_icon_消息提醒
            </div>
            <div class="code-name">.icon-feiyonggailan_icon_xiaoxitixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiaoxitixing-youtixing"></span>
            <div class="name">
              消息提醒-有提醒
            </div>
            <div class="code-name">.icon-xiaoxitixing-youtixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenfenzheng6-copy-copy"></span>
            <div class="name">
              身份证-copy
            </div>
            <div class="code-name">.icon-shenfenzheng6-copy-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-id-card-front-copy"></span>
            <div class="name">
              身份证-copy-copy
            </div>
            <div class="code-name">.icon-id-card-front-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongji1"></span>
            <div class="name">
              统计
            </div>
            <div class="code-name">.icon-tongji1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinwenfabu"></span>
            <div class="name">
              规章制度发布
            </div>
            <div class="code-name">.icon-xinwenfabu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chakanzuixinxiaoxi"></span>
            <div class="name">
              查看最新消息1
            </div>
            <div class="code-name">.icon-chakanzuixinxiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuixinxiaoxi"></span>
            <div class="name">
              最新消息
            </div>
            <div class="code-name">.icon-zuixinxiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiaoxizhongxin_zuixinxiaoxi"></span>
            <div class="name">
              消息中心_最新消息
            </div>
            <div class="code-name">.icon-xiaoxizhongxin_zuixinxiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-3"></span>
            <div class="name">
              new
            </div>
            <div class="code-name">.icon-3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-new"></span>
            <div class="name">
              new
            </div>
            <div class="code-name">.icon-new
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-bumen1x"></span>
            <div class="name">
              部门
            </div>
            <div class="code-name">.icon-a-bumen1x
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhiwei"></span>
            <div class="name">
              职位
            </div>
            <div class="code-name">.icon-zhiwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bumen"></span>
            <div class="name">
              部门
            </div>
            <div class="code-name">.icon-bumen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-position"></span>
            <div class="name">
              职位
            </div>
            <div class="code-name">.icon-position
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bumen-copy"></span>
            <div class="name">
              部门
            </div>
            <div class="code-name">.icon-bumen-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gerenshixiang-01"></span>
            <div class="name">
              个人事项
            </div>
            <div class="code-name">.icon-gerenshixiang-01
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gangwei1"></span>
            <div class="name">
              岗位
            </div>
            <div class="code-name">.icon-gangwei1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazai-"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caidan-gerenshixiang-copy"></span>
            <div class="name">
              菜单-个人事项
            </div>
            <div class="code-name">.icon-caidan-gerenshixiang-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shangchuan2"></span>
            <div class="name">
              上传
            </div>
            <div class="code-name">.icon-shangchuan2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuzhi"></span>
            <div class="name">
              组织
            </div>
            <div class="code-name">.icon-zuzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jishiben"></span>
            <div class="name">
              待办
            </div>
            <div class="code-name">.icon-jishiben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gangwei"></span>
            <div class="name">
              岗位
            </div>
            <div class="code-name">.icon-gangwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zuzhizuguanli"></span>
            <div class="name">
              通讯录
            </div>
            <div class="code-name">.icon-zuzhizuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianshi"></span>
            <div class="name">
              电视
            </div>
            <div class="code-name">.icon-dianshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingyong-tianchong"></span>
            <div class="name">
              应用-填充
            </div>
            <div class="code-name">.icon-yingyong-tianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.icon-shouye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daiban"></span>
            <div class="name">
              待办
            </div>
            <div class="code-name">.icon-daiban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuandui"></span>
            <div class="name">
              团队
            </div>
            <div class="code-name">.icon-tuandui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanxianguanli"></span>
            <div class="name">
              权限管理
            </div>
            <div class="code-name">.icon-quanxianguanli
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuotai-xiaoxitixing"></use>
                </svg>
                <div class="name">工作台-消息提醒</div>
                <div class="code-name">#icon-gongzuotai-xiaoxitixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxitixingxuanzhong"></use>
                </svg>
                <div class="name">消息提醒选中</div>
                <div class="code-name">#icon-xiaoxitixingxuanzhong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxizhongxin_xiaoxi_youxiaoxitixing"></use>
                </svg>
                <div class="name">消息中心_消息_有消息提醒</div>
                <div class="code-name">#icon-xiaoxizhongxin_xiaoxi_youxiaoxitixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxitixing"></use>
                </svg>
                <div class="name">消息提醒</div>
                <div class="code-name">#icon-xiaoxitixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-feiyonggailan_icon_xiaoxitixing"></use>
                </svg>
                <div class="name">费用概览_icon_消息提醒</div>
                <div class="code-name">#icon-feiyonggailan_icon_xiaoxitixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxitixing-youtixing"></use>
                </svg>
                <div class="name">消息提醒-有提醒</div>
                <div class="code-name">#icon-xiaoxitixing-youtixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenfenzheng6-copy-copy"></use>
                </svg>
                <div class="name">身份证-copy</div>
                <div class="code-name">#icon-shenfenzheng6-copy-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-id-card-front-copy"></use>
                </svg>
                <div class="name">身份证-copy-copy</div>
                <div class="code-name">#icon-id-card-front-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongji1"></use>
                </svg>
                <div class="name">统计</div>
                <div class="code-name">#icon-tongji1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinwenfabu"></use>
                </svg>
                <div class="name">规章制度发布</div>
                <div class="code-name">#icon-xinwenfabu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chakanzuixinxiaoxi"></use>
                </svg>
                <div class="name">查看最新消息1</div>
                <div class="code-name">#icon-chakanzuixinxiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuixinxiaoxi"></use>
                </svg>
                <div class="name">最新消息</div>
                <div class="code-name">#icon-zuixinxiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxizhongxin_zuixinxiaoxi"></use>
                </svg>
                <div class="name">消息中心_最新消息</div>
                <div class="code-name">#icon-xiaoxizhongxin_zuixinxiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-3"></use>
                </svg>
                <div class="name">new</div>
                <div class="code-name">#icon-3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-new"></use>
                </svg>
                <div class="name">new</div>
                <div class="code-name">#icon-new</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bumen1x"></use>
                </svg>
                <div class="name">部门</div>
                <div class="code-name">#icon-a-bumen1x</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhiwei"></use>
                </svg>
                <div class="name">职位</div>
                <div class="code-name">#icon-zhiwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bumen"></use>
                </svg>
                <div class="name">部门</div>
                <div class="code-name">#icon-bumen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-position"></use>
                </svg>
                <div class="name">职位</div>
                <div class="code-name">#icon-position</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bumen-copy"></use>
                </svg>
                <div class="name">部门</div>
                <div class="code-name">#icon-bumen-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gerenshixiang-01"></use>
                </svg>
                <div class="name">个人事项</div>
                <div class="code-name">#icon-gerenshixiang-01</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gangwei1"></use>
                </svg>
                <div class="name">岗位</div>
                <div class="code-name">#icon-gangwei1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai-"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caidan-gerenshixiang-copy"></use>
                </svg>
                <div class="name">菜单-个人事项</div>
                <div class="code-name">#icon-caidan-gerenshixiang-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangchuan2"></use>
                </svg>
                <div class="name">上传</div>
                <div class="code-name">#icon-shangchuan2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuzhi"></use>
                </svg>
                <div class="name">组织</div>
                <div class="code-name">#icon-zuzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jishiben"></use>
                </svg>
                <div class="name">待办</div>
                <div class="code-name">#icon-jishiben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gangwei"></use>
                </svg>
                <div class="name">岗位</div>
                <div class="code-name">#icon-gangwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zuzhizuguanli"></use>
                </svg>
                <div class="name">通讯录</div>
                <div class="code-name">#icon-zuzhizuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianshi"></use>
                </svg>
                <div class="name">电视</div>
                <div class="code-name">#icon-dianshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingyong-tianchong"></use>
                </svg>
                <div class="name">应用-填充</div>
                <div class="code-name">#icon-yingyong-tianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#icon-shouye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daiban"></use>
                </svg>
                <div class="name">待办</div>
                <div class="code-name">#icon-daiban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuandui"></use>
                </svg>
                <div class="name">团队</div>
                <div class="code-name">#icon-tuandui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanxianguanli"></use>
                </svg>
                <div class="name">权限管理</div>
                <div class="code-name">#icon-quanxianguanli</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
