import request from "@/utils/request";

/**
 *
 *列表 查询
 * **/
export const getRecordList = (param) => {
  return new Promise((resolve) => {
    request
      .get(
        "/articleTopic/getPublishTableGrid?pageSize=" +
          param.pageSize +
          "&&pageNum=" +
          param.pageNum +
          "&&title=" +
          param.title +
          "&&catalogid=" +
          param.catalogid
      )
      .then((res) => {
        resolve(res);
      });
  });
};
export const articleTopicGetAttachPassPage = (param) => {
  return new Promise((resolve) => {
    request
      .get(
        "/articleTopic/getAttachPassPage?pageSize=" +
          param.pageSize +
          "&&pageNum=" +
          param.pageNum +
          "&&title=" +
          param.title +
          "&&type=" +
          param.type
      )
      .then((res) => {
        resolve(res);
      });
  });
};

/**
 *
 * 修改
 * **/
export const createTopic = (param) => {
  return new Promise((resolve) => {
    let str = `/articleTopic/create`;
    request.post(str, param).then((res) => {
      resolve(res);
    });
  });
};
export const getArticleCatalog = () => {
  return new Promise((resolve) => {
    request.get("/articleTopic/getArticleCatalog").then((res) => {
      resolve(res);
    });
  });
};
// 删除
export const deleteRecordItem = (id) => {
  return new Promise((resolve) => {
    request
      .get(
        "/articleTopic/deleteArticle?id=" + id
      )
      .then((res) => {
        resolve(res);
      });
  });
};
// 发布
export const publishRecordItem = (param) => {
  return new Promise((resolve) => {
    let str = `/articleTopic/updateArticle`;
    request.post(str, param).then((res) => {
      resolve(res);
    });
  });
};

export const userListRoleUser = (userName) => {
  return new Promise((resolve) => {
    // https://scrm.sino-bridge.com:8098/oa/blade-api/blade-system/user/listRoleUser
    let str = `/articleTopic/listRoleUser?userName=${userName}`;
    request.get(str).then((res) => {
      resolve(res);
    });
  });
};

export const insertAttachPass = (param) => {
  return new Promise((resolve) => {

    let str = `/articleTopic/insertAttachPass`;
    request.post(str, param).then((res) => {
      resolve(res);
    });
  });
};
export const updateAttachPass = (param) => {
  return new Promise((resolve) => {

    let str = `/articleTopic/updateAttachPass`;
    request.post(str, param).then((res) => {
      resolve(res);
    });
  });
};
export const getAttachPassById = (param) => {
  return new Promise((resolve) => {

    let str = `/articleTopic/getAttachPassById?id=${param}`;
    request.get(str, param).then((res) => {
      resolve(res);
    });
  });
};
export const getDetailByUserId = (id,userId,type) => {
  return new Promise((resolve) => {

    let str = `/articleTopic/getDetailByUserId?id=${id}&userId=${userId}&type=${type}`;
    request.get(str).then((res) => {
      resolve(res);
    });
  });
};
export const updateAttachPassRead = (attachPassId,userId ) => {
  return new Promise((resolve) => {

    let str = `/articleTopic/updateAttachPassRead?attachPassId=${attachPassId}&userId=${userId}`;
    request.get(str).then((res) => {
      resolve(res);
    });
  });
};
export const publishAttachPass = (id ) => {
  return new Promise((resolve) => {

    let str = `/articleTopic/publishAttachPass?id=${id}`;
    request.get(str).then((res) => {
      resolve(res);
    });
  });
};
export const downLoadFile = (path ) => {
  return new Promise((resolve) => {

    let str = `/articleTopic/downLoadFile?path=${path}`;
    request.get(str,{responseType: "blob"}).then((res) => {
      resolve(res);
    });
  });
};
export const deleteAttachPass = (id ) => {
  return new Promise((resolve) => {

    let str = `/articleTopic/deleteAttachPass?id=${id}`;
    request.get(str).then((res) => {
      resolve(res);
    });
  });
};
export const articleTopicGetDictList = (code ) => {
  return new Promise((resolve) => {

    let str = `/articleTopic/getDictList?code=${code}`;
    request.get(str).then((res) => {
      resolve(res);
    });
  });
};
export const getAttachPassByUserId = (param ) => {
  return new Promise((resolve) => {
     let str = "/articleTopic/getAttachPassByUserId?pageSize=" +
          param.pageSize +
          "&&pageNum=" +
          param.pageNum +
          "&&title=" +
          param.title +
          "&&userId=" +
          param.userId+
          "&&type=" +
          param.type;
    request.get(str).then((res) => {
      resolve(res);
    });
  });
};