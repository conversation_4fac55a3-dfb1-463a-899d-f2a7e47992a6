import request from "@/utils/request";
import qs from "qs";
//获取应用列表
export const getAppList = (Susername) => {
  return new Promise((resolve) => {
    let param = {
      username: Susername,
    };
    let headerParams = {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    };
    request
      .post("/account/getApp", qs.stringify(param), headerParams)
      .then((res) => {
        resolve(res);
      });
  });
};
//获取用户数据
export const getUserName = (query) => {
  return new Promise((resolve) => {
    request.get("/account/getUserName?sSearch=" + query).then((res) => {
      resolve(res);
    });
  });
};
//获取账号管理列表数据
export const getAccount = (param) => {
  return new Promise((resolve) => {
    request
      .get(
        "/account/getAccount?pageSize=" +
          param.pagesize +
          "&&pageNum=" +
          param.currentPage +
          "&&sSearch=" +
          param.searchword
      )
      .then((res) => {
        resolve(res);
      });
  });
};
//新增账号管理
export const accountAdd = (param) => {
  return new Promise((resolve) => {
    request.post("/account/add", param).then((res) => {
      resolve(res);
    });
  });
};
//修改账号管理
export const accountUpdate = (param) => {
  return new Promise((resolve) => {
    request.post("/account/update", param).then((res) => {
      resolve(res);
    });
  });
};
//删除账号管理
export const accountDel = (param) => {
  return new Promise((resolve) => {
    request.post("/account/deltete", param).then((res) => {
      resolve(res);
    });
  });
};
//获取账号管理详情
export const getAccountDetail = (param) => {
  return new Promise((resolve) => {
    request.post("/account/getAccountDetail?userName=" +param.userName, param).then((res) => {
      resolve(res);
    });
  });
};
