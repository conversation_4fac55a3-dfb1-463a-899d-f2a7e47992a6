<template>
  <div class="tab" id="version">
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23">
        <div class="title_header">版本更新</div>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center" class="search">
      <el-col :span="23" :class="isAllSerarch? 'searchBox searchBoxBg':'searchBox'">
        <i class="el-icon-close closeSearch" v-if="isAllSerarch" @click="searchThrottle('close_search_all')"></i>
        <template v-if="!isAllSerarch">
          <el-col :span="8" style="text-align: left" v-if="isAdmin">
            <el-button class="btnStyle" type="primary" style="background-color: #0E61C9; width: 122px;" size="medium"

                       @click="openAddDialog">添加更新记录
            </el-button>

          </el-col>
          <el-col :span="isAdmin?16:24" style="text-align: right">
            <el-input style="width: 306px;" placeholder="请输入内容" @keyup.enter.native="searchThrottle('search')"
                      v-model.trim="query.allName" @input="searchThrottle('search')" size="medium">
              <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer"
                 @click="searchThrottle('search')"></i>
            </el-input>
            <el-button class="buttonStyle" type="primary" style=" background-color: #0E61C9; width: 122px;"
                       size="medium"
                       @click="search('claer_search')">搜索更多
            </el-button>
            <el-button class="buttonStyle" type="primary" style=" background-color: #31C97E; width: 122px;"
                       size="medium"
                       @click="openHandleDialog">查看用户手册
            </el-button>
          </el-col>
        </template>
        <template v-else>
          <el-col :span="19">
            <el-row :gutter="20">
              <el-col :span="4" style="text-align: left;padding-right: 20px">
                <el-select v-model="query.applicationName" placeholder="请选择系统名称" clearable style="width: 100%">
                  <el-option v-for="(item,index) in  systemlist" :label="item.label" :value="item.value"
                             :key="item.value+index"/>
                </el-select>
              </el-col>
              <el-col :span="4" style="text-align: right;padding-right: 20px">
                <el-input placeholder="请输入版本号"
                          v-model.trim="query.versionNum" size="medium">
                  <i slot="suffix" class="el-input__icon" style="cursor: pointer"
                     @click="searchThrottle('search_all')"></i>
                </el-input>
              </el-col>
              <el-col :span="4" style="text-align: right;padding-right: 20px">
                <el-input placeholder="请输入主题"
                          v-model.trim="query.recordTheme" size="medium">
                  <i slot="suffix" class="el-input__icon" style="cursor: pointer"
                     @click="searchThrottle('search_all')"></i>
                </el-input>
              </el-col>
              <el-col :span="4" style="text-align: right;padding-right: 20px">
                <el-input placeholder="请输入关键字"
                          v-model.trim="query.recordLabel" size="medium">
                  <i slot="suffix" class="el-input__icon" style="cursor: pointer"
                     @click="searchThrottle('search_all')"></i>
                </el-input>
              </el-col>
              <el-col :span="8" style="text-align: left;padding-right: 10px">
                <el-date-picker
                    v-model="query.searchDate"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期" size="medium">
                </el-date-picker>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="5" style="text-align: right;padding-right: 10px">
            <el-button class="btnStyle" type="primary" style="background-color: #0E61C9;line-height: 18px"
                       size="medium"
                       @click="searchThrottle('search_all')">查询
            </el-button>
            <el-button class="btnStyle" type="primary" style=" background-color: #8e91a4;line-height: 18px"
                       size="medium"
                       @click="searchThrottle('search_all_clrar')">重置
            </el-button>
          </el-col>
        </template>

      </el-col>
    </el-row>

    <el-row type="flex" justify="center">
      <el-col :span="23">
        <el-table
            ref="versionTable"
            :data="tableData"
            element-loading-background="rgb(33,39,77, 0.8)"
            height="calc(100vh - 270px)" style="width: 100%; " class="tableT" highlight-current-row v-loading="loading">
          <el-table-column type="expand" v-show="tableData">
            <template slot-scope="props">
              <EditorDteail v-model="props.row.recordContent" :min-height="120" :showType="'list'"/>
            </template>
          </el-table-column>
          <el-table-column
              label="系统名称"
              prop="applicationName"
              width="160">
            <template slot-scope="props">
              <span>{{ formatApplicationName(props.row.applicationName) }}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="版本号"
              prop="versionNum"
              min-width="120"
          >
          </el-table-column>
          <el-table-column
              label="主题"
              prop="recordTheme"
              width="160">
          </el-table-column>
          <el-table-column
              label="关键字"
              prop="recordLabel"
              min-width="160"
          >
            <template slot-scope="props">
              <el-popover trigger="hover" placement="top" :style="{ 'background-color': '#21274d !important' }">
               <span class="myTag" v-for="item in props.row.recordLabelTag" :key="item">
                                    {{ item }}
                                </span>
                <div slot="reference" class="name-wrapper">
                                <span class="myTag" v-for="item in props.row.recordLabelTag" :key="item">
                                    {{ item }}
                                </span>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
              label="发布时间"
              prop="releaseTime"
              width="120">
            <template slot-scope="props">
              <span>{{ formatDate(props.row.releaseTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
              label="操作"
              prop="operation"
              :width="isAdmin?250:80"
              align="center"
          >
            <template slot-scope="props">
              <el-button type="success" plain size="mini" @click="showDialogVisible(props.row,'detail')"
                         icon="el-icon-info">
              </el-button>
              <el-button v-if="isAdmin" type="success" plain size="mini" @click="showDialogVisible(props.row,'change')"
                         icon="el-icon-edit">
              </el-button>
              <el-button v-if="isAdmin" type="danger" plain size="mini" @click="openDelDialog(props.row.recordId)"
                         icon="el-icon-delete">
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pager">
          <el-pagination @size-change="handleSizeChange" @current-change="pagehandleCurrentChange"
                         :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pagesize"
                         layout="total, sizes, prev, pager, next, jumper" :total="totalcount"></el-pagination>
        </div>
      </el-col>
    </el-row>
    <!--添加/编辑-->
    <el-dialog ref="changeDialog" :title="titleDia" :visible.sync="adddialogVisible" width="900px" class="addP"
               :show-close="false"
               :close-on-click-modal="false"
               :before-close="addmenuClose">
      <div style="text-align: left">
        <el-form label-position="left" label-width="100px" :rules="addrule" ref="addform" :model="addform">
          <el-col :span="8">
            <el-form-item
                label="更新系统"
                prop="applicationName">
              <el-select v-model="addform.applicationName" placeholder="请选择系统名称" @change="edirSearch" clearable>
                <el-option v-for="(item,index) in  systemlist" :label="item.label" :value="item.value"
                           :key="item.value+index"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
                label="版本号"
                prop="versionNum">
              <el-input v-model.trim="addform.versionNum" placeholder="请输入版本号" @input="checkVersionNum">
                <template slot="prepend">V</template>
                <template slot="append" v-if="version">{{ '最近一次版本号为' + version }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
                label="主题"
                prop="recordTheme">
              <el-input v-model.trim="addform.recordTheme" placeholder="请输入更新主题" maxlength="50"
                        show-word-limit>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
                label="关键字"
                prop="recordLabel">
              <div style="display: flex">
                <el-row style="width: 100%;">
                  <el-col :span="10">
                    <el-input v-model.trim="addform.recordLabel" placeholder="请输入关键字" maxlength="6"
                              class="addKeyConcent_input"
                              show-word-limit
                              :disabled="!showInput"
                    >
                      <el-button slot="append" icon="el-icon-add" @click="addKeyWord">确认添加</el-button>
                    </el-input>
                  </el-col>
                  <el-col :span="14" style="padding-left: 20px">
                    <el-tag
                        :key="tag"
                        v-for="tag in dynamicTags"
                        closable
                        :disable-transitions="false"
                        @close="handleClose(tag)"
                        style="padding: 0 5px"
                    >
                      {{ tag }}
                    </el-tag>
                  </el-col>
                </el-row>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
                label="更新内容"
                prop="recordContent">
              <editor v-model="addform.recordContent" :min-height="120"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
                label="发布时间"
                prop="releaseTime">
              <el-date-picker
                  v-model="addform.releaseTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
                label="附件上传"
                prop="manualPath">
              <el-upload ref="uploader" :multiple="false" class="upload-demo"
                         :on-success="handleAvatarSuccess"
                         :before-upload="beforeAvatarUpload"
                         :show-file-list="true" :file-list="fileList" :action="upFileUrl"
                         :on-remove="handleRemove"
                         :limit="2"
              >
                <el-button size="small" type="primary">上传用户手册</el-button>
              </el-upload>
            </el-form-item>

          </el-col>
          <el-col :span="24">
            <el-form-item
                label="附件上传"
                prop="recordInstructionPath">
              <el-upload ref="uploader" :multiple="true" class="upload-demo"
                         :on-success="handleAvatarSuccess2"
                         :before-upload="beforeAvatarUpload2"
                         :on-remove="handleRemove2"
                         :show-file-list="true"
                         :action="upFileUrl"
                         :file-list="fileList1"
              >
                <el-button size="small" type="primary">上传本次说明附件</el-button>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
                <el-button class="OKButton" :disabled="adddis" @click="handleVersionAdd()">确定</el-button>
                <el-button @click="addClose" class="CancButton">取 消</el-button>
            </span>
    </el-dialog>
    <!--    删除-->
    <el-dialog title="删除更新记录" :visible.sync="deletedialogVisible" width="30%" :show-close="false" class="addP" id="deleteDialog">
      <div style="text-align: center">
        <i class="el-icon-warning" style="font-size: 20px; color: #fcb543"><span
            style="font-size: 16px; color: #333; margin-left: 12px">删除后无法恢复，确定删除该条更新记录吗？</span></i>
      </div>
      <span slot="footer" class="dialog-footer">
                <el-button class="OKButton" :disabled="deldis" @click="handleDel">确 定</el-button>
                <el-button @click="deleteClose" class="CancButton">取 消</el-button>
            </span>
    </el-dialog>

    <!--详情-->
    <el-dialog ref="detailDialog" title="查看更新记录" :visible.sync="detailDialogVisible" width="900px" class="addP"
               :show-close="false"
               :close-on-click-modal="false"
               :before-close="detailmenuClose">
      <div style="text-align: left">
        <el-form label-position="left" label-width="150px" ref="detailform" :model="detailform" id="updateDetail">
          <el-col :span="24">
            <el-form-item label="更新系统:" prop="applicationName">
              {{ formatApplicationName(detailform.applicationName) }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="版本号:" prop="versionNum">
              {{ detailform.versionNum }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="主题:" prop="recordTheme">
              {{ detailform.recordTheme }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="关键字:" prop="recordLabel">
              <span class="myTag" v-for="item in detailform.recordLabelTag" :key="item">
                  {{ item }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="更新内容:" prop="recordContent">
              <EditorDteail id="recordContent" v-model="detailform.recordContent" :min-height="30"
                            :showType="'table'"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="发布时间:" prop="releaseTime">
              {{ formatDate(detailform.releaseTime) }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="用户手册附件:" prop="powerValue">
              <div v-for="item in  fileList" :key="item.url">
                <div class="filedName">{{ item.name }} <i class="el-icon-view myIcon" @click="preview(item.url)"></i>
                  <i class="el-icon-download myIcon" @click="downLoad(detailform.recordId,item.url,item.name)"></i>
                </div>

              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="本次更新说明附件:" prop="powerValue">
              <div v-for="item in  fileList1" :key="item.url">
                <div class="filedName">{{ item.name }} <i class="el-icon-view myIcon" @click="preview(item.url)"></i>
                  <i class="el-icon-download myIcon" @click="downLoad(detailform.recordId,item.url,item.name)"></i>
                </div>

              </div>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" style="text-align: center">
                <el-button @click="detailmenuClose" class="CancButton">取 消</el-button>
            </span>
    </el-dialog>


    <el-dialog title="查看用户手册" :visible.sync="detailHandleVisible" width="1100px" class="addP" :show-close="false"
               :close-on-click-modal="false"
               :before-close="detailHandleClose" id="hadnleId">
      <el-row type="flex" justify="center" class="search2">
        <el-col :span="24">
          <el-row type="flex" justify="space-between">
            <el-col :span="24">
              <el-select v-model="query2.applicationName" placeholder="请选择系统名称" @change="search2" clearable>
                <el-option v-for="(item,index) in  systemlist" :label="item.label" :value="item.value"
                           :key="item.value+index"/>
              </el-select>
              <el-input style="width: 306px;padding-left: 20px" placeholder="请输入主题" @keyup.enter.native="search2"
                        v-model.trim="query2.recordTheme">
                <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer"
                   @click="search2"></i>
              </el-input>
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <el-row type="flex" justify="center">
        <el-col :span="24">
          <el-table
              v-loading="loading2"
              element-loading-background="rgb(33,39,77, 0.8)"
              :data="tableData2"
              height="calc(100vh - 270px)" style="width: 100%;"
              id="haneleDetailId"
          >
            <el-table-column
                label="系统名称"
                prop="applicationName"
                align="center"
                min-width="140">
              <template slot-scope="props">
                <span>{{ formatApplicationName(props.row.applicationName) }}</span>
              </template>
            </el-table-column>
            <el-table-column
                label="手册版本号"
                prop="versionNum"
                align="center"
                min-width="100">
            </el-table-column>
            <el-table-column
                label="主题"
                prop="recordTheme"
                align="center"
                min-width="250">
            </el-table-column>
            <el-table-column
                label="上传时间"
                prop="updateTime"
                width="200"
                align="center">
              <template slot-scope="props">
                <span>{{ formatDate(props.row.updateTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column
                label="操作"
                prop="operation"
                width="80"
                align="center">
              <template slot-scope="props" v-show="tableData2.length>0">
                <i class="el-icon-view myIcon" @click="preview(props.row.manual.filePath)"></i>
                <i class="el-icon-download myIcon"
                   @click="downLoad(props.row.recordId,props.row.manual.filePath,props.row.manual.attachName)"></i>
              </template>
            </el-table-column>
          </el-table>

        </el-col>
      </el-row>
      <div class="pager">
        <el-pagination @size-change="handleSizeChange2" @current-change="pagehandleCurrentChange2"
                       :current-page="currentPage2" :page-sizes="[5, 10, 15]" :page-size="pagesize2"
                       layout="total, sizes, prev, pager, next, jumper" :total="totalcount2"></el-pagination>
      </div>
      <span slot="footer" class="dialog-footer" style="text-align: center;margin-top: 10px">
                <el-button @click="detailHandleClose" class="CancButton">关 闭</el-button>
            </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  checkUser,
  delRecord, getPreviewPath,
  getRecordList,
  versionAdd, versionAddFiles, versionAddTimes,
  versionDetail,
  versionEdir,
  versionSearch
} from "../../api/sino/versionUpdateRecords";
import Editor from '@/components/Editor';
import EditorDteail from '@/components/Editor/detail.vue';

import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import moment from 'moment';
import {Base64} from 'js-base64';


export default {
  name: "versionUpdateRecords",
  components: {
    Editor,
    EditorDteail
  },
  data() {
    var validateRecordLabel = (rule, value, callback) => {
      if (!this.dynamicTags.length > 0 && !this.dynamicTags.length>0) {
        callback(new Error('请添加关键字'));
      } else {
        callback();
      }
    };
    var validateRecordVersion = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入版本号'));
      } else callback();
      // else {
      //   checkVersionIsExist({
      //     applicationName: this.addform.applicationName,
      //     versionNum: this.addform.versionNum
      //   }).then(res => {
      //     if (res.message == '该版本已存在！') {
      //       callback(new Error('输入版本已存在，请重新输入'));
      //     } else callback();
      //   })
      // }
    };
    return {
      isAllSerarch: false,
      isAdminList: ['zhangdd01', 'admin', 'shenpp', 'xuhui', 'yuqzh', 'hely', 'zhangchong', 'dingling', 'tangjie01', 'huangyu01', 'hanzj', 'liubing', 'lizhiyu', 'yezf', 'liuxiong', 'dzhq'],
      isAdmin: false,
      currentRow: null,
      //版本更新列表
      tableData: [{
        applicationName: '',
        versionNum: '',
        recordTheme: '',
        recordLabel: '',
        releaseTime: '',
      }],
      //用户手册列表
      tableData2: [{
        applicationName: '',
        versionNum: '',
        recordTheme: '',
        recordLabel: '',
        releaseTime: '',
      }],
      //版本更新列表查询参数
      query: {
        allName: '',
        applicationName: '',
        recordTheme: '',
        recordLabel: '',
        versionNum: '',
      },
      //用户手册列表查询参数
      query2: {
        allName: '',
        applicationName: '',
        recordTheme: '',
        recordLabel: '',
        versionNum: '',
        startDate: '',
        endDate: ''
      },
      //版本更新详情页数据
      detailform: {
        applicationName: '',
        versionNum: '',
        recordTheme: '',
        recordLabel: '',
        recordContent: '',
        releaseTime: '',
        recordInstructionPath: '',
        manualPath: ''
      },
      //版本更新loading
      loading: false,
      //用户手册loading
      loading2: false,
      currentPage: 1,
      pagesize: 10,
      currentPage2: 1,
      pagesize2: 10,
      searchword: "",
      totalcount: null,
      totalcount2: null,
      userList: [],
      appList: [],
      powerType: 0,
      adddialogVisible: false,
      detailDialogVisible: false,
      detailHandleVisible: false,
      //系统列表
      systemlist: [
        {value: "OauthSys", label: "一体化平台管理系统"},
        {value: "EOSS", label: "神州新桥EOSS"},
        {value: "CRM", label: "销售机会管理系统"},
        {value: "PMS", label: "项目管理系统"},
        {value: "EHR", label: "人力资源管理系统"},
        {value: "RAMS", label: "报销审批系统"},
        {value: "NTDH-EOSS", label: "南通东华EOSS"},
        {value: "RMS", label: "报销管理系统"},
        {value: "TRAIN", label: "培训系统测试"},
        {value: "TRAINMGT", label: "培训管理系统测试"},
        {value: "TSM", label: "人才服务管理系统"},
        {value: "TRAINC", label: "培训系统"},
        {value: "TRAINMGTC", label: "培训管理系统"},
        {value: "DM", label: "运营管理系统"},
        {value: "FMS", label: "财务管理系统"},
        {value: "FMSC", label: "财务测试系统"},
        {value: "WeCom-SalesReport", label: "企业微信_销售额简报"},
        {value: "WeCom-FastReimbursement", label: "企业微信-快捷报销"},
        {value: "WeCom-Questionnaire", label: "企业微信-新桥问卷"},
        {value: "WeCom-MeetingRoomReservation", label: "企业微信-会议室预定"},
        {value: "WeCom-MeetingRoomManagement", label: "企业微信-会议室管理"},
        {value: "WeCom-ContractQuery", label: "企业微信-合同查询"},
        {value: "WeCom-ContractCollection", label: "企业微信-合同回款"},
        {value: "WeCom-LeaveApplication", label: "企业微信-请假申请"},
        {value: "WeCom-InternalRecommendation", label: "企业微信-内部推荐"},
        {value: "WeCom-SalaryQuery", label: "企业微信-薪资查询"},
        {value: "WeCom-NetworkResourceManagement", label: "企业微信-网络资源管理"}
      ],
      //添加 or  修改表单数据
      addform: {
        applicationName: "",
        versionNum: '',
        recordTheme: '',
        recordLabel: '',
        recordContent: '',
        releaseTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        recordInstructionPath: '',
        manualPath: ''
      },
      //弹框标题
      titleDia: '新增更新记录',
      //添加 or  修改表单验证规则
      addrule: {
        applicationName: [
          {required: true, message: "请选择更新系统", trigger: "change"},
        ],
        versionNum: [
          {required: true, validator: validateRecordVersion, trigger: "blur"},
        ],
        recordTheme: [
          {required: true, message: "请输入主题", trigger: "blur"},
        ],
        recordLabel: [
          {required: true, validator: validateRecordLabel, trigger: ['change', 'blur']}
        ],
        recordContent: [
          {required: true, message: "请输入更新内容", trigger: ['change', 'blur']},
        ],
        releaseTime: [
          {required: true, message: "请选择发布时间", trigger: "change"},
        ],
        manualPath: [
          {required: true, message: "请上传用户手册", trigger: "change"},
        ],
        recordInstructionPath: [
          {required: true, message: "请上传本次版本更新说明附件", trigger: "change"},
        ]
      },
      adddis: false,//新增按钮状态
      deldis: false,//删除按钮状态
      detaildis: false,//详情确定 按钮状态
      deletedialogVisible: false,//删除弹出框,
      selectedId: '',
      version: '',//查询最新版本号
      dynamicTags: [],//关键字标签
      upFileUrl: '/ssoapi/record/upload',//附件上传地址
      fileList: [], //回显 用户手册
      fileList1: [], //回显 说明文件
      fileList2: [],//添加说明文件 参数
      fileList3: [], //添加用户手册 参数
      releaseTimeRule: '',
      pickerOptions: { //时间选择器禁止时间控制
        disabledDate: (time) => {
          if (this.releaseTimeRule) {
            return time.getTime() < new Date(this.releaseTimeRule).getTime() - 3600 * 1000 * 24;
          } else return false
        }
      },
      canClearDate: 0,
      canChangeIcon: 0,
      lastClickTime: 0,
      throttleInterval: 2000, // 设置节流时间间隔为2秒
    }
  },
  // 利用watch  监听this.dynamicTags  如果dynamicTags 长度超过5   this.$message提示最多添加5个关键字
  computed: {
    showInput() {
      return this.dynamicTags.length < 5;
    }
  },
  watch: {
    'addform.versionNum': function (newVal) {
      if (newVal) {
        this.addform.versionNum = newVal.replace(/^\.|[^.\d]/g, '');
      }
    },
    'addForm.recordContent': function (newVal) {
      if (newVal) {
        if (this.$refs.addform) {
          this.$refs.addform.validateField('recordContent', () => {
            return true
          })
        }
      }
    },
    dynamicTags() {
      if (this.dynamicTags.length > 5) {
        this.$message({
          message: '最多添加5个关键字',
          type: 'warning'
        });
      }
      if (this.dynamicTags.length > 0) {
        if (this.$refs.addform) {
          this.$refs.addform.validateField('recordLabel', () => {
            return true
          })
        }
      }
    },
    canChangeIcon() {
      // if (this.canChangeIcon >0 ) {
      this.changeIcon()
      // }
    }
  },
  created() {
    //权限校验
    this.setIsAdminState()
    //列表页获取
    this.search('search')
  },
  mounted() {
    this.changeIcon()
  },
  beforeDestroy() {
    this.canChangeIcon = 0
  },
  methods: {
    // 系统名称回显
    formatApplicationName(val) {
      let Str = ''
      this.systemlist.map((item) => {
        // 系统名称回显
        if (item.value == val) {
          Str = item.label
        }
      })
      return Str
    },
    //节流
    throttle(func, delay) {
      let timer = null;
      return function (...args) {
        if (!timer) {
          func.apply(this, args);
          timer = setTimeout(() => {
            timer = null;
          }, delay);
        }
      };
    },
    //修改table列表展开图标样式
    changeIcon() {
      this.$nextTick(() => {
        const elements = Array.from(this.$refs.versionTable.$el.querySelectorAll('.el-icon-arrow-right'));
        // 遍历元素并修改类名
        elements.forEach(element => {
          element.classList.remove('el-icon-arrow-right');
          element.classList.add('el-icon-d-arrow-right');
        });
      });
    },
    //权限校验
    setIsAdminState() {
      checkUser(window.localStorage.getItem("Susername")).then(res=>{
        if( res.data == 1 ){
          this.isAdmin = true
        }
        else this.isAdmin = false
      })
      // this.isAdmin = this.isAdminList.includes(window.localStorage.getItem("Susername")) > 0
    },
    //版本号校验
    checkVersionNum() {
      const value = this.addform.versionNum;
      const newValue = value.replace(/^\.|[^.\d]/g, '');
      if (value !== newValue) {
        this.addform.versionNum = newValue;
      }
    },

    //查询置空
    handResertSearch() {
      this.query = {
        allName: '',
        applicationName: '',
        recordTheme: '',
        recordLabel: '',
        versionNum: '',
        searchDate: []
      }
    },
    //版本列表查询（带防抖）
    searchThrottle(type) {
      this.loading = true;
      if (type == 'close_search_all') {
        this.isAllSerarch = false;
      }
      // 使用箭头函数确保函数内部的 this 指向正确
      const debounceSearch = () => {
        this.search(type);
      };
      // 清除上一次的定时器
      clearTimeout(this.timer);
      // 设置新的定时器
      this.timer = setTimeout(debounceSearch, this.throttleInterval);
    },
    //版本列表查询
    search(type) {
      this.currentPage = 1;
      switch (type) {
        case 'search': {
          this.getListDate()
          break;
        }
        case 'claer_search': {
          this.query.allName = ''
          this.isAllSerarch = true
          break;
        }
        case 'search_all': {
          this.query.allName = ''
          this.getListDate()
          break;
        }
        case 'close_search_all': {
          this.isAllSerarch = false;
          this.handResertSearch()
          this.getListDate()
          break;
        }
        case 'search_all_clrar': {  //重置
          this.handResertSearch()
          this.getListDate()
          break;
        }
      }
    },
    //用户手册查询
    search2() {
      this.currentPage2 = 1;
      this.getListDate2();
    },
    //修改表格分页的展示条数
    handleSizeChange(val) {
      this.pagesize = val;
      this.getListDate();
    },
    //修改表格分页的当前页
    pagehandleCurrentChange(val) {
      this.currentPage = val;
      this.getListDate();
    },

    //修改表格分页的展示条数
    handleSizeChange2(val) {
      this.pagesize2 = val;
      this.getListDate2();
    },
    //修改表格分页的当前页
    pagehandleCurrentChange2(val) {
      this.currentPage2 = val;
      this.getListDate2();
    },
    // 修改表格分页的展示条数
    handleCurrentChange(val) {
      this.currentRow = val;
    },

    // 添加弹框取消
    addmenuClose() {
      this.adddialogVisible = false;
      this.adddis = false;
      this.$refs.addform.resetFields();
      this.handleReset()
      this.powerType = 0
    },
    // 删除弹框取消
    deleteClose() {
      this.deletedialogVisible = false;
      this.deldis = false;
    },
    //更新记录详情
    showDialogVisible(row, type) {
      this.handleReset()
      if (row.manual) {
        this.fileList = [{
          'name': row.manual.attachName,
          'url': row.manual.filePath
        }]
      }
      if (row.instruction.length > 0) {
        this.fileList1 = row.instruction.map(item => {
          return {
            'name': item.attachName,
            'url': item.filePath
          }
        })
      }
      versionDetail(row.recordId).then(res => {
        if (type == 'change') {
          this.addform = res.data
          this.addform.versionNum = res.data.versionNum.split('V')[1].toString()
          this.dynamicTags = res.data.recordLabel.split(",")
          // let ll = res.data.recordLabel.split(",")
          this.addform.recordLabel = ''
          this.addform.releaseTime = res.data.releaseTime
          this.titleDia = '编辑更新记录'
          this.adddialogVisible = true
        } else {
          this.detailDialogVisible = true
          this.detailform = res.data
          this.detailform.versionNum = res.data.versionNum.split('V')[1].toString()
          this.detailform.recordLabelTag = res.data.recordLabel.split(",")
        }
      })
    },
    //时间格式化
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD');
    },
    //获取列表数据
    getListDate() {
      this.loading = true;
      let param = {
        ...this.query, pageNum: this.currentPage,
        pageSize: this.pagesize,
      }

      if (this.query.searchDate) {
        param.startDate = this.query.searchDate[0] ? this.formatDate(this.query.searchDate[0]) : ''
        param.endDate = this.query.searchDate[1] ? this.formatDate(this.query.searchDate[1]) : ''
      } else {
        param.startDate = ''
        param.endDate = ''
      }
      getRecordList(param).then(res => {
        res.data.recordList.map(item => {
          item.recordLabelTag = item.recordLabel.split(",")
        })
        this.tableData = res.data.recordList;
        this.totalcount = res.data.totalCount;
        this.loading = false;
        this.canChangeIcon++
      })
    },
    //获取列表数据
    getListDate2() {
      this.loading2 = true
      getRecordList({
        ...this.query2, pageNum: this.currentPage2,
        pageSize: this.pagesize2,
      }).then(res => {
        this.tableData2 = res.data.recordList;
        this.totalcount2 = res.data.totalCount;
        this.loading2 = false
      })
    },

    //关闭更新详情弹框
    detailmenuClose() {
      this.detailDialogVisible = false
    },
    //关闭用户手册弹框
    detailHandleClose() {
      this.handleResetHandle()
      this.detailHandleVisible = false
    },
    //打开删除弹框
    openDelDialog(id) {
      this.selectedId = id
      this.deletedialogVisible = true;
    },

    //删除列表数据
    handleDel() {
      this.deletedialogVisible = false;
      this.deldis = true;
      delRecord(this.selectedId).then(res => {
        if (res.code == 200) {
          this.currentPage = 1;
          this.getListDate();
          this.$message({
            message: "删除成功！",
            type: "success",
          });
          this.deldis = false;
          this.selectedId = '';//取消选中
          this.deletedialogVisible = false;
        } else {
          this.deletedialogVisible = false;
          this.deldis = false;
          this.$message.error({
            message: res.message || "删除失败！",
          });
        }
      })
    },
    //打开添加弹框
    openAddDialog() {
      this.handleReset()
      this.adddialogVisible = true;
      this.titleDia = '新增更新记录'
    },
    //打开手册弹框
    openHandleDialog() {
      this.handleReset()
      this.detailHandleVisible = true
      this.getListDate2()
    },
    //版本号查询
    edirSearch(val) {
      if (this.titleDia == '编辑更新记录') {
        this.version = ''
      } else {
        versionSearch(val).then(res => {
          if (res.data) {
            this.version = res.data.versionNum || ''
            this.releaseTimeRule = res.data.releaseTime || ''
            // if (this.titleDia == '新增更新记录') {
            //   this.addform.releaseTime = ''
            // } else {
            //   if (this.counter > 0) {
            //     this.addform.releaseTime = '';
            //   }
            //   this.counter = 1;
            // }
          } else {
            this.version = ''
            this.releaseTimeRule = ''
          }
        })
      }

    },
    //关闭标签
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
      if (this.dynamicTags.length == 0) {
        this.addform.recordLabel = ''
      }
    },
    // 预览
    preview(path) {
      getPreviewPath(path).then(res => {
        let url = res.data
        url = "https://ehr.sino-bridge.com:8086/preview/onlinePreview?url=" + encodeURIComponent(Base64.encode(url));
        // if (window.location.href.indexOf('127.0.0.1') > -1 || window.location.href.indexOf('**************') > -1
        //     || window.location.href.indexOf('**************') > -1 || window.location.href.indexOf('localhost') > -1) {
        //   url = "https://ehr.sino-bridge.com:8086/preview/onlinePreview?url=" + encodeURIComponent(Base64.encode(url));
        //
        // } else {
        //   url = "https://ehr.sino-bridge.com:8086/preview/onlinePreview?url=" + encodeURIComponent(Base64.encode(url));
        // }
        window.open(url);
      })
    },
    //添加记录
    handleVersionAdd() {
      this.$refs.addform.validate((valid) => {
        if (valid) {
          let recordLabel = this.dynamicTags.length > 0 ? this.dynamicTags.join(',') : ''
          this.addform.versionNum = this.addform.versionNum.includes('V') > 0 ? this.addform.versionNum : 'V' + this.addform.versionNum
          let SysRecordInfo = {
            ...this.addform, userName: window.localStorage.getItem("Susername")
          }
          SysRecordInfo.recordLabel = recordLabel
          this.adddis = true
          let isChange = this.titleDia == '编辑更新记录'
          if (isChange) {
            versionEdir(SysRecordInfo).then(res => {
              if (res.code == 200) {
                this.currentPage = 1;
                this.getListDate();
                this.$message({
                  message: "版本更新记录修改成功！",
                  type: "success",
                });
                this.adddis = false;
                this.adddialogVisible = false;
              } else {
                this.addform.recordLabel = ''
                this.adddis = false;
                this.$message.error({
                  message: res.message || "版本更新记录修改失败！",
                });
              }
            })
          } else {

            //版本添加
            versionAdd(SysRecordInfo).then(res => {
              if (res.code == 200) {
                this.currentPage = 1;
                this.getListDate();
                this.$message({
                  message: "版本更新记录添加成功！",
                  type: "success",
                });
                this.adddis = false;
                this.adddialogVisible = false;
              } else {
                this.adddis = false;
                this.$message.error({
                  message: res.message || "版本更新记录添加失败！",
                });
              }
            })
          }
          //说明附件
          versionAddFiles(this.fileList2).then(() => {
            this.adddis = false;
          })
          //用户手册
          versionAddFiles(this.fileList3).then(() => {
            this.adddis = false;
          })
        }
      })
    },
    //关闭 添加弹框
    addClose() {
      this.adddialogVisible = false;
      this.adddis = false;
      this.$refs.addform.resetFields();
      this.fileList = [];
          this.fileList1 = [];
          this.fileList2 = [];
          this.fileList3 = []
    },

    //置空操作
    handleReset() {
      this.addform = {
        applicationName: "",
        versionNum: '',
        recordTheme: '',
        recordLabel: '',
        recordContent: '',
        releaseTime: moment().format('YYYY-MM-DD HH:mm:ss'),
        recordInstructionPath: '',
        manualPath: ''
      };
      this.detailform = {
        applicationName: "",
        versionNum: '',
        recordTheme: '',
        recordLabel: '',
        recordContent: '',
        releaseTime: '',
        recordInstructionPath: '',
        manualPath: ''
      }
      this.fileList = []
      this.fileList1 = []
      this.dynamicTags = []
      this.releaseTimeRule = ''
      this.$nextTick(() => {
        if (this.$refs.addform) {
          this.$refs.addform.resetFields();

        }
      })
      this.counter = 0
      this.version = ''
    },
    //用户手册查询置空
    handleResetHandle() {
      this.query2 = {
        allName: '',
        applicationName: '',
        recordTheme: '',
        recordLabel: '',
        versionNum: '',
        startDate: '',
        endDate: ''
      }
      this.tableData2 = [{
        applicationName: '',
        versionNum: '',
        recordTheme: '',
        recordLabel: '',
        releaseTime: '',
      }]
    },
    deleteActiveTag(id) {
      this.activeTags.forEach((v, k) => {
        if (v.id == id) {
          this.activeTags.splice(k, 1);
        }
      });
    },
    handleAvatarSuccess(res, file) {
      this.addform.manualPath = res.data
      if (this.addform.manualPath) {
        if (this.$refs.addform) {
          this.$refs.addform.validateField('manualPath', () => {
            return true
          })
        }
      }
      let param = {
        attachName: file.name,
        filePath: res.data,
        userName: window.localStorage.getItem("Susername")
      }
      this.fileList.shift();
      this.fileList.push({
        name: file.name,
        url: res.data,
      });
      this.fileList3.shift();
      this.fileList3.push(param);
    },
    beforeAvatarUpload(file) {
      const isDocument = file.type === 'application/pdf' || file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      if (!isDocument) {
        this.$message.error('上传的文件只能是pdf、doc或docx格式!');
      }
      return isDocument;
    },
    handleRemove(file) {
      this.addform.manualPath = ''
      this.fileList3 = this.fileList3.filter(item => item.filePath != file.url);
    },
    handleAvatarSuccess2(res, file) {
      if (res.data !== this.addform.recordInstructionPath && res.data) {
        if (this.addform.recordInstructionPath) {
          this.addform.recordInstructionPath += ',' + res.data;

        } else {
          this.addform.recordInstructionPath = res.data;
        }
      }
      if (this.addform.recordInstructionPath) {
        if (this.$refs.addform) {
          this.$refs.addform.validateField('recordInstructionPath', () => {
            return true
          })
        }
      }
      let param = {
        attachName: file.name,
        filePath: res.data,
        userName: window.localStorage.getItem("Susername")
      }
      this.fileList2.push(param)
    },
    beforeAvatarUpload2(file) {
      const isDocument = file.type === 'application/pdf' || file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      if (!isDocument) {
        this.$message.error('上传的文件只能是pdf、doc或docx格式!');
      }
      return isDocument;
    },
    handleRemove2(file) {
      let list = this.addform.recordInstructionPath.split(",");
      if(file.response){
        list = list.filter(item => item != file.response.data );
        this.addform.recordInstructionPath = list.join(",")
        this.fileList2 = this.fileList2.filter(item => item.filePath != file.response.data );
      }else{
        list = list.filter(item => item != file.url);
        this.addform.recordInstructionPath = list.join(",")
        this.fileList2 = this.fileList2.filter(item =>  item != file.url);
      }
    },

    addKeyWord() {
      if (!this.dynamicTags.includes(this.addform.recordLabel) && this.addform.recordLabel) {
        this.dynamicTags.push(this.addform.recordLabel);
      }
    },
    downLoad(id, url, name) {
      if (id != '' && id != null && id != undefined) {
        versionAddTimes(id).then(res => {
          if (res.code == 200) {
            this.getListDate2()
          }
        })
      }
      const xhr = new XMLHttpRequest();
      xhr.open('GET', '/ssoapi/record/download?id=' + url, true);
      xhr.responseType = 'blob';
      xhr.onload = function () {
        if (this.status === 200) {
          const url = window.URL.createObjectURL(this.response);
          const a = document.createElement('a');
          a.href = url;
          a.download = name;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        }
      };
      xhr.onerror = function () {
        console.error('File download failed.');
      };
      xhr.send();

    },
  }
};
</script>

<style scoped lang="less">
.tab {
  background-color: #21274D;
  margin: -20px 0;
  height: calc(100vh - 90px);

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */

  ::-webkit-scrollbar-track {
    background: #21274D;
  }

  /* Handle */

  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.pager {
  margin-top: 20px;
}

.pager {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-top: 10px;
  margin-bottom: 10px;
}

.el-pagination {
  float: right;
}

.title {
  height: 66px;
  line-height: 66px;
  /* border-bottom: 1px solid #eee; */
  /*margin-bottom: 10px;*/

  /deep/ .el-input .el-input__inner {
    //border-radius: 34px !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(199, 199, 199, 1) !important;
    border: 0px;

  }
}

.search2 {
  height: 60px;
  line-height: 60px;
}

.search {
  height: 80px;
  line-height: 40px;
  //border-bottom: 1px solid #eee;
  margin-bottom: 15px;

  .searchBoxBg {
    background: #383d5f;
  }

  .searchBox {
    padding: 20px 20px;
    position: relative;

    .closeSearch {
      position: absolute;
      display: inline-block;
      top: 10px;
      right: 10px;
      font-size: 15px;
      color: white;
      z-index: 999;
    }

  }

  /deep/ .el-input .el-input__inner, .el-range-editor.el-input__inner, .el-range-editor.el-range-input, .el-range-editor.el-range-separator {
    //border-radius: 34px !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(199, 199, 199, 1) !important;
    border: 0px;
  }

  /deep/ .el-range-input {
    //border-radius: 34px !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(199, 199, 199, 1) !important;
    border: 0px;
  }

  /deep/ .el-range-separator {
    //border-radius: 34px !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(199, 199, 199, 1) !important;
    border: 0px;
  }
}

.add,
.edit,
.delete {
  color: #fff;
  margin-left: 15px;
  border: none;
}

.add {
  background-color: #037cb3;
}

.edit {
  background-color: #17b3d9;
}

.delete {
  background-color: #04a6ef;
}

.tip {
  background-color: #f6f4f5;
  color: #898989;
  font-size: 18px;
  height: 44px;
  line-height: 44px;
  padding-left: 18px;
}

.basicinfo,
.moreinfo {
  float: left;
  width: 49%;
  height: 30px;
  color: #333;
  margin-bottom: 5px;
  border-bottom: 1px solid #f4f4f4;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  font-family: "microsoft yahei";
  font-size: 16px;
}

.basicinfo {
  border-right: 1px solid #f4f4f4;
}

.account {
  color: #389ae2 !important;
  font-weight: bold;
}

.tree_img {
  display: inline-block;
  width: 16px;
  height: 18px;
  vertical-align: middle;
}

.tree_icon {
  background: url("../../assets/images/tree_icon.png") no-repeat -40px -40px;
}

.tree-folder {
  background: url("../../assets/images/tree_icon.png") no-repeat 0px -40px;
}

.tree-folder-open {
  background: url("../../assets/images/tree_icon.png") no-repeat -19px -40px;
}

.sure {
  background: none repeat scroll 0 0 #61b0e9;
  border-color: #389ae2;
  color: #fff;
  text-shadow: none;
  text-transform: uppercase;
}

/* 13寸 */
@media screen and (min-width: 600px) and (max-width: 1300px) {
  .tableT {
    margin-bottom: -5px
  }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
  .tableT {
    margin-bottom: 30px
  }
}

/deep/ .el-table, .el-table__expanded-cell {
  background-color: #21274d !important;
}

/deep/ .el-table__expanded-cell[class*=cell] {
  background-color: #202144 !important;
  padding: 0;
}

.el-table__expanded-cell[class*=cell]:hover {
  background-color: #202144 !important;
}

#openUl {
  background: #202144;
  color: #dce5ec;
  padding: 10px 60px;
  list-style-type: disc !important;

  ul {
    list-style-type: disc !important;
    padding: 10px 75px;

    li {
      background: #202144;
      height: 20px;
      line-height: 20px;
      margin: 5px 0px !important;
    }
  }

  p {
    padding: 10px 60px;
  }

}

#openUl2 {
  padding-left: 20px;
  list-style-type: disc !important;

  ul {
    list-style-type: disc !important;
    padding: 10px 75px;

    li {
      height: 20px;
      line-height: 20px;
      margin: 5px 0px !important;
    }
  }

  p {
    padding: 10px 60px;
  }
}

.myTag {
  display: inline-block;
  height: 15px;
  line-height: 15px;
  padding: 2px 10px;
  background: transparent;
  border: 1px solid #0e61c9;
  color: #0e61c9;
  margin: 0 2px;
}

/deep/ .addP .el-dialog__footer {
  text-align: center !important;
}

/deep/ .el-tag + .el-tag {
  margin-left: 10px;
}

/deep/ .addKeyConcent_input .el-input-group--append .el-input__inner, .el-input-group__prepend {
  width: 150px;
}

ul {
  list-style-type: disc !important;
}

/deep/ #hadnleId .el-table__body-wrapper {
  background: white;
}

/deep/ #hadnleId .el-table__body tr:hover > td {
  background: aliceblue !important;
}

/deep/ #hadnleId .el-pagination .el-pagination__total, .el-pagination .el-pagination__jump {
  color: black !important;
}

/deep/ #hadnleId .el-pagination__jump {
  color: black !important;

}

/deep/ #hadnleId .el-pagination .el-select .el-input .el-input__inner, .el-pager li, .el-pagination .btn-next, .el-pagination .btn-prev {
  color: black !important;
  background-color: white !important;
}

/deep/ #hadnleId .el-pagination__editor.el-input .el-input__inner {
  color: black !important;
  background-color: white !important;
}

/deep/ #hadnleId .el-pager li {
  background-color: white !important;
  color: black !important;
}

/deep/ #hadnleId .el-pager li.active {
  background-color: white !important;
  color: #409eff !important;
}

/deep/ #hadnleId .el-pagination .btn-next {
  background-color: transparent !important;
  color: #303133 !important;
}

/deep/ #hadnleId .el-pagination .btn-prev {
  background-color: transparent !important;
  color: #303133 !important;
}

/deep/ #hadnleId .el-pagination button:disabled {
  color: #C0C4CC !important;
  background-color: transparent !important;
}

/deep/ #hadnleId .el-table, .el-table__expanded-cell {
  background-color: white !important;
}


/deep/ .el-loading-mask {
  background-color: #21274d !important;
}

/deep/ #haneleDetailId .el-loading-mask {
  background-color: rgba(255, 255, 255, 1) !important;
}

/deep/ #hadnleId th {
  background-color: #eaeef3;
  color: black;
}

#version #openUl > ul {
  list-style: disc !important;
}

.myIcon {
  color: #409eff;
  padding: 0 5px;
  cursor: pointer;
}

.name-wrapper {
  white-space: nowrap; /* 保持文本在一行内 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
  width: 100%; /* 设置容器宽度，可以根据需要调整 */
}

/deep/ .el-icon-d-arrow-right {
  font-size: 15px !important;
  color: white !important;
}

/deep/ #recordContent .ql-editor {

}

/deep/ #recordContent li {
  padding: 0px 0px !important;
  line-height: 25px;
}

/deep/ #recordContent .ql-editor ol {
  padding-left: 30px !important;
}

/deep/ #recordContent .ql-editor ul {
  margin-left: -42px !important;
  margin-top: -5px;
}

/deep/ #recordContent .ql-editor p {
  padding-left: 0px !important;
}

/deep/ #recordContent .ql-editor li:not(.ql-direction-rtl)::before {
  color: #d8d8d8;
  font-size: 38px;
  vertical-align: middle; /* 调整垂直对齐方式 */
}

/deep/ strong {
  font-weight: bold !important;
}

.filedName {
  color: #939393;
}

/deep/ .addP .el-form-item__label {
  color: #939393;
}

/deep/ .search .el-range-editor.el-input__inner {
  width: 100%;
}

/deep/  #updateDetail{
  .el-form-item{
    margin: 0;
  }
}
/deep/ .el-upload-list{
  width: 35%;
}

/deep/ #deleteDialog .el-dialog__body{
  padding: 60px 70px 50px;
}

</style>
