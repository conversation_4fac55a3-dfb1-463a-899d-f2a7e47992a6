export const setCookie = (name, value, exdays) => {
  let exdate = new Date(); //获取时间
  exdate.setTime(exdate.getTime() + 24 * 60 * 60 * 1000 * exdays);
  window.document.cookie = name + '=' + value + ';path=/;expires=' + exdate.toGMTString();
}


export const clearCookie = (name) => {
  setCookie(name, '', -1)
}

export const getCookie = (name) => {
  let cookie = document.cookie;
  let tooken = '';
  if (cookie) {
    let arr = cookie.split("; ");
    arr.forEach(v => {
      let arr2 = v.split("=");
      if (arr2[0] == name) {
        tooken = arr2[1]
      }
    })
  }
  return tooken;
}


