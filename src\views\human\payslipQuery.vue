<template>
    <div class="salary">
        <el-row type="flex" justify="center" class="title">
            <el-col :span="23">
                <div class="title_header">工资条查询</div>
            </el-col>
        </el-row>
        <template v-if="!isLogin">
            <div class="search" style="display: flex;justify-content: center;margin-top: 10%;">
                工资查询密码：
                <div style="margin-right: 15px;"><el-input placeholder="请输入密码" v-model.trim="searchword"
                        show-password></el-input>
                    <div style="color:red;font-size: 12px;" v-if="!isok">密码错误！</div>
                </div>
                <el-button class="btnStyle" type="primary" style=" background-color: #0E61C9;" size="medium"
                    @click="checkPass">查询
                </el-button>
            </div>
        </template>
        <template v-if="isLogin">
           <el-row type="flex" justify="center" class="search">
               <el-col :span="23" style="display: flex">
                   <div style="flex:3">
                       <el-button type="primary" style="border: none;background-color:#31C97E;width: 120px;"
                           size="medium" @click="editPass">修改查询密码</el-button>
                   </div>
                   <div style="flex:1">
                        <!-- <el-date-picker v-model="searchword" type="date" placeholder="选择日期" @change="search"
                           style="width: 100%;" :clearable="false">
                        </el-date-picker> -->
                  </div>
               </el-col>
           </el-row>
            <el-row type="flex" justify="center">
                <el-col :span="23">
                    <el-table :data="tableData" class="tableT" ref="multipleTable"
                        style="width: 100%;overflow: scroll;" height="60vh" highlight-current-row>
                        <el-table-column prop="salarydate" label="日期" width="100"></el-table-column>
                        <el-table-column prop="name" label="姓名" width="100"></el-table-column>
                        <el-table-column prop="basicsalary" label="基本工资" width="100"></el-table-column>
                        <el-table-column prop="postsalary" label="岗效工资" width="100"></el-table-column>
                        <el-table-column prop="mealsallowance" label="餐补" width="100"></el-table-column>
                        <el-table-column prop="travelallowance" label="出差补助" width="100"></el-table-column>
                        <el-table-column prop="bonus" label="奖金" width="100"></el-table-column>
                        <el-table-column prop="overtimeallowance" label="加班" width="100"></el-table-column>
                        <el-table-column prop="otherallowance" label="其他补助" width="100"></el-table-column>
                        <el-table-column prop="subsidy" label="补贴" width="100"></el-table-column>
                        <el-table-column prop="otherdeductmoney" label="其他扣款" width="100"></el-table-column>
                        <el-table-column prop="beoverduedeductmoney" label="逾期扣款" width="100"></el-table-column>
                        <el-table-column prop="checkonworkattendancedeductmoney" label="考勤扣款"></el-table-column>
                        <el-table-column prop="secrecysalary" label="保密补贴" width="100"></el-table-column>
                        <el-table-column prop="secrecybonus" label="保密奖金" width="100"></el-table-column>
                        <el-table-column prop="totalsalarys" label="工资总额" width="100"></el-table-column>
                        <el-table-column prop="fiverisk" label="扣五险一金" width="100px"></el-table-column>
                        <el-table-column prop="grosssaraly" label="应发工资" width="100"></el-table-column>
                        <el-table-column prop="incometax" label="扣个税" width="100"></el-table-column>
                        <el-table-column prop="supplementtax" label="补扣/补发个税" width="120"></el-table-column>
                        <el-table-column prop="yearendbouns" label="年终奖金" width="120"></el-table-column>
                        <el-table-column prop="yearendbounstax" label="扣年奖个税" width="100"></el-table-column>
                        <el-table-column prop="realsalary" label="实发金额" width="100"></el-table-column>

                    </el-table>

                </el-col>
            </el-row>
             <el-row>
               <div class="paginator">
                 <el-pagination @size-change="handleSizeChange" @current-change="pagehandleCurrentChange"
                                :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pagesize"
                                layout="total, sizes, prev, pager, next, jumper" :total="totalcount"></el-pagination>
               </div>
             </el-row>
        </template>
        <el-dialog title="修改工资条查询密码" :visible.sync="editVisable" width="600px" :show-close="false" class="addP">

            <el-form label-position="left" label-width="100px" ref="form" :model="form">
                <el-form-item label="登录用户" prop="staffName">
                   {{ loginName || '' }}
                </el-form-item>
                <el-form-item label="新密码" prop="password"
                    :rules="{ required: true, message: '请输入新密码', trigger: 'blur' }">
                    <el-input v-model.trim="form.password" show-password>
                    </el-input>
                </el-form-item>
                <el-form-item label="重复确认" prop="newPassword"
                    :rules="{ required: true, message: '请重复确认新密码', trigger: 'blur' }">
                    <el-input v-model.trim="form.newPassword" type='password'>
                    </el-input>
                    <div style="color:red;font-size: 12px;" v-show="form.newPassword!=''&&form.password!=''&&form.newPassword != form.password">密码两次输入不一致！</div>
                </el-form-item>

            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button class="OKButton" @click="resumesend('form')">确定</el-button>
                <el-button @click="editClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {determinePwd,getAllInfo,modiSalaryPas} from "@/api/human/payslipQuery";
import moment from "moment/moment";

export default {
    name: "payslipQuery",
    data() {
        return {
            isLogin: false,
            tableData: [],
            searchword: "",
            password: '',//
            editVisable: false,//修改密码
            pagesize: 10,
            currentPage: 1,
            staffCode: '',//员工工号
            totalcount:0,
            isok:true,//密码错误
            userName: window.localStorage.getItem("Susername"),
            loginName: window.localStorage.getItem("LoginName"),
            form: {
                staffName: '',//登录用户
                password: '',//新密码
                newPassword: '',//重复确认
            }
        }
    },
    watch: {
    },
    created() {
      this.search()
    },
    methods: {
      //时间格式化
      formatDate(dateStr) {
        if(!dateStr){
          return ''
        }
        return moment(dateStr).format('YYYY-MM-DD');
      },
      //修改表格分页的展示条数
      handleSizeChange(val) {
        this.pagesize = val;
        this.search();
      },
      //修改表格分页的当前页
      pagehandleCurrentChange(val) {
        this.currentPage = val;
        this.search();
      },

      //用于变量或者对象为空时，调用方法定义的全局方法获取
        search() {
            this.getAllInofList();
        },
        getAllInofList() {
          getAllInfo({ pageSize:this.pagesize, pageNum: this.currentPage, userName: this. userName || '' }).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.data.data;
                    this.totalcount = res.data.count
                }
            });
        },
        checkPass(){
          if(!this.searchword){
            return
          }
          determinePwd({
            userName: window.localStorage.getItem("Susername"),
            passWord: this.searchword
          }).then((res)=>{
            if(res.data === 'success'){
              this.isLogin = true
              this.isok = true
              this.search()
            }
            else{
              this.isok = false
            }
          }).catch(()=>{
            this.isok = false
          })
        },
        editPass() {
            this.editVisable = true
        },
        editClose() {
            this.editVisable = false;
            this.$refs.form.resetFields();
        },
        resumesend(formname) {
            let that = this
            that.$refs[formname].validate((valid) => {
                if (valid && that.form.newPassword == that.form.password) {
                    console.log(that.form)
                    modiSalaryPas({
                        userName: window.localStorage.getItem("Susername"),
                        newPassword1: that.form.newPassword,
                        newPassword2: that.form.password
                    }).then((res) => {
                        if (res.code == 200) {
                            that.editClose()
                            that.$message({
                                message: "修改成功",
                                type: "success",
                            });
                            this.isLogin = false
                            that.searchword=''
                        }
                    });
                } else {
                    that.$message({
                        message: "请正确填写",
                        type: "warning",
                    });
                    return false;
                }
            });
        },
    },
    computed: {//同步项目中定义的全局的变量或者对象

    }
}
</script>

<style scoped lang="less">


.el-pagination {
  float: right;
}
.salary {
    background-color: #21274D;
    margin: -20px 0;
    height: calc(100vh - 90px);


    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }


}

.title {
    height: 66px;
    line-height: 66px;
}


.search {
    height: 40px;
    line-height: 40px;
    // border-bottom: 1px solid #eee;
    margin-bottom: 15px;

    /deep/ .el-input .el-input__inner {
        //border-radius: 34px !important;
        background-color: rgba(255, 255, 255, 0.25) !important;
        color: rgba(199, 199, 199, 1) !important;
        border: 0px;
        width: 306px;
    }

    /deep/ .el-input__prefix {
        right: 10px !important;
        left: auto !important
    }
}



@media screen and (min-width: 600px) and (max-width: 1300px) {

    //13寸
    .orgStyle {
        max-height: calc(100vh - 270px);
    }

    .tableT {
        margin-bottom: 0px
    }

    .custom-tree-node {
        font-size: 12px;
    }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
    .tableT {
        margin-bottom: 30px
    }
}
</style>
