<template>
    <div class="org_wrapper">
        <el-row type="flex" justify="center" class="title">
            <el-col :span="23">
                <div class="title_header">通讯录</div>
            </el-col>
        </el-row>

        <el-row type="flex" justify="center">
            <el-col :span="23">
                <div class="main_container">
                    <div class="main_right">
                        <div class="search_line">
                            <span style="color: #fff">找人：</span>
                            <el-input placeholder="输入姓名或手机号查找" @keyup.enter.native="onSearchTxtChange" v-model="searchTxt"
                                class="input-with-select" style="width:306px">
                                <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer"
                                    @click="onSearchTxtChange"></i>
                            </el-input>
                        </div>
                        <el-table v-loading="loading" element-loading-background="rgb(33,39,77, 0.8)" :data="staffList"
                            height="calc(100vh - 286px)" style="width: 100%;" current-row-key="staffId" class="tableT"
                            highlight-current-row>
                
                            <el-table-column label="员工姓名" prop="staffName" >
                            </el-table-column>
<!--                            <el-table-column label="状态" min-width="5">-->
<!--                                <template slot-scope="scope">-->
<!--                                    <span>{{ ({ 0: '离职', 1: '在职', 2: '实习' }[scope.row.state]) }}</span>-->
<!--                                </template>-->
<!--                            </el-table-column>-->
<!--                            <el-table-column prop="userName" min-width="8" label="登录账号"/>-->
                            <el-table-column prop="deptName"  label="部门"/>
                            <!-- <el-table-column prop="positionName" min-width="14" label="职务"/> -->
                            <el-table-column prop="conactPhone"  label="分机">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.conactPhone ? scope.row.conactPhone.replace(/(\d{2})\d+(\d{2})/, '$1***$2') : '-' }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="mobilePhone"  label="手机号码">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.mobilePhone ? scope.row.mobilePhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '-' }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="conactEmail" label="邮箱">
                                <template slot-scope="scope">
                                    <span>{{ scope.row.conactEmail ? scope.row.conactEmail.replace(/(.{3}).*(@.*)/, '$1***$2') : '-' }}</span>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="paginator">
                            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                                :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pageSize"
                                layout="total, sizes, prev, pager, next, jumper" :total="totalCount || 0">
                            </el-pagination>
                        </div>
                    </div>

                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { selectAllDept, getAllStaffEmp, getList } from "@/api/sino/staffmanage";

export default {
    name: "addressbook",
    data() {
        return {
            orgTreeLoading: false,
            loading: false,
            staffList: [],
            totalCount: 0,
            selectedTreeNode: {},
            orgTreeList: [],
            positionTreeList: [],
            orgId: '',
            searchTxt: '',
            currentPage: 1,
            pageSize: 10,
            defaultProps: {
                children: 'children',
                label: 'label'
            },
            filterText: '',
        }
    },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        },
    },
    created() {
        this.getStaffListLocal();//获取员工数据
        this.getDeptList();
        this.getPositionList();
        this.orgTreeLoading = true;
        this.getOrgList().then(() => {
            this.orgTreeLoading = false;
        }).catch(() => {
            this.orgTreeLoading = false;
        });
    },
    methods: {//用于变量或者对象为空时，调用方法定义的全局方法获取
        ...mapActions(['createStaff', 'getDeptList']),
        getOrgList() {
            return selectAllDept().then(res => {
                const orgTreeList = res && res.data || [];
                console.log('orgTreeList', orgTreeList);
                this.orgTreeList = orgTreeList;
            });
        },


        // 获取员工列表
        getStaffList(params) {
            try {
                this.loading = true;
                getAllStaffEmp(params).then(res => {
                    this.loading = false;
                    const data = res.data;
                    this.totalCount = data.totalCount;
                    this.staffList = data.staffSubject;
                });
            } catch (e) {
                this.loading = false;
            }
        },

        //节点被点击时的回调
        onDeptTreeNodeClick(data) {
            this.selectedTreeNode = data;
            this.orgId = data.code;
            this.currentPage = 1;
            this.getStaffListLocal();
        },

        handleSizeChange(val) {
            this.pageSize = val;
            this.getStaffListLocal();
        },
        handleCurrentChange(val) {
            this.currentPage = val;
            this.getStaffListLocal();
        },
        getPositionList(orgId) {
            getList(orgId).then(res => {
                this.positionTreeList = res && res.data || [];
            });
        },
        getStaffListLocal() {
            const qs = {
                pageSize: this.pageSize,
                pageNum: this.currentPage,
                orgId: this.orgId || '',
                search: this.searchTxt,
                state: 1
            };
            this.getStaffList(qs);
        },
        filterNode(value, data) {
            if (!value) return true;
            return data.label.indexOf(value) !== -1;//对树节点进行筛选时执行的方法
        },

        onSearchTxtChange() {
            this.currentPage = 1;
            this.getStaffListLocal();
        },
        transformForestData(arr, labelName, valueName) {
            for (let node of arr) {
                this.transformNodeData(node, labelName, valueName);
            }
        },
        transformNodeData(node, labelName, valueName) {
            if (node) {
                if (node.children && node.children.length > 0) {
                    this.transformForestData(node.children, labelName, valueName);
                }
                node.label = node[labelName];
                node.value = node[valueName];
            }
        },
    },
    computed: {//同步项目中定义的全局的变量或者对象
        ...mapState({
            deptList: 'deptList',
        }),
        transformedOrgData: function () {
            if (Array.isArray(this.orgTreeList)) {
                this.transformForestData(this.orgTreeList, 'name', 'code');
            } else {
                this.transformNodeData(this.orgTreeList, 'name', 'code');
            }
            return this.orgTreeList;
        },
    }
}
</script>

<style scoped lang="less">
.team_bg_image {
    background: url(../../assets/images/team.svg);
    background-size: cover;
    height: 25px;
    float: left;
    width: 25px;
}

.zuzhiOpen {
    background: url(../../assets/images/zuzhi-open.svg);
    background-size: cover;
    height: 15px;
    float: left;
    width: 15px;
    position: relative;
    top: 8px;
    left: 2px;
    margin-right: 5px
}



.custom-tree-node {
    font-size: 15px;
}


.org_wrapper {
    background-color: #21274D;
    margin: -20px 0;
    height: calc(100vh - 90px);
    overflow: hidden;

    /deep/ .el-form-item {
        margin-bottom: 18px !important;
    }

    /deep/ .el-tree__empty-block {
        background-color: #21274d;
    }

    ::-webkit-scrollbar {
        width: 6px;
    }

    /*定义滑块 内阴影+圆角*/
    ::-webkit-scrollbar-thumb {
        box-shadow: inset 0 0 0px white;
        -webkit-box-shadow: inset 0 0 0px white;
        background-color: rgb(193, 193, 193);
        /*滚动条的背景颜色*/
        border-radius: 20px;
    }
}

.paginator {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-bottom: 20px;
}


.main_container {
    display: flex;

    .main_left {
        min-width: 250px;
        margin-right: 10px;

        /deep/ .el-input .el-input__inner {
            //border-radius: 54px !important;
            background-color: rgba(255, 255, 255, 0.25) !important;
            color: rgba(199, 199, 199, 1) !important;
            border: 0px
        }

    }

    ::-webkit-scrollbar {
        width: 6px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #21274D;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #888;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    .main_right {
        width: calc(100%);

        .search_line {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-bottom: 15px;


            .el-input-group.el-input-group--append {
                width: 300px;
            }
        }

        /deep/ .el-input .el-input__inner {
            //border-radius: 54px !important;
            background-color: rgba(255, 255, 255, 0.25) !important;
            color: rgba(199, 199, 199, 1) !important;
            border: 0px
        }
    }
}


.title {
    height: 66px;
    line-height: 66px;
    // border-bottom: 1px solid #eee;
    /*margin-bottom: 10px;*/
}


.orgStyle {
    max-height: calc(100vh - 270px);
    overflow: auto;
    border: 0px solid rgb(235, 238, 245)
}


@media screen and (min-width: 600px) and (max-width: 1300px) {

    //13寸
    .orgStyle {
        max-height: calc(100vh - 270px);
    }

    .tableT {
        margin-bottom: 0px
    }

    .custom-tree-node {
        font-size: 12px;
    }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
    .tableT {
        margin-bottom: 30px
    }
}
</style>
