<template>
  <div class="home" v-loading.fullscreen="loading" element-loading-text="拼命加载中" element-loading-background="rgb(33,39,77, 1)">
    <div class="cartoon">
      <!--      左侧小方块-->
      <div class="diamond-box">
        <div class="diamond dia1">
          <div class="light g-anim1"></div>
        </div>
        <div class="diamond dia2">
          <div class="light g-anim3"></div>
        </div>
        <div class="diamond dia3">
          <div class="light g-anim2"></div>
        </div>
        <!--        <img class="circle fadexk-k1" src="../assets/images/trends/circle-light.png" />-->
      </div>
      <!--      上方大屏幕-->
      <div class="screen-box shake">
        <div class="screen"></div>
        <div class="screen-light g-anim1"></div>
      </div>
      <!--      中间大圆台-->
      <div class="terrace">
        <div class="terrace-base">
          <div class="terrace-base-light g-anim3"></div>
          <div class="terrace-stage">
            <div class="box-gs">
              <img class="bimgan11" src="../assets/images/trends/source-RAMS.png"  width="80%"/>
              <img class="bimgan5" src="../assets/images/trends/source-EHR.png"  width="80%"/>
              <img class="bimgan9" src="../assets/images/trends/source-RMS.png"  width="80%"/>
              <!-- <img class="bimgan0" src="../assets/images/trends/center-light2.png"/>
              <img class="bimgan1" src="../assets/images/trends/source-CRM.png"  width="78%"/>
              <img class="bimgan2" src="../assets/images/trends/center-light1.png" />
              <img class="bimgan3" src="../assets/images/trends/center-light2.png" />
              <img class="bimgan4" src="../assets/images/trends/circle-s.png" />
              <img class="bimgan6" src="../assets/images/trends/center-light2.png" />
              <img class="bimgan7" src="../assets/images/trends/source-EOSS.png"  width="80%" />
              <img class="bimgan8" src="../assets/images/trends/circle-s.png" />
              <img class="bimgan10" src="../assets/images/trends/source-TSM.png"  width="80%"/>
              <img class="bimgan12" src="../assets/images/trends/source-PMS.png"  width="80%"/>
              <img class="bimgan13" src="../assets/images/trends/source-TRAIN.png"  width="150%"/>
              <img class="bimgan14" src="../assets/images/trends/source-FMS.png"  width="80%"/>
              <img class="bimgan15" src="../assets/images/trends/source-DM.png"  width="50%"/> -->
            </div>
          </div>
        </div>
      </div>
      <!--      右侧建筑物-->
      <div class="build-box">
        <div class="build-base"></div>
        <div class="build-light g-anim2"></div>
      </div>
    </div>
    <div class="logo-box">
      <img class="logo-img" src="../assets/images/logo1.png" alt="" height="82"/>
      <div class="logo-title">中国企联办公自动化平台</div>
    </div>
    <div class="login_message">
      <div class="form-box">
        <div class="form-container">
          <div class="pwdTitle">
            <img src="../assets/images/pwd.png" alt="logo" class="logo" />
            账号密码登录
          </div>
          <transition name="slide-fade">
            <div class="input-box">
              <el-form ref="form" :model="form" :rules="rules">
                <el-form-item prop="name">
                  <el-input v-model="form.name" placeholder="请输入用户名" prefix-icon="el-icon-user"></el-input>
                </el-form-item>
                <el-form-item prop="pwd">
                  <el-input v-model="form.pwd" placeholder="请输入密码" prefix-icon="el-icon-unlock"
                    type="password"></el-input>
                </el-form-item>
                <el-form-item prop="valid">
                  <el-input class="input-content" v-model="form.valid" placeholder="请输入验证码" prefix-icon="el-icon-lock" style="float: left"
                    @keydown.enter.native="onSubmit('form')"></el-input>
                  <img :src="randomSrc" @click="randomNum" alt="验证码获取失败"
                    style="margin-left: 10px; vertical-align: middle" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" class='signButton' :disabled="submitEnable" @click="onSubmit('form')">登 录</el-button>
                </el-form-item>
              </el-form>
            </div>
          </transition>
          <div style="text-align: right;color: #a9a9a9;cursor: pointer;" @click="toScagin">扫码登录</div>
        </div>

      </div>
    </div>
    <div class="starlist">
      <img class="star starflick" src="../assets/images/trends/star.png" />
      <img class="star1 starflick1" src="../assets/images/trends/star.png" />
      <img class="star2 starflick2" src="../assets/images/trends/star.png" />
      <img class="star3 starflick3" src="../assets/images/trends/star.png" />
      <img class="star4 starflick4" src="../assets/images/trends/star.png" />
      <img class="star5 starflick" src="../assets/images/trends/star.png" />
    </div>
  </div>
</template>

<script>
import '../assets/css/cartoon.css'
import { randomLenNum } from "@/utils/randomNum";
import { mapActions } from "vuex";
import { setCookie } from "@/utils/cookie";
import { loginIn,leavelogin } from "@/api/login/login";
import { sm2 } from "sm-crypto";
export default {
  name: "login",
  data() {
    return {
      rememberName: false,
      rememberPwd: false,
      showChange: "",
      randomSrc: "",
      randomStr: "",
      url: "",
      form: {
        name: "",
        pwd: "",
        valid: "",
      },
      rules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        pwd: [{ required: true, message: "请输入密码", trigger: "blur" }],
        valid: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      submitEnable:false,
      loading:true,
    };
  },
  created() {
    let reg = new RegExp("(^|&)" + "changeType" + "=([^&]*)(&|$)");
    let r = window.location.search.substr(1).match(reg);
    if (!!r && r[2] == "ntdh~test") {
      this.showChange = true;
    } else {
      this.showChange = false;
    }
    this.randomNum();
  },
  mounted() {

    debugger
    if (window.localStorage.getItem("Susername")) {
      this.form.name = window.localStorage.getItem("Susername");
      this.rememberName = true;
    } else {
      this.form.name = "";
    }
    if (window.localStorage.getItem("Spwd")) {
      this.form.pwd = window.localStorage.getItem("Spwd");
      this.rememberPwd = true;
    } else {
      this.form.pwd = "";
    }
    //token验证提示--自动触发
    if (window.location.href.indexOf("msg") != -1) {
      let msg = window.location.href.split("msg=")[1];
      this.$message({
        message: msg,
        type: "error",
      });
    }
    if (window.location.href.indexOf("clientid") == -1) {
      window.sessionStorage.removeItem("clientid");
    }
    if (window.location.href.indexOf("leaveOffice") != -1) {
      this.loading = true
      let userName=this.getStringBetween(window.location.href, 'userName=', '&passWord=')
      let passWord=this.getStringBetween(window.location.href, 'passWord=', '&key=')
      leavelogin({
        userName: userName,
        passWord: passWord,
        key: 'leaveOffice'
      }).then((res) => {
        if (res.code == 200) {
          setCookie("access_token", res.data.access_token, 0.125);
          setCookie("refresh_token", res.data.refresh_token, 0.125);
          window.localStorage.setItem("Susername", res.map.UserName);
          window.localStorage.setItem("LoginName", res.map.StaffName);
          this.$router.push("/sino/index");
          this.loading = false
        }else{
          this.loading = false
          this.$message({
              message: res.message,
              type: "error",
         });
        }
      });
    } else {
      this.loading = false
    }
  },
  methods: {
    ...mapActions({
      editUsername: "EDIT_USER",
    }),
    encrypt(text) {
      let publicKey =
        "042136fe42541b34a589a158382b5a2f65593c181af2b388f18745818d5a74350387e8bdd8d44828f4b94180cc4b5cd8143da5324ee9ba86a0fd7d75572a1f4e11";
  
      return "04" + sm2.doEncrypt(text, publicKey);
    },
    toScagin() {
      this.$router.replace("/scanSign");
    },
    randomNum() {
      this.randomStr = randomLenNum(4, true);
      // this.randomSrc =  `http://192.168.103.47:8099/code/${this.randomStr}`;
      this.randomSrc =  `${process.env.VUE_APP_BASE_API}/code/${this.randomStr}`;  //原始代码
      // this.randomSrc =  `${process.env.VUE_APP_BASE_API}code/${this.randomStr}`;
      // this.randomSrc =  `${process.env.VUE_APP_BASE_API}code/64461739150719503`;
    },
    getStringBetween(str, start, end) {
      const parts = str.split(start);
      if (parts.length < 2) return '';
      const middleParts = parts[1].split(end);
      return middleParts[0];
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submitEnable=true;
          loginIn({
            userName: this.form.name,
            password:  this.encrypt(this.form.pwd) ,
            // password:  this.form.pwd,
            code: this.form.valid,
            randomStr: this.randomStr,
            grant_type: "password",
            scope: "server",
            client_id: "oauth",
            client_secret: "oauth",
          }).then((res) => {
            
            if (res.code == 200) {
   
            
              setCookie("access_token", res.data.access_token, 0.125);
              setCookie("refresh_token", res.data.refresh_token, 0.125);
              this.$message({
                showClose: true,
                message: "登录成功",
                type: "success",
              });
              window.localStorage.setItem("scanLogin", false);//非扫码登录
              window.localStorage.setItem("isLogin", true);
              var test = window.location.href;
              if (test.indexOf("clientid") != -1) {
                let url = test.split("clientid=")[1];
                window.sessionStorage.setItem("clientid", url.split("#")[0]);
                window.localStorage.setItem("ifFirstLogin", true);//第一次登陆
                // createToken({
                //   userName: this.form.name,
                //   clientid: url.split("#")[0],
                // }).then((result) => {
                //   let openUrl = `${result.data.url}?token=${result.data.token}`
                //   console.log(openUrl);
                //   window.open(openUrl);
                // });
              }else{
                window.sessionStorage.removeItem("clientid");
              }
              window.localStorage.setItem("Susername", res.data.userId);
              if (res.map && res.map.data && res.map.data.name) {
                window.localStorage.setItem("LoginName", res.map.data.name);
              } else {
                window.localStorage.setItem("LoginName", res.data.userId);
              }
              if (this.rememberPwd) {
                window.localStorage.setItem("Spwd", this.form.pwd);
              }
              this.editUsername(res.data.userId);
              this.$router.push("/sino/index");
              this.submitEnable=false
            } else if (res.code == 10020) {
              this.$message({
                message: '请通过扫码登陆！',
                type: "error",
              });
              this.$router.push("/scanSign");
            }
            else {
              this.submitEnable=false;
              this.randomNum();
              this.$message({
                showClose: true,
                message: res.message,
                type: "error",
              });
            }
          }
          );
        }
      });
    },
  },
  watch: {
    form: {
      handler(val) {
        const { name, pwd } = val;
        if (this.rememberName) {
          window.localStorage.setItem("Susername", name);
        }
        if (this.rememberPwd) {
          window.localStorage.setItem("Spwd", pwd);
        }
      },
      deep: true,
    },
  },
};
</script>
<style scoped lang="less" >
.home {
  width: 100%;
  height: 100%;
  background: url("../assets/images/background/three.jpg") no-repeat;
  background-size:100% 100%;
  background-attachment:fixed;
  position: relative;
  overflow: hidden;

  .logo-box {
    display: table-cell;
    height: 64px;
    position: absolute;
    left: 4%;
    top: 6%;

    .logo-img {
      display: inline-block;
      vertical-align: middle;
    }

    .logo-title {
      height: 55px;
      vertical-align: middle;
      line-height: 55px;
      font-size: 28px;
      color: rgba(255, 255, 255, 1);
      display: inline-block;
      margin-left: 20px;
      // position: absolute;
      // top: 6.5%;
      // left: 19%;
      border-left: 1px solid rgba(255, 255, 255, 0.65);
      padding-left: 30px;
    }
  }

  .left-box {
    width: 1060px;
    height: 671px;
    background-image: url("../assets/images/3D.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    top: 16%;
  }

  .login_message {
    position: absolute;
    right: 10%;
    top: 24%;

    .title {
      width: 600px;
      font-size: 42px;
      color: #fff;
      font-family: "xingkai";
      text-align: center;
      line-height: 70px;
    }

    .form-box {
      width:90%;
      height: 410px;
      /*width: 600px;*/
      /*height: 440px;*/
      background-image: url("../assets/images/login.png");
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      justify-content: center;
      text-align: center;

      .tabType {
        position: absolute;
        font-size: 12px;
        color: #01e3fb;
        bottom: 6%;
        right: 7%;
        cursor: pointer;
        height: 15px;
        line-height: 15px;

        .typeChange {
          height: 15px;
          width: 22px;
          vertical-align: top;
          margin-right: 4px;
          display: inline-block;
        }
      }

      .form-container {
        width: 80%;

        .input-content{
          width: 60%;
        }
        .pwdTitle {
          height: 24px;
          line-height: 24px;
          font-size: 20px;
          color: #0082ef;
          text-align: center;
          margin-top: 8%;
          margin-bottom: 35px;

          .logo {
            width: 29px;
            height: 24px;
            vertical-align: top;
            margin-right: 8px;
            display: inline-block;
          }
        }

        /deep/ .el-input--prefix .el-input__inner {
          height: 46px !important;
          line-height: 46px;
          background-color: #374e78 !important;
          color: #fff;
          padding-left: 40px;
          border: 0ch;
          box-shadow: 0 0 2px #26bfd4;
        }

        /deep/ .el-input--prefix .el-input__inner:hover {
          box-shadow: 0 0 6px #26bfd4;
        }

        /deep/.el-input__icon {
          width: 35px;
          font-size: 20px;
          line-height: 46px;
        }

        /deep/ .el-button--primary {
          background: linear-gradient(#00b5fc, #027ae0);
          border: 0ch;
        }
      }
    }
  }
}
.signButton {
  margin-top: 10%;
  width: 100%;
  font-size: 18px;
  color: #fff;
}
.signButton:active{
  box-shadow: 0 0 10px #aabdff;
}
.iframeDiv {
  width: 78%;
  height: 100%;
  padding-top: 15px;
  overflow: hidden;
}

.iframe {
  width: 100%;
  height: 100%;
}

.active {
  color: #fff !important;
  text-decoration: underline;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.8s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter,
.slide-fade-leave-to

/* .slide-fade-leave-active for below version 2.1.8 */
  {
  transform: translateX(10px);
  opacity: 0;
}
/* 13寸 */
/*@media screen and (min-width: 600px) and (max-width: 1300px) {*/
/*  .home .login_message{*/
/*    right: 5%;*/
/*    top: 20%;*/
/*  }*/
/*  .home .login_message .form-box{*/
/*    width: 520px;*/
/*    height: 380px;*/
/*  }*/
/*  .signButton{*/
/*    margin-top: 30px;*/
/*  }*/
/*}*/

/* 15寸 */
/*@media screen and (min-width: 1301px) and (max-width: 1920px) {*/
/*  .home .login_message .form-box {*/
/*      width: 600px;*/
/*      height: 440px;*/
/*    }*/
/*}*/
@media screen and (min-width: 600px) and (max-width: 1200px) {
  .home .login_message .form-box {
    width: 100%;
    height: 380px;
  }
  .input-content{
    width: 50%;
  }
  .home .login_message {
    right: 11%;
    top: 15%;
  }
}
@media screen and (min-width: 1201px) and (max-width: 1400px) {
  .home .login_message .form-box {
    width: 106%;
    height: 370px;
  }
  .home .form-container .input-content{
      width: 54% !important;
  }
  .signButton {
    margin-top: 5%;}
  .home .login_message {
    right: 12.5%;
    top: 16%;
  }
}
@media screen and (min-width: 1401px) and (max-width: 1500px) {
  .home .login_message .form-box {
    width: 111%;
    height: 400px;
  }
  .home .form-container .input-content{
    width: 58% !important;
  }
  .home .login_message {
    right: 13%;
    top: 18%;
  }
}
@media screen and (min-width: 1501px) and (max-width: 1600px) {
  .home .login_message .form-box {
    width: 121%;
    height: 415px;
  }
  .home .form-container .input-content{
    width: 62% !important;
  }

  .home .login_message {
    right: 15%;
    top: 21%;
  }
}
@media screen and (min-width: 1601px) and (max-width: 1800px) {
  .home .login_message .form-box {
    width: 125%;
    height: 415px;
  }
  .home .form-container .input-content{
    width: 63% !important;
  }

  .home .login_message {
    top: 22%;
    right: 16%;
  }
}
@media screen and (min-width: 1801px) and (max-width: 1920px) {
  .home .login_message .form-box {
    width: 130%;
    height: 425px;
  }
  .home .form-container .input-content{
    width: 64% !important;
  }
  .home .login_message {
    right: 18%;
    top: 25%;
  }
}
</style>
