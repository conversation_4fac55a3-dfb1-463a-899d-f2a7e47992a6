<template>
  <div class="org_wrapper">
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23">
        <span style="font-size: 18px; color: #fff;">组织结构管理</span>
        <div style="margin-bottom:20px;float: right;">
          <el-row type="flex" justify="space-between">
            <div>
              <el-button size="medium" class="add-button" @click="onCreateOrg">新增
              </el-button>
              <el-button size="medium" class="edit-button" @click="onUpdateOrg">修改
              </el-button>
              <el-button size="medium" class="delete-button" @click="onDeleteOrg">删除
              </el-button>
            </div>
          </el-row>
        </div>
      </el-col>
    </el-row>

    <el-dialog :title="isEditing ? '修改组织' : '新增组织'" class="addP" width="460px" :visible.sync="dialogFormVisible">
      <div style="margin-bottom: 20px"></div>
      <div class="prev_org_line" v-if="!this.isEditing && this.editingRow && this.editingRow.orgName">
        <span>上级组织：</span>
        {{ this.editingRow && this.editingRow.orgName }}
      </div>
      <el-form :model="orgForm" :rules="rules" ref="orgForm">
        <el-form-item label="组织名称" prop="orgName" :label-width="formLabelWidth">
          <el-input v-model="orgForm.orgName" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="组织全称" prop="orgFullName" :label-width="formLabelWidth">
          <el-input v-model="orgForm.orgFullName" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="managerId" :label-width="formLabelWidth">
          <el-cascader v-loading="loadingStaff" placeholder="选择负责人" v-model="orgForm.managerId" :show-all-levels="false"
            :options="localStaffList" :props="managerProps" style="width: 100%" clearable filterable>
          </el-cascader>
        </el-form-item>
        <el-form-item label="组织电话" prop="orgPhone" :label-width="formLabelWidth">
          <el-input v-model="orgForm.orgPhone" maxlength="15" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="组织地址" prop="orgAddress" :label-width="formLabelWidth">
          <el-input v-model="orgForm.orgAddress" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="组织编码" prop="orgCode" :label-width="formLabelWidth">
          <div style="display: flex;">
            <div v-if="this.editingRow" style="min-width: max-content">
              {{ this.isEditing ? this.editingRow.parentCode : this.editingRow.orgCode }}
            </div>
            <el-input v-model.trim="orgForm.orgCode" autocomplete="off"></el-input>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="onConfirm">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="删除" :visible.sync="deletedialogVisible" width="30%" :show-close="false" class="deletemenu">
      <div style="text-align: center">
        <i class="el-icon-warning" style="font-size: 20px;color: #fcb543;"><span
            style="font-size: 16px;color: #333;margin-left: 12px;">确定删除该组织吗？</span></i>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="sure" @click="deleteForReal">确 定</el-button>
        <el-button @click="deletedialogVisible = false" class="cancel">取 消</el-button>
      </span>
    </el-dialog>

    <el-row type="flex" justify="center">
      <el-col :span="23">
        <el-table :data="transformedData" style="width: 100%;margin-bottom: 20px;" row-key="orgId"  class="tableT"
          highlight-current-row :expand-row-keys="expandRowKeys" @row-click="onRowClick" @expand-change="onExpandChange"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
          <el-table-column label="组织名称" min-width="3">
            <template slot-scope="scope">
              <img v-if="(scope.row.children || []).length === 0" src="../../assets/images/team.svg" alt=""
                style="width: 25px; position: relative; top: 7px; left: 2px">
              <img v-else-if="expandRowKeys.indexOf(scope.row.orgId) > -1" src="../../assets/images/zuzhi-open.svg"
                alt="" style="width: 17px; position: relative; top: 5px; left: 2px">
              <img v-else src="../../assets/images/zuzhi.svg" alt="" style="width: 18px; position: relative; top: 5px;">
              <span style="margin-left: 10px">{{ scope.row.orgName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="orgFullName" label="组织全称" min-width="3">
          </el-table-column>
          <el-table-column prop="orgCode" min-width="2" label="组织编码">
          </el-table-column>
          <el-table-column prop="managerName" min-width="2" label="负责人">
          </el-table-column>
          <el-table-column prop="orgPhone" min-width="1.5" label="组织电话">
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </div>
</template>

<script >
import { mapState, mapActions } from 'vuex';
import { Notification } from 'element-ui';

const initFormValues = {
  orgName: '',
  orgFullName: '',
  managerId: '',
  orgPhone: '',
  orgAddress: '',
  orgCode: ''
};
export default {
  name: "organization",
  data() {
    const checkPhone = (rule, value, callback) => {
      const onlyNum = /^\d+$/;
      if (!value) {
        callback(new Error('请输入组织电话'));
      }
      if (!onlyNum.test(value)) {
        callback(new Error('只能输入数字'));
      }
      if (value.length > 15) {
        callback(new Error('最多输入15个字符'));
      }
      callback();
    };
    //   const checkOrgCode = (rule, value, callback) => {
    //     const onlyNum = /^\d+$/;
    //     if (!value) {
    //       callback(new Error('请输入组织编码'));
    //     }
    //     if (!onlyNum.test(value)) {
    //       callback(new Error('只能输入数字'));
    //     }
    //     callback();
    //   };
    return {
      rules: {
        orgAddress: [
          { required: true, message: '请输入组织地址', trigger: 'blur' }
        ],
        managerId: [
          { required: true, message: '请选择负责人', trigger: 'blur' }
        ],
        orgFullName: [
          { required: true, message: '请输入组织全称', trigger: 'blur' }
        ],
        orgName: [
          { required: true, message: '请输入组织名称', trigger: 'blur' }
        ],
        orgPhone: [
          {
            required: true,
            trigger: 'blur',
            validator: checkPhone
          }
        ],
        orgCode: [
          { required: true, message: '请输入组织编码', trigger: 'blur' },
          { pattern: /^\d+$/, message: '只能输入数字', trigger: 'blur' }
        ]
      },
      deletedialogVisible: false,
      managerProps: {
        emitPath: false,
        label: 'staffName',
        value: 'staffId'
      },
      editingRow: null,
      dialogFormVisible: false,
      formLabelWidth: '85px',
      // orgForm: { ... initFormValues },
      orgForm: {
        orgName: '',
        orgFullName: '',
        managerId: '',
        orgPhone: '',
        orgAddress: '',
        orgCode: ''
      },
      expandRowKeys: ['200'],
      isEditing: false,
      loadingStaff: false,
      localStaffList: null
    };
  },
  created() {
    this.getOrgList();
    if (!(this.staffList && this.staffList.length)) {
      this.loadingStaff = true;
      this.getStaffList().then((staffList) => {
        this.loadingStaff = false;
        this.localStaffList = staffList;
      }).catch(() => this.loadingStaff = false);
    } else {
      this.localStaffList = this.staffList;
    }
  },
  computed: {
    ...mapState({
      orgTreeList: 'orgList',
      staffList: 'staffList'
    }),
    transformedData: function () {
      if (Array.isArray(this.orgTreeList)) {
        this.transformForestData(this.orgTreeList);
      } else {
        this.transformNodeData(this.orgTreeList);
      }
      return this.orgTreeList;
    }
  },
  methods: {
    ...mapActions([
      'getOrgList',
      'getStaffList',
      'createOrg',
      'updateOrg',
      'deleteOrg'
    ]),
    onUpdateOrg() {
      if (this.editingRow) {
        this.isEditing = true;
        this.orgForm = this.editingRow;
        const parentCodeLen = this.editingRow.parentCode && this.editingRow.parentCode.length;
        if (parentCodeLen) {
          this.orgForm.orgCode = this.editingRow.orgCode.substring(parentCodeLen);
        }
        this.dialogFormVisible = true;
      } else {
        Notification.warning({
          message: '请先选中要修改的组织'
        })
      }
    },
    onDeleteOrg() {
      if (!this.editingRow) {
        Notification.warning({
          message: '请先选中要删除的组织'
        });
        return;
      }
      this.deletedialogVisible = true;
    },
    deleteForReal() {
      if (this.editingRow) {
        this.deleteOrg({
          orgId: this.editingRow.orgId
        }).then(() => {
          this.deletedialogVisible = false;
          Notification.success({
            message: `删除成功`
          });
          this.getOrgList();
        });
      } else {
        Notification.warning({
          message: '请先选中组织'
        });
      }
    },
    onCreateOrg() {
      if (!this.editingRow) {
        Notification.warning({
          message: '请先选中一个上级组织'
        });
        return;
      }
      this.isEditing = false;
      this.orgForm = { ...initFormValues };
      this.dialogFormVisible = true;
    },
    onConfirm() {
      const op = this.isEditing ? '修改' : '新增';
      this.$refs.orgForm.validate(async valid => {
        if (valid) {
          const orgForm = { ...this.orgForm };

          if (this.editingRow) {
            console.log(this.editingRow);
            if (!this.isEditing) { // 新增
              orgForm.parentId = this.editingRow.orgId;
              orgForm.orgCode = this.editingRow.orgCode + this.orgForm.orgCode;
            } else {
              orgForm.parentId = this.editingRow.parentId;
              orgForm.orgCode = (this.orgForm.parentCode + '') + this.orgForm.orgCode;
            }
          }
          try {
            if (this.isEditing) {
              await this.updateOrg(orgForm);
            } else {
              await this.createOrg(orgForm);
            }
            Notification.success({
              message: `${op}成功`
            });
            this.getOrgList();
            this.dialogFormVisible = false;
          } catch (e) {
            console.log(`${op}组织报错`, e);
          }
        }
      });
    },
    transformForestData(arr) {
      for (let node of arr) {
        this.transformNodeData(node);
      }
    },
    transformNodeData(node) {
      if (node) {
        if (node.children && node.children.length > 0) {
          this.transformForestData(node.children);
        } else {
          node.children = null;
        }
        node.label = node.orgName;
        node.value = node.orgId;
      }
    },
    onRowClick(data) {
      this.editingRow = data;
    },
    onExpandChange(row, expanded) {
      if (expanded) {
        this.expandRowKeys.push(row.orgId)
      } else {
        this.expandRowKeys = this.expandRowKeys.filter(key => key !== row.orgId)
      }
    }
  }
};
</script>

<style scoped lang="less">
.add {
  background-color: #5874e1;
  color: #fff;
}

.title {
  height:66px;
  line-height: 66px;
  // border-bottom: 1px solid #eee;
  margin-bottom: 10px;
}

.org_wrapper {
  background-color: #21274D;
  margin: -20px 0;

}

.prev_org_line {
  background: rgb(250, 246, 226);
  color: rgb(191, 153, 89);
  padding: 8px 10px;
  border-radius: 5px;
  margin-bottom: 18px;

  span {
    color: rgb(191, 152, 88);
    font-weight: bold;
  }
}

.org_title {
  display: flex;
  justify-content: space-between;
  height: 50px;

  .org_title_txt {
    font-size: 25px;
  }

  .buttons_section {
    width: 180px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .orgbtn {
      width: 50px;
      height: 30px;
    }

    .newbtn {
      background: lightseagreen;
      color: white;
    }
  }
}
</style>