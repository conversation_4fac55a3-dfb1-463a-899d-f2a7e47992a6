<template>
  <div class="org_wrapper">
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23">
        <div class="title_header">员工管理</div>
        <div style="margin-bottom:20px;float: right;">
          <el-row type="flex" justify="space-between">
            <div>
            </div>
          </el-row>
        </div>
      </el-col>
    </el-row>
    <el-dialog :title="isEditing ? '编辑员工' : '添加员工'" class="addP" width="550px" :visible.sync="dialogFormVisible"
      :before-close="digClose">
      <div class="login-style">
        <div class="basicinfo" @click="showBasicClick" :class="{ account: showbasicinfo }">基本信息</div>
        <div class="moreinfo" @click="showMoreClick" :class="{ account: !showbasicinfo }">更多信息</div>
      </div>

      <el-form :model="form" ref="staffForm" :rules="rules" style="margin-top: 20px">
        <el-col v-if="showbasicinfo" class="formSty">
          <el-form-item label="员工姓名" :label-width="formLabelWidth" prop="staffName">
            <el-input v-model="form.staffName" autocomplete="off" maxlength="20" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="登录账号" :label-width="formLabelWidth" prop="userName">
            <el-input v-model="form.userName" autocomplete="off" maxlength="20" show-word-limit
              :disabled="isEditing"></el-input>
          </el-form-item>
          <el-form-item label="员工工号" :label-width="formLabelWidth" prop="staffCode">
            <el-input v-model="form.staffCode" autocomplete="off" maxlength="20" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="性别" :label-width="formLabelWidth">
            <el-radio-group v-model="form.sex">
              <el-radio :label="0">男</el-radio>
              <el-radio :label="1">女</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="员工状态" :label-width="formLabelWidth">
            <el-radio-group v-model="form.state">
              <el-radio :label="1">在职</el-radio>
              <el-radio :label="2">实习</el-radio>
              <el-radio :label="0">离职</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item label="所属组织" :label-width="formLabelWidth">
            <el-cascader placeholder="选择所属组织" v-model="form.orgId" :show-all-levels="false"
              :options="transformedOrgData" :props="{ checkStrictly: true, emitPath: false }" clearable
              style="width: 100%" filterable></el-cascader>
          </el-form-item> -->

          <el-form-item label="所属部门" :label-width="formLabelWidth">
            <el-cascader placeholder="选择所属部门" v-model="form.deptTreeId" :show-all-levels="false"
              :options="transformedDeptData" :props="{
                checkStrictly: true,
                label: 'deptname',
                value: 'deptcode',
                emitPath: false,
                multiple: true,
              }" clearable style="width: 100%" filterable></el-cascader>
          </el-form-item>
          <el-form-item label="岗位名称" :label-width="formLabelWidth">
            <el-cascader placeholder="选择岗位名称" v-model="form.posId" :show-all-levels="false"
              :options="transformedPosData" :props="{ checkStrictly: true, emitPath: false }" clearable
              style="width: 100%" filterable></el-cascader>
          </el-form-item>
          <el-form-item label="手机号码" :label-width="formLabelWidth" prop="mobilePhone">
            <el-input v-model="form.mobilePhone" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="conactEmail" :label-width="formLabelWidth">
            <!-- :rules="[{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['change'] }]" -->
            <el-input v-model="form.conactEmail" autocomplete="off">
              <el-select v-model="form.selectEmail" slot="append" placeholder="请选择" style="width: 180px;">
                <el-option label="@cec1979.org.cn" value="@cec1979.org.cn"></el-option>
              </el-select>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col v-if="!showbasicinfo" class="formSty">
          <el-form-item label="相片" :label-width="formLabelWidth">
            <el-upload ref="uploader" :multiple="false" class="upload-demo" :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload" :show-file-list="false" list-type="picture" :action="upAppUrl">
              <el-button size="small" type="primary">选择文件</el-button>
            </el-upload>
            <template v-if="imageUrl">
              <img :src="baseImgUrl + '/' + imageUrl" class="photoPathStyle" alt="logo" />
            </template>
          </el-form-item>

          <el-form-item label="生日" :label-width="formLabelWidth">
            <el-date-picker type="date" value-format="yyyy-MM-dd" placeholder="选择日期" v-model="form.birthday"
              style="width: 100%;"></el-date-picker>
          </el-form-item>

          <el-form-item label="婚否" :label-width="formLabelWidth">
            <el-radio-group v-model="form.marital">
              <el-radio :label="1">已婚</el-radio>
              <el-radio :label="0">未婚</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="学历" :label-width="formLabelWidth">
            <el-select v-model="form.education" style="width: 100%" placeholder="请选择学历">
              <el-option label="博士及以上" value="doctorO"></el-option>
              <el-option label="硕士" value="master"></el-option>
              <el-option label="本科" value="bachelor"></el-option>
              <el-option label="大专" value="associate"></el-option>
              <el-option label="高中" value="senior"></el-option>
              <el-option label="初中及以下" value="juniN"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="地址" :label-width="formLabelWidth">
            <el-input v-model="form.conactAddress" autocomplete="off" maxlength="50" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="联系方式" :label-width="formLabelWidth">
            <el-input v-model="form.conactPhone" autocomplete="off" maxlength="20" show-word-limit></el-input>
          </el-form-item>

        </el-col>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align:right;margin-right:12%">
        <el-button type="primary" @click="onConfirm" class="OKButton" :disabled="saveStatus">确 定</el-button>
        <el-button @click="digClose" class="CancButton"> 取 消</el-button>
      </div>
    </el-dialog>
    <el-row type="flex" justify="center">
      <el-col :span="23">
        <div class="main_container">
          <div class="main_left">
            <el-input placeholder="请输入组织名称" v-model="filterText" class="inputStyle" suffix-icon="el-icon-search">
            </el-input>
            <div style="margin-bottom: 25px;"></div>
            <div class='orgStyle'>
              <el-tree v-loading="orgTreeLoading" element-loading-background="rgb(33,39,77, 0.8)" class="filter-tree"
                @node-click="onDeptTreeNodeClick" highlight-current :data="transformedOrgData" :props="defaultProps"
                default-expand-all :expand-on-click-node="false" :filter-node-method="filterNode" ref="tree">
                <span class="custom-tree-node" slot-scope="{ node,data }">
                  <div v-if="!data.children" class="team_bg_image"></div>
                  <div v-else class="zuzhiOpen"></div>
                  <span>
                    {{ node.label }}
                  </span>

                </span>
              </el-tree>
            </div>
          </div>
          <div class="main_right">
            <div class="search_line">
              <el-button-group>
                <el-button type="primary" @click="workStatus = '全部'"
                  :style="workStatus === '全部' ? this.activeButtonStyle : this.normalButtonStyle">
                  全部
                </el-button>
                <el-button type="info" @click="workStatus = '在职'"
                  :style="workStatus === '在职' ? this.activeButtonStyle : this.normalButtonStyle">
                  在职
                </el-button>
                <el-button type="primary" @click="workStatus = '离职'"
                  :style="workStatus === '离职' ? this.activeButtonStyle : this.normalButtonStyle">
                  离职
                </el-button>
              </el-button-group>
              <span>
                <el-button class="add" size="medium" @click="onAddStaff">添加员工
                </el-button>
                <el-input placeholder="请输入内容" @keyup.enter.native="onSearchTxtChange" v-model="searchTxt"
                  class="input-with-select" style="width:306px">
                  <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer"
                    @click="onSearchTxtChange"></i>
                </el-input>
              </span>
            </div>
            <el-table v-loading="loading" element-loading-background="rgb(33,39,77, 0.8)" :data="staffList"
              height="calc(100vh - 286px)" style="width: 100%;" current-row-key="staffId" class="tableT"
              highlight-current-row>
              <el-table-column label="员工姓名" prop="staffName" min-width="8">
              </el-table-column>
              <el-table-column label="状态" min-width="5">
                <template slot-scope="scope">
                  <span>{{ ({ 0: '离职', 1: '在职', 2: '实习' }[scope.row.state]) }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="userName" min-width="8" label="登录账号">
              </el-table-column>
              <el-table-column prop="positionName" min-width="8" label="职务">
              </el-table-column>
              <el-table-column prop="conactPhone" min-width="10" label="电话">
              </el-table-column>
              <el-table-column prop="mobilePhone" min-width="10" label="手机号码">
              </el-table-column>
              <el-table-column prop="conactEmail" min-width="12" label="邮箱">
              </el-table-column>
              <el-table-column min-width="12" label="操作">
                <template slot-scope="scope">

                  <el-button type="success" plain size="mini" @click="onEditStaff(scope.row)" icon="el-icon-edit">
                  </el-button>
                  <el-button type="danger" plain size="mini" @click="onDeleteStaff(scope.row)" icon="el-icon-delete">
                  </el-button>

                </template>
              </el-table-column>
            </el-table>
            <div class="paginator">
              <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="totalCount || 0">
              </el-pagination>
            </div>
          </div>
          <el-dialog title="删除" :visible.sync="deletedialogVisible" width="30%" :show-close="false" class="deletemenu">
            <div style="text-align: center">
              <i class="el-icon-warning" style="font-size: 20px;color: #fcb543;"><span
                  style="font-size: 16px;color: #333;margin-left: 12px;">确定将该员工删除吗？</span></i>
            </div>
            <span slot="footer" class="dialog-footer">
              <el-button class="OKButton" @click="deleteForReal">确 定</el-button>
              <el-button @click="deletedialogVisible = false" class="CancButton">取 消</el-button>
            </span>
          </el-dialog>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { PHONE_REG } from "../../utils/const";
import { listOrganizations, getAllStaff, getList, addStaff, updateStaff, getPositionData } from "@/api/sino/staffmanage";
const initFormData = {
  staffName: '', // 员工姓名
  userName: '', // 登录账号
  staffCode: '', // 工号
  sex: 0, // 性别 0男/1女
  state: 1, // 员工状态
  orgId: '',  // 所属组织
  deptTreeId: '', // 所属部门
  posId: '', // 所属岗位
  mobilePhone: '',
  conactEmail: '',
  photoPath: '',
  birthday: '', // '2020-11-20'
  marital: 0, // 1已婚 0未婚
  education: '', // 学历
  conactAddress: '',
  conactPhone: '',
  // busiFlag: '', // 组内编号
  selectEmail: '@cec1979.org.cn'

};
export default {
  name: "staffmanage.vue",
  data() {
    // const checkEmail = (rule, value, callback) => {
    //   const re = /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;
    //   if (value && !re.test(value)) {
    //     callback(new Error('请输入正确的邮箱'));
    //   }
    //   callback();
    // };
    const checkPhone = (rule, value, callback) => {
      const onlyNum = /^\d+$/;
      if (!value) {
        callback(new Error('请输入手机号码'));
      }
      if (!onlyNum.test(value)) {
        callback(new Error('只能输入数字'));
      }
      if (value && value.length > 15) {
        callback(new Error('最多输入15个字符'));
      }
      if (!PHONE_REG.test(value)) {
        callback(new Error('请输入正确的手机号码'));
      }
      callback();
    };
    return {
      rules: {
        // conactEmail: [
        //   {
        //     trigger: 'change',
        //     validator: checkEmail,
        //   }
        // ],
        mobilePhone: [
          { required: true, trigger: 'blur', validator: checkPhone, }
        ],
        staffName: [
          { required: true, message: "请填写员工姓名", trigger: "blur" }
        ],
        userName: [
          { required: true, message: "请填写登录账号", trigger: "blur" }
        ],
        staffCode: [
          { required: true, message: "请填写员工工号", trigger: "blur" }
        ],
      },
      baseImgUrl: process.env.VUE_APP_BASE_URL,
      fileList: [],
      showbasicinfo: true,
      orgTreeLoading: false,
      activeButtonStyle: {
        background: '#0080BE 100%',
        color: '#101010 100%',
        // borderRadius: '47px',

        width: '72px',
        fontSize: ' 14px',
        border: '0px',
      },
      normalButtonStyle: {
        background: '#21274D',
        color: '#D3D3D3 100%',
        border: '0px solid rgb(227, 227, 227)',
        margin: '0 2px'
      },
      deletingRow: null,
      deletedialogVisible: false,
      loading: false,
      staffList: [],
      totalCount: 0,
      selectedTreeNode: {},
      orgTreeList: [],
      deptTreeList: [],
      positionTreeList: [],
      photoPath: '',
      orgId: '',
      workStatus: '在职',
      searchTxt: '',
      currentPage: 1,
      pageSize: 10,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      positionData: [],
      isEditing: false,
      filterText: '',
      showMore: false,
      dialogFormVisible: false,
      formLabelWidth: '80px',
      form: { ...initFormData },
      imageUrl: "",//图片地址
      postUrl: "",
      expandRowKeys: ['200'],
      saveStatus: false,//保存按钮禁止重复点击
      upAppUrl: process.env.VUE_APP_BASE_UPLOAD + 'appPerm/upAppLog',
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    'form.orgId': function (newVal) {
      this.getPositionList(newVal);
    },
    'workStatus': function () {
      this.currentPage = 1;
      this.getStaffListLocal();
    }
  },
  created() {
    this.getStaffListLocal();//获取员工数据
    this.getDeptList();
    this.getPositionList();
    this.orgTreeLoading = true;
    this.getOrgList().then(() => {
      this.orgTreeLoading = false;
    }).catch(() => {
      this.orgTreeLoading = false;
    });
    this.getPositionDataComponent()
  },
  methods: {//用于变量或者对象为空时，调用方法定义的全局方法获取
    ...mapActions(['createStaff', 'deleteStaff', 'getDeptList', 'updateStaff',]),
    getOrgList() {
      return listOrganizations().then(res => {
        const orgTreeList = res && res.data || [];
        this.orgTreeList = orgTreeList;
      });
    },
    showBasicClick() {
      this.showbasicinfo = true;
    },
    showMoreClick() {
      this.showbasicinfo = false;
      // 清空表单校验，避免再次进来会出现上次校验的记录
      this.$refs['staffForm'].clearValidate();
    },
    onDeleteStaff(rowData) {
      this.deletedialogVisible = true;
      this.deletingRow = rowData;
    },
    deleteForReal() {
      if (this.deletingRow) {
        const rowData = this.deletingRow;
        try {
          this.loading = true;
          this.deleteStaff(rowData.userName).then(() => {
            this.$message({
              message: "删除成功1",
              type: "success",
            });
            this.deletedialogVisible = false;
            setTimeout(() => {
              this.getStaffListLocal();
            }, 2000)
          })

        } catch (e) {
          console.error('删除员工错误', e);
        }
      } else {
        this.$message({
          message: "未选择员工",
          type: "error",
        });
      }
    },
    // 获取员工列表
    getStaffList(params) {
      const qs = params || {
        pageSize: 1000000,
        pageNum: 1,
        orgId: '',
        search: '',
        state: 1
      };
      try {
        this.loading = true;
        getAllStaff(qs).then(res => {
          this.loading = false;
          const data = res.data;
          this.totalCount = data.totalCount;
          this.staffList = data.staffSubject;
        });
      } catch (e) {
        this.loading = false;
      }
    },
    getPositionDataComponent() {
      getPositionData().then((res) => {
        // console.log(res,10)
        this.positionData = res.data;
      })
    },
    //关闭弹框
    digClose() {
      this.dialogFormVisible = false;
      // this.$refs.staffForm&&this.$refs.staffForm.resetFields();//清除数据
      this.imageUrl = "";
      this.saveStatus = false;
      this.form = {};
    },
    // 员工信息的新增或修改
    async onConfirm() {
      const op = this.isEditing ? '修改' : '添加';
      this.$refs.staffForm.validate(async valid => {
        if (valid) {
          if (!PHONE_REG.test(this.form.mobilePhone)) {
            this.$message({
              message: "请输入正确的手机号",
              type: "warning",
            });
            return;
          }
          if (this.form.staffName == '' || this.form.userName == '' || this.form.staffCode == '') {
            this.$message({
              message: "请将信息填写完整",
              type: "warning",
            });
            return;
          }
          try {
            this.saveStatus = true;
            // if(this.form.conactEmail){
            //   this.form.conactEmail=this.form.conactEmail+this.form.selectEmail
            // }
            let obj = {
              ...this.form,
              conactEmail: this.form.conactEmail + this.form.selectEmail,
              orgId: '1',
              deptTreeId: this.form.deptTreeId.join(','),
            }

            if (this.isEditing) {
              obj.photoPath = this.postUrl;
              updateStaff(obj).then((res) => {
                if (res && res.code == 200) {
                  this.dialogFormVisible = false;
                  this.saveStatus = false;
                  this.imageUrl = "";
                  this.getStaffListLocal();
                  this.$message({
                    message: "修改成功",
                    type: "success",
                  });
                } else {
                  this.saveStatus = false;
                  this.$message({
                    message: res.message || "修改失败",
                    type: "error",
                  });
                }
              })
            } else {
              obj.photoPath = this.postUrl
              addStaff(obj).then((res) => {
                if (res && res.code == 200) {
                  this.saveStatus = false;
                  this.dialogFormVisible = false;
                  this.imageUrl = "";
                  this.getStaffListLocal();
                  this.$message({
                    message: "添加成功",
                    type: "success",
                  });
                } else {
                  this.saveStatus = false;
                  this.$message({
                    message: res.message || "添加失败",
                    type: "error",
                  });
                }
              }).catch(() => {
                this.$message({
                  message: "添加失败",
                  type: "error",
                });
              })
            }

          } catch (e) {
            console.log(`${op}员工报错`);
          }
        }
      });
    },
    handleAvatarSuccess(res, file) {
      this.imageUrl = res.data
      // console.log(res.data)
      this.postUrl = res.data;

    },
    //上传图片以及对图片的限制
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/png" || file.type === "image/jpeg";
      const isLt2M = file.size / 1024 / 1024 < 1;
      if (!isJPG) {
        this.$message.error("上传的系统logo请选择png、jpg、jpeg等图片格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传系统logo大小不能超过 1MB!");
      }
      return isJPG && isLt2M;
    },
    onAddStaff() {
      this.form = { ...initFormData };
      this.$set(this.form, 'orgId', this.orgId);
      this.dialogFormVisible = true;
      this.isEditing = false;
      this.saveStatus = false;
    },
    //上传图片--以及对图片大小的控制
    onFileChosen(param) {
      console.log(param);
      const file = param.file;
      const reader = new FileReader();
      reader.addEventListener("load", () => {
        this.photoPath = reader.result;
        this.$set(this.form, 'photoPath', reader.result);
      }, false);

      if (file) {
        const size = file.size;
        if (size / 1024 / 1024 > 1) {
          this.$message({
            message: "图片不能超过1M",
            type: "error",
          });
          return;
        }
        reader.readAsDataURL(file);
      }
    },
    onEditStaff(detailData) {

      let rowData = { ...detailData }
      rowData.deptTreeId = rowData.deptCode && rowData.deptCode.split(',')
      this.isEditing = true;
      this.dialogFormVisible = true;
      if (rowData.photoPath) {
        this.imageUrl = rowData.photoPath.substr(rowData.photoPath.indexOf("=") + 1);
        this.postUrl = this.imageUrl
        console.log(this.imageUrl)
      }

      if (rowData.conactEmail && rowData.conactEmail.indexOf('@') > -1) {
        var index = rowData.conactEmail && rowData.conactEmail.indexOf('@')
        rowData.selectEmail = rowData.conactEmail && rowData.conactEmail.split("@")[1]
        rowData.conactEmail = rowData.conactEmail && rowData.conactEmail.slice(0, index)
        rowData.selectEmail = "@" + rowData.selectEmail
      }
      this.form = { ...rowData };
    },
    //节点被点击时的回调
    onDeptTreeNodeClick(data) {
      this.selectedTreeNode = data;
      this.orgId = data.orgId;
      this.currentPage = 1;
      this.getStaffListLocal();
    },

    handleSizeChange(val) {
      this.pageSize = val;
      this.getStaffListLocal();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getStaffListLocal();
    },
    getPositionList(orgId) {
      getList(orgId).then(res => {
        this.positionTreeList = res && res.data || [];
      });
    },
    getStaffListLocal() {
      const qs = {
        pageSize: this.pageSize,
        pageNum: this.currentPage,
        orgId: this.orgId || '',
        search: this.searchTxt,
        state: ({ '在职': 1, '实习': 2, '离职': 0, '全部': 3 }[this.workStatus])
      };
      this.getStaffList(qs);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;//对树节点进行筛选时执行的方法
    },

    onSearchTxtChange() {
      this.currentPage = 1;
      this.getStaffListLocal();
    },
    transformForestData(arr, labelName, valueName) {
      for (let node of arr) {
        this.transformNodeData(node, labelName, valueName);
      }
    },
    transformNodeData(node, labelName, valueName) {
      if (node) {
        if (node.children && node.children.length > 0) {
          this.transformForestData(node.children, labelName, valueName);
        } else {
          // node.children = null;
        }
        // node.label = node[labelName] + ((node.children || []).length);
        node.label = node[labelName];
        node.value = node[valueName];
        // node.children2 = node.children;
      }
    },
  },
  computed: {//同步项目中定义的全局的变量或者对象
    ...mapState({
      deptList: 'deptList',
    }),
    transformedOrgData: function () {
      if (Array.isArray(this.orgTreeList)) {
        this.transformForestData(this.orgTreeList, 'orgName', 'orgId');
      } else {
        this.transformNodeData(this.orgTreeList, 'orgName', 'orgId');
      }

      console.log('transformed orgTreeList', this.orgTreeList);
      return this.orgTreeList;
    },
    transformedDeptData: function () {
      this.positionData.map(item => item.children = null)
      this.transformForestData(this.positionData, 'deptname', 'deptcode');


      return this.positionData;
    },
    transformedPosData: function () {
      if (Array.isArray(this.positionTreeList)) {
        this.transformForestData(this.positionTreeList, 'positionName', 'positionId');
      } else {
        this.transformNodeData(this.positionTreeList, 'positionName', 'positionId');
      }

      return this.positionTreeList;
    }
  }
}
</script>

<style scoped lang="less">
.basicinfo {
  border-right: 1px solid #f4f4f4;
}

.account {
  color: #389AE2 !important;
  font-weight: bold;
}

.team_bg_image {
  background: url(../../assets/images/team.svg);
  background-size: cover;
  height: 25px;
  float: left;
  width: 25px;
}

.zuzhiOpen {
  background: url(../../assets/images/zuzhi-open.svg);
  background-size: cover;
  height: 15px;
  float: left;
  width: 15px;
  position: relative;
  top: 8px;
  left: 2px;
  margin-right: 5px
}

.basicinfo,
.moreinfo {
  float: left;
  width: 49%;
  height: 30px;
  color: #333;
  margin-bottom: 5px;
  border-bottom: 1px solid #f4f4f4;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  font-family: "microsoft yahei";
  font-size: 16px;
}

.custom-tree-node {
  font-size: 15px;
}


.org_wrapper {
  background-color: #21274D;
  margin: -20px 0;
  height: calc(100vh - 90px);
  overflow: hidden;

  /deep/ .el-form-item {
    margin-bottom: 18px !important;
  }

  /deep/ .el-tree__empty-block {
    background-color: #21274d;
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 0px white;
    -webkit-box-shadow: inset 0 0 0px white;
    background-color: rgb(193, 193, 193);
    /*滚动条的背景颜色*/
    border-radius: 20px;
  }
}

.paginator {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-bottom: 20px;
}


.main_container {
  display: flex;

  /*border: 1px solid red;*/
  .main_left {
    min-width: 250px;
    margin-right: 10px;
    /*max-height: 500px;*/
    /*overflow: auto;*/
    /*border: 1px solid red;*/

    /deep/ .el-input .el-input__inner {
      //border-radius: 54px !important;
      background-color: rgba(255, 255, 255, 0.25) !important;
      color: rgba(199, 199, 199, 1) !important;
      border: 0px
    }

  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #21274D;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  .main_right {
    width: calc(100% - 200px);

    .search_line {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;


      .el-input-group.el-input-group--append {
        width: 300px;
      }
    }

    /deep/ .el-input .el-input__inner {
      //border-radius: 54px !important;
      background-color: rgba(255, 255, 255, 0.25) !important;
      color: rgba(199, 199, 199, 1) !important;
      border: 0px
    }
  }
}

.add {
  width: 98px;
  //border-radius: 54px;
  background-color: rgba(14, 97, 201, 1);
  color: rgba(255, 255, 255, 1);
  margin-right: 10px;
  border: 0px;
}

.title {
  height: 66px;
  line-height: 66px;
  // border-bottom: 1px solid #eee;
  /*margin-bottom: 10px;*/
}

.photoPathStyle {
  width: 100px;
  height: 100px;
  display: block;
}

.orgStyle {
  max-height: calc(100vh - 270px);
  overflow: auto;
  border: 0px solid rgb(235, 238, 245)
}

.formSty {
  margin-top: 20px;
  height: 250px;
  overflow: auto;
  padding-right: 15px;

}

@media screen and (min-width: 600px) and (max-width: 1300px) {

  //13寸
  .orgStyle {
    max-height: calc(100vh - 270px);
  }

  .tableT {
    margin-bottom: 0px
  }

  .custom-tree-node {
    font-size: 12px;
  }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
  .tableT {
    margin-bottom: 30px
  }
}
</style>
