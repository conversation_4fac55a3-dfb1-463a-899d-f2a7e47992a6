<template>
  <div class="tab">
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23">
        <div  class="title_header">访问限制管理</div>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center" class="search">
      <el-col :span="23">
        <el-row type="flex" justify="space-between">
          <el-col :span="12" style="text-align: left">
            <el-button class="btnStyle" type="primary" style=" background-color: #0E61C9;" size="medium"
                       @click="adduser">新增
            </el-button>
            <el-button class="buttonStyle" type="primary" style=" background-color: #31C97E;" size="medium"
                       @click="edit">修改
            </el-button>
            <el-button class="buttonStyle" type="primary" style=" background-color: #FD5252;" size="medium"
                       @click="deletemenu">删除
            </el-button>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-input style="width: 306px;" placeholder="请输入内容" @keyup.enter.native="search" v-model.trim="searchword">
              <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="search"></i>
            </el-input>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <el-row type="flex" justify="center">
      <el-col :span="23">
        <el-table :data="tableData" height="calc(100vh - 270px)" style="width: 100%; " row-key="menuId" highlight-current-row
          @current-change="handleCurrentChange" class="tableT" ref="singleTable">
          <el-table-column prop="userName" label="账号名称"> </el-table-column>
          <el-table-column prop="staffName" label="员工姓名"> </el-table-column>
          <el-table-column prop="orgName" label="部门"> </el-table-column>
          <el-table-column prop="detail" label="账号描述"> </el-table-column>
        </el-table>
        <div class="pager">
          <el-pagination @size-change="handleSizeChange" @current-change="pagehandleCurrentChange"
            :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pagesize"
            layout="total, sizes, prev, pager, next, jumper" :total="totalcount"></el-pagination>
        </div>
      </el-col>
    </el-row>

    <el-dialog :title="titleDia" :visible.sync="adddialogVisible" width="500px" class="addP" :show-close="false"
      :before-close="addmenuClose">
      <div style="text-align: left">
        <el-form label-position="left" label-width="100px" :rules="addrule" ref="addform" :model="addform">
          <el-col>
            <el-form-item label="用户" prop="powerValue" style="margin-top: 20px">
              <el-select v-model="addform.powerValue" filterable remote reserve-keyword placeholder="请输入选择用户"
                :remote-method="remoteMethod" :loading="loading">
                <el-option v-for="item in userList" :key="item.powerValue" :label="item.name" :value="item.powerValue">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="系统/菜单" prop="powerType">
              <el-radio v-model="powerType" :label="0">系统</el-radio>
              <el-radio v-model="powerType" :label="1">菜单</el-radio>
            </el-form-item>
            <el-form-item label="菜单" prop="appIdArray" v-if="powerType == 1">
              <el-select v-model="addform.appIdArray" multiple placeholder="请点此选择菜单">
                <el-option v-for="item in appList" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="OKButton" :disabled="adddis" @click="resumesend('addform')">确定</el-button>
        <el-button @click="addmenuClose" class="CancButton">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="删除" :visible.sync="deletedialogVisible" width="30%" :show-close="false" class="deletemenu">
      <div style="text-align: center">
        <i class="el-icon-warning" style="font-size: 20px; color: #fcb543"><span
            style="font-size: 16px; color: #333; margin-left: 12px">确定删除该限制吗？</span></i>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="OKButton" :disabled="deldis" @click="deletesend">确 定</el-button>
        <el-button @click="deleteClose" class="CancButton">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import {
  getAppList,
  getUserName,
  getAccount,
  accountAdd,
  accountDel,
  accountUpdate,
  getAccountDetail
} from "@/api/sino/account";
export default {
  name: "authoritymanage",
  data() {
    return {
      currentRow: null,
      tableData: [],
      loading: false,
      currentPage: 1,
      pagesize: 10,
      searchword: "",
      totalcount: null,
      userList: [],
      appList: [],
      powerType: 0,
      adddialogVisible: false,
      systemlist: [
        {
          value: "MacSys",
          label: "EOSS系统",
        },
        {
          value: "CrmSys",
          label: "CRM系统",
        },
        {
          value: "PmsSys",
          label: "PMS系统",
        },
        {
          value: "HRSys",
          label: "EHR系统",
        },
      ],
      addform: {
        powerValue: "",
        powerType: "",
        appIdArray: [],
      },
      titleDia: '新增访问限制',
      addrule: {
        powerValue: [
          { required: true, message: "请选择用户", trigger: "blur" },
        ],
        appIdArray: [
          { required: true, message: "请选择菜单", trigger: "change" },
        ],
      },
      editrule: {
        menuName: [
          { required: true, message: "请输入菜单名称", trigger: "blur" },
        ],
        menuType: [
          { required: true, message: "请选择类型", trigger: "change" },
        ],
        subSystem: [
          { required: true, message: "请选择子系统", trigger: "change" },
        ],
      },
      adddis: false,//新增按钮状态
      deldis: false,//删除按钮状态
      deletedialogVisible: false,//删除弹出框
    };
  },
  created() {
    this.getTreeData();
    getAppList(
      localStorage.getItem("Susername"),
    ).then((res) => {
      if (res.code == 200) {
        console.log(res.data);
        this.appList = res.data;
      }
    });
  },
  methods: {
    search() {
      this.currentPage = 1;
      this.getTreeData();
    },
    //修改表格分页的展示条数
    handleSizeChange(val) {
      this.pagesize = val;
      this.getTreeData();
    },
    //修改表格分页的当前页
    pagehandleCurrentChange(val) {
      this.currentPage = val;
      this.getTreeData();
    },
    handleCurrentChange(val) {
      this.currentRow = val;
    },
    //远程搜索方法
    remoteMethod(query) {
      if (query !== "") {
        this.loading = true;
        getUserName(query).then((res) => {
          if (res.code == 200) {
            this.loading = false;
            console.log(res.data);
            this.userList = res.data;
          }
        });
      } else {
        this.userList = [];
      }
    },
    getTreeData() {
      getAccount({
        pagesize: this.pagesize,
        currentPage: this.currentPage,
        searchword: this.searchword,
      }).then((res) => {
        if (res.code == 200) {
          console.log(res.data);
          this.tableData = res.data.AccountSubject;
          this.totalcount = res.data.totalCount;
        }
      });
    },
    changeicon(row) {
      row.ifexpand = !row.ifexpand;
    },
    addmenuClose() {
      this.adddialogVisible = false;
      this.adddis = false;
      this.$refs.addform.resetFields();
      this.addform = {
        powerValue: "",
        powerType: "",
        appIdArray: [],
      };
      this.powerType = 0
    },
    //添加数据
    resumesend(formname) {
      this.$refs[formname].validate((valid) => {
        if (valid) {
          this.adddis = true;
          if (this.powerType == 0) {
            this.addform.powerType = "system";
            this.addform.appIdArray = [];
          } else if (this.powerType == 1) {
            this.addform.powerType = "black";
          }
          if (this.titleDia == '新增访问限制') {
            accountAdd(this.addform).then((res) => {
              if (res.code == 200) {
                this.adddis = false;
                this.adddialogVisible = false;
                this.currentPage = 1;
                this.getTreeData();
                this.$refs.addform.resetFields();
                document.documentElement.style.overflow = "auto";
                this.$message({
                  message: "添加成功！",
                  type: "success",
                });
              }
              else {
                this.adddis = false;
                this.$message({
                  message: res.message,
                  type: "error",
                });
              }
            });
          } else {
            accountUpdate(this.addform).then((res) => {
              if (res.code == 200) {
                this.adddis = false;
                this.adddialogVisible = false;
                this.currentPage = 1;
                this.getTreeData();
                this.$refs.addform.resetFields();
                document.documentElement.style.overflow = "auto";
                this.$message({
                  message: "修改成功！",
                  type: "success",
                });
              } else {
                this.adddis = false;
                this.$message({
                  message: res.message,
                  type: "error",
                });
              }
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    adduser() {
      this.titleDia = '新增访问限制';
      this.remoteMethod();
      this.adddialogVisible = true;
      this.$refs.addform&&this.$refs.addform.resetFields();
      this.adddis = false;
      this.addform = {
        powerValue: "",
        powerType: "",
        appIdArray: [],
      };
      this.powerType = 0
    },


    edit() {
      this.titleDia = '修改访问限制';
      if (this.currentRow) {
        getAccountDetail({ userName: this.currentRow.userName }).then(res => {
          if (res.code == 200) {
            this.addform.powerValue = res.data.userName;
            this.powerType = res.data.powerType == "black" ? 1 : 0;
            this.addform.appIdArray = res.data.appIds;
          }
        })
        this.remoteMethod();
        this.adddialogVisible = true;
        this.adddis = false;
      } else {
        this.$message.warning({
          message: '请选择表格中的数据'
        });
      }
    },

    deletemenu() {
      if (this.currentRow) {
        this.deletedialogVisible = true;
      } else {
        this.$message.warning({
          message: '请选择表格中的数据'
        });
      }
    },
    //删除访问限制
    deletesend() {
      this.deletedialogVisible = false;
      this.deldis = true;
      accountDel({ powerValue: this.currentRow.userName }).then((res) => {
        if (res.code == 200) {
          this.currentPage = 1;
          this.getTreeData();
          this.$message({
            message: "删除成功！",
            type: "success",
          });
          this.deldis = false;
          this.currentRow = null;//取消选中
          this.$refs.singleTable&&this.$refs.singleTable.setCurrentRow();//取消列表高亮选中
          this.deletedialogVisible = false;
          document.documentElement.style.overflow = "auto";
        } else {
          this.deletedialogVisible = false;
          this.deldis = false;
          this.$message.error({
            message: res.message || "删除失败！",
          });
        }
      });
    },
    deleteClose() {
      this.deletedialogVisible = false;
      this.deldis = false;
    },
  },
};
</script>

<style scoped lang="less">
.tab {
  background-color: #21274D;
  margin: -20px 0;
  height: calc(100vh - 90px);

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #21274D;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.pager {
  margin-top: 20px;
}

.pager {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-top: 10px;
  margin-bottom: 10px;
}

.el-pagination {
  float: right;
}

.title {
  height: 66px;
  line-height: 66px;
  /* border-bottom: 1px solid #eee; */
  /*margin-bottom: 10px;*/

  /deep/ .el-input .el-input__inner {
    //border-radius: 34px !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(199, 199, 199, 1) !important;
    border: 0px;

  }
}

.search {
  height: 40px;
  line-height: 40px;
  // border-bottom: 1px solid #eee;
  margin-bottom: 15px;

  /deep/ .el-input .el-input__inner {
    //border-radius: 34px !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(199, 199, 199, 1) !important;
    border: 0px;
    width: 306px;
  }
}
.add,
.edit,
.delete {
  color: #fff;
  margin-left: 15px;
  border: none;
}

.add {
  background-color: #037cb3;
}

.edit {
  background-color: #17b3d9;
}

.delete {
  background-color: #04a6ef;
}

.tip {
  background-color: #f6f4f5;
  color: #898989;
  font-size: 18px;
  height: 44px;
  line-height: 44px;
  padding-left: 18px;
}

.basicinfo,
.moreinfo {
  float: left;
  width: 49%;
  height: 30px;
  color: #333;
  margin-bottom: 5px;
  border-bottom: 1px solid #f4f4f4;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  font-family: "microsoft yahei";
  font-size: 16px;
}

.basicinfo {
  border-right: 1px solid #f4f4f4;
}

.account {
  color: #389ae2 !important;
  font-weight: bold;
}

.tree_img {
  display: inline-block;
  width: 16px;
  height: 18px;
  vertical-align: middle;
}

.tree_icon {
  background: url("../../assets/images/tree_icon.png") no-repeat -40px -40px;
}

.tree-folder {
  background: url("../../assets/images/tree_icon.png") no-repeat 0px -40px;
}

.tree-folder-open {
  background: url("../../assets/images/tree_icon.png") no-repeat -19px -40px;
}

.sure {
  background: none repeat scroll 0 0 #61b0e9;
  border-color: #389ae2;
  color: #fff;
  text-shadow: none;
  text-transform: uppercase;
}

/* 13寸 */
@media screen and (min-width: 600px) and (max-width: 1300px) {
  .tableT {
    margin-bottom: -5px
  }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
  .tableT {
    margin-bottom: 30px
  }
}
</style>
