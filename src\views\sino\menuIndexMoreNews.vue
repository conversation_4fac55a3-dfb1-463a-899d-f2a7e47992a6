<template>
  <div
    class="org_wrapper"
    v-loading="pageLoading"
    element-loading-background="rgb(33,39,77, 0.8)"
  >
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23">
        <div style="display: flex">
          <div
            style="
              color: #2c5fc2;
              margin-right: 10px;
              font-size: 1rem;
              cursor: pointer;
            "
            @click="goBack()"
          >
            <i class="el-icon-back" style="margin-right: 6px"></i>返回
          </div>
          <div class="title_header">新闻与公告</div>
        </div>
        <div style="margin-bottom: 20px; float: right">
          <el-row type="flex" justify="space-between">
            <div></div>
          </el-row>
        </div>
      </el-col>
    </el-row>

    <el-row type="flex" justify="center">
      <el-col :span="23">
        <div class="main_container">
<!--          <el-row class="search_line">-->
<!--            <el-col :span="12">-->
<!--              <el-button class="add" size="medium" style="margin-right: 10px"-->
<!--                >添加规章制度-->
<!--              </el-button>-->
<!--            </el-col>-->
<!--            <el-col :span="12" style="text-align: right">-->
<!--              <el-select-->
<!--                v-model="value"-->
<!--                size="medium"-->
<!--                placeholder="请选择"-->
<!--                style="margin-right: 1rem; width: 220px"-->
<!--              >-->
<!--                <el-option-->
<!--                  v-for="item in options"-->
<!--                  :key="item.value"-->
<!--                  :label="item.label"-->
<!--                  :value="item.value"-->
<!--                >-->
<!--                </el-option>-->
<!--              </el-select>-->
<!--              <el-input-->
<!--                placeholder="请输入标题"-->
<!--                @keyup.enter.native="onSearchTxtChange"-->
<!--                v-model="searchTxt"-->
<!--                style="width: 220px"-->
<!--                class="input-with-select"-->
<!--                size="medium"-->
<!--              >-->
<!--                <i-->
<!--                  slot="suffix"-->
<!--                  class="el-input__icon el-icon-search"-->
<!--                  style="cursor: pointer"-->
<!--                  @click="onSearchTxtChange"-->
<!--                ></i>-->
<!--              </el-input>-->
<!--            </el-col>-->
<!--          </el-row>-->
          <el-table
            ref="versionTable"
            :data="tableData"
            height="calc(100vh - 270px)"
            style="width: 100%"
            class="tableT"
            highlight-current-row
            v-loading="loading"
          >
            <el-table-column label="标题" prop="positionnam" min-width="200">
              <template slot-scope="scope">
                <div
                  style="cursor: pointer"
                  @click="openNews(scope.row)"
                >
                  {{ scope.row.Title }}
                </div>
              </template>
            </el-table-column>
<!--            <el-table-column label="类型" prop="recordTheme" min-width="160"></el-table-column>-->
            <el-table-column label="发布人" prop="Creator" min-width="120">
            </el-table-column>
            <el-table-column
              label="发布时间"
              prop="CreateTime"
              min-width="120"
            >
              <template slot-scope="props">
                <span>{{ formatDate(props.row.CreateTime) }}</span>
              </template>
            </el-table-column>
<!--            <el-table-column label="审核状态" prop="releaseTime" width="120"></el-table-column>-->
          </el-table>
<!--          <div class="paginator">-->
<!--            <el-pagination-->
<!--              @size-change="handleSizeChange"-->
<!--              @current-change="handleCurrentChange"-->
<!--              :current-page="currentPage"-->
<!--              :page-sizes="[5, 10, 15]"-->
<!--              :page-size="pageSize"-->
<!--              layout="total, sizes, prev, pager, next, jumper"-->
<!--              :total="totalcount"-->
<!--            >-->
<!--            </el-pagination>-->
<!--          </div>-->
        </div>
      </el-col>
    </el-row>
    <!-- 新闻标题的弹窗 -->
    <div class="tipStyle">
      <el-dialog
        title="查看规章制度"
        :visible.sync="dialogTipVisible"
        width="80%"
        class="addpup"
        :before-close="handleTipClose"
      >
        <div style="line-height: 2;padding-left: 3.5rem" class="dialogContent">
          <div class="dialogTitle">{{newsContent.Title}}</div>
          <div class="dialogTime">
            <span>发布时间：</span><span>{{ formatDate(newsContent.CreateTime) }}</span>
          </div>
        </div>
        <div style="line-height: 2" class="dialogContent">
          <EditorDteail id="recordContent" v-model="newsContent.contentText" :min-height="30"
                        :showType="'table'"/>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleTipClose()" class="cancelbtn" size="medium"
            >关闭</el-button
          >
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getNews, addviewLog } from "@/api/app/app";
import moment from 'moment';
import EditorDteail from '@/components/Editor/detail.vue';
export default {
  name: "menuIndexMoreNews",
  components: {
    EditorDteail
  },
  data() {
    return {
      pageLoading: false,
      searchTxt: "",
      filterText: "",
      pageSize: 10,
      currentPage: 1,
      formLabelWidth: "120px",
      dmValue: "",
      expandRowKeys: ["200"],
      totalcount: null,
      companyList: [],
      userName:"",
      loginName:"",
      value: "",
      //版本更新列表
      tableData: [],
      newsContent:{},
      //版本更新loading
      loading: false,
      dialogTipVisible: false,
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree2.filter(val); //查询
    },
  },
  created() {
    this.getNewsData();
    this.userName = window.localStorage.getItem("Susername");
    this.loginName = window.localStorage.getItem("LoginName");
  },
  methods: {
    getNewsData() {
      getNews().then((res) => {
        if (res.code == 200) {
          this.tableData = res.data;
        }
      });
    },
    //时间格式化
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD HH:mm:ss');
    },
    // 触发首页的新闻弹窗事件
    openNews(val) {
      this.newsContent = {...val}
      this.dialogTipVisible = true;
      addviewLog({
        userName: this.userName,
        name: this.loginName,
        id: val.id,
      }).then((res) => {
        console.log(res);
      });
    },
    // 弹窗的关闭按钮
    handleTipClose() {
      this.dialogTipVisible = false;
    },
    // 返回上一个页面
    goBack() {
      window.history.back();
    },
  },
  computed: {},
};
</script>

<style scoped lang="less">
.paginator {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-bottom: 20px;
}

.org_wrapper {
  background-color: #21274d;
  margin: -20px 0;
  height: calc(100vh - 90px);
}

.add {
  width: 120px;
  //border-radius: 54px;
  background-color: #0e61c9;
  color: #ffffff;
  border: 0px;
}

.title {
  height: 66px;
  line-height: 66px;
  // border-bottom: 1px solid #eee;
  /*margin-bottom: 10px;*/
}
.myIcon {
  color: #409eff;
  padding: 0 5px;
}
.dialogTitle {
  font-size: 1.5rem;
  color: #000;
}
.dialogContent {
  border: 1px solid rgba(62, 62, 62, 0.2);
  box-shadow: rgba(62, 62, 62, 0.2) 0px 0px 12px 0px;
  padding: 0.5rem 3.5rem 0.5rem 0;
  margin-bottom: 1rem;
}
/deep/ #haneleDetailId .el-loading-mask {
  background-color: rgba(255, 255, 255, 1) !important;
}
.main_container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 160px);
  .search_line {
    /*width: 300px;*/
    /*float: right;*/
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;

    /deep/ .el-input .el-input__inner {
      //border-radius: 34px !important;
      background-color: rgba(255, 255, 255, 0.25) !important;
      color: rgba(199, 199, 199, 1) !important;
      border: 0px;
    }
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #21274d;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}
</style>
