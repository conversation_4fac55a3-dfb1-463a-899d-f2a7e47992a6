import request from '@/utils/request'

//获取企业微信扫码地址
export const oauthMenu = (userName)=>{
    return new Promise((resolve)=>{
        request.get("/menu/oauthMenu?userName="+userName).then(res=>{
            resolve(res)
        })
    })
}

export const menuUpdate = (param) => {
    return new Promise((resolve) => {
      request.post("/menu/update", param).then((res) => {
        resolve(res);
      });
    });
  };
  
  export const getDetil = (menuId) => {
    return new Promise((resolve) => {
      request.get("/menu/getDetil?MenuId=" + menuId).then((res) => {
        resolve(res);
      });
    });
  };
  
  export const menuDel = (menuId) => {
    return new Promise((resolve) => {
      request.get("/menu/del?MenuId=" + menuId).then((res) => {
        resolve(res);
      });
    });
  };

  export const listMenus = () => {
    return new Promise((resolve) => {
      request.get("/menu/listMenus").then((res) => {
        resolve(res);
      });
    });
  };

  export const menuAdd = (param) => {
    return new Promise((resolve) => {
      request.post("/menu/add", param).then((res) => {
        resolve(res);
      });
    });
  };