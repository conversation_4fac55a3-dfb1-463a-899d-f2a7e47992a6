<template>
    <div class="tab">
        <el-row type="flex" justify="center" class="title">
            <el-col :span="23">
                <div class="title_header">我的证书</div>
            </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="search">
            <el-col :span="23">
                <template>
                    <el-col :span="8" style="text-align: left">
                        <el-button class="btnStyle" type="primary" style="background-color: #0E61C9;" @click="addmenu"
                            size="medium">新增
                        </el-button>
                    </el-col>
                </template>


            </el-col>
        </el-row>
        <el-row type="flex" justify="center" style="margin-top: 15px;overflow-x: auto;">
            <el-col :span="23">

                <el-table :data="tableData" class="tableT" height="calc(100vh - 268px)" style="width: 100%;"
                    highlight-current-row>
                    <el-table-column label="证书厂商" max-width="160">
                        <template slot-scope="scope">
                            <span v-if="scope.row.firm != '其他'">{{ scope.row.firm }}</span>
                            <span v-else>{{ scope.row.otherFirm }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="名称级别">
                        <template slot-scope="scope">
                            <span v-if="scope.row.certLevel != '其他'">{{ scope.row.certLevel }}</span>
                            <span v-else>{{ scope.row.otherLevel }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="领域方向">
                        <template slot-scope="scope">
                            <span v-if="scope.row.orientation != '其他'">{{ scope.row.orientation }}</span>
                            <span v-else>{{ scope.row.otherOrientation }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="certNo" label="证书编号">
                    </el-table-column>
                    <el-table-column prop="getTime" label="取证时间" width="105">
                    </el-table-column>
                    <el-table-column prop="expireTime" label="到期时间" width="105">
                    </el-table-column>
                    <el-table-column prop="amount" label="证书状态" width="100">
                        <template slot-scope="scope">
                            <span v-if="scope.row.sendMessage == 2">
                                <el-tooltip effect="dark" :content="scope.row.message" placement="top-start">
                                    <span class="amount" style="color: red">
                                        待变更
                                    </span>
                                </el-tooltip>
                            </span>
                            <span class="amount" v-else>
                                <span class="amount" v-if="scope.row.certState == '正常'" style="color: #1f9dd7">
                                    {{ scope.row.certState }}
                                </span>
                              <el-tooltip effect="dark" :content="scope.row.certState" placement="top-start">
                                <span class="amount" v-if="scope.row.certState != '正常' && scope.row.certState != '未完善' && scope.row.certState != '异常'" style="color: #9f0606">
                                    {{ scope.row.certState }}
                                </span>
                              </el-tooltip>
                                <el-tooltip effect="dark" :content="getContent(scope.row)" placement="top-start">
                                    <span class="amount" v-if="scope.row.certState == '未完善'" style="color: #9f0606">
                                        {{ scope.row.certState }}
                                    </span>
                                </el-tooltip>
                                <el-tooltip effect="dark" :content="scope.row.message" placement="top-start">
                                    <span class="amount" v-if="scope.row.certState == '异常'" style="color: #9f0606">
                                        {{ scope.row.certState }}
                                    </span>
                                </el-tooltip>
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column width="100" label="审批状态">
                        <template slot-scope="scope">
                            <span class="amount" v-if="scope.row.approveState == 'TGSP'">审批通过</span>
                            <span class="amount" v-if="scope.row.approveState == 'SP'">证书审批中</span>
                            <span class="amount" v-if="scope.row.approveState == 'BH'">证书驳回</span>
                            <span class="amount" v-if="scope.row.approveState == 'BHSP'">驳回审批</span>
                            <span class="amount" v-if="scope.row.approveState == 'BGSP'">变更审批中</span>
                            <span class="amount" v-if="scope.row.approveState == 'BGBH'">变更驳回</span>
                            <span class="amount" v-if="scope.row.approveState == 'SQSP'">申请变更中</span>
                            <span class="amount" v-if="scope.row.approveState == 'SQBH'">申请变更驳回</span>
                            <span class="amount" v-if="scope.row.approveState == 'DBG'">待变更</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="amount" :width="handlerNum" label="操作">
                        <template slot-scope="scope">
                             <!--重新提交-->
                            <span class="amount"
                                style="color: #409eff;cursor: pointer;font-size: 15px;margin-right: 10px"
                                @click="certedit(scope.row,'CXTJ')"
                                v-if="scope.row.approveState == 'BH' || scope.row.approveState == 'BGBH' || scope.row.approveState == 'SQBH'">
                                <i class="el-icon-edit-outline"></i>重新提交
                            </span>
<!--                                                          v-if="scope.row.certState == '未完善'"-->
                            <span class="amount"
                                style="color: #e6a23c;cursor: pointer;font-size: 15px;margin-right: 10px"
                                @click="viewDetail(scope.row)">
                                <i class="el-icon-document-copy"></i>查看
                            </span>

                            <span class="amount"
                                style="color: #909399;cursor: pointer;font-size: 15px;margin-right: 10px"
                                @click="certedit(scope.row,'BJ')"
                                v-if="scope.row.approveState != 'TGSP' && scope.row.approveState == 'DBG'">
                                <i class="el-icon-document-checked"></i>变更
                            </span>

                        </template>
                    </el-table-column>

                </el-table>
            </el-col>
        </el-row>

        <el-dialog :title="certTitle" :visible.sync="dialogVisible" width="1000px" :show-close="false" class="addP"
            top="3vh">
            <el-form :model="certformL" :rules="certrule" ref="certformL" label-position="top" class="searchFrom">
                <el-row>

                    <el-col :span="8">
                        <el-form-item label="证书厂商" prop="firm">
                            <el-select filterable clearable v-model="certformL.firm" placeholder="证书厂商"
                                style="width: 90%;" @change="choosefirm">
                                <el-option v-for="(item, index) in firms" :key="index" :label="item" :value="item">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="certformL.firm == '其他'">
                        <el-form-item label="其他证书厂商" prop="otherFirm">
                            <el-input v-model.trim="certformL.otherFirm" placeholder="请输入其他证书厂商" style="width: 90%;"
                                @input="forceUpdate">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="名称级别" prop="certLevel">
                            <el-select filterable clearable v-model="certformL.certLevel" placeholder="名称级别"
                                style="width: 90%;" @change="chooseLevel">
                                <el-option v-for="(item, index) in certLevels" :key="index" :label="item" :value="item">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="certformL.certLevel == '其他'">
                        <el-form-item label="其他名称级别" prop="otherLevel">
                            <el-input v-model.trim="certformL.otherLevel" placeholder="请输入其他名称级别" style="width: 90%;"
                                @input="forceUpdate">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="领域方向" prop="orientation">
                            <el-select filterable clearable v-model="certformL.orientation" placeholder="领域方向"
                                style="width: 90%;" @change="chooseori(certformL.orientation)">
                                <el-option v-for="(item, index) in orientations" :key="index" :label="item"
                                    :value="item">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="certformL.orientation == '其他'">
                        <el-form-item label="其他领域方向" prop="otherOrientation">
                            <el-input v-model.trim="certformL.otherOrientation" placeholder="请输入其他领域方向"
                                style="width: 90%;" @input="forceUpdate">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="取证时间" prop="getTime">
                            <el-date-picker v-model="certformL.getTime" type="date" value-format="yyyy-MM-dd"
                                format="yyyy-MM-dd" style="width: 90%;" @change="choosetime" placeholder="取证时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="证书编号" prop="certNo">
                            <el-input v-model.trim="certformL.certNo" placeholder="请输入证书编号" style="width: 90%;">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="到期时间" prop="expireTime">
                            <el-date-picker v-model="certformL.expireTime" type="date" value-format="yyyy-MM-dd"
                                format="yyyy-MM-dd" style="width: 90%;" :picker-options="pickerOptions"
                                placeholder="选择到期时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="是否关联新桥" prop="ifSino">
                            <el-select filterable clearable v-model="certformL.ifSino" placeholder="是否关联新桥"
                                style="width: 90%;">
                                <el-option v-for="(item, index) in ifSinoData" :key="index" :label="item.label"
                                    :value="item.label">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="关联邮箱" prop="mail" v-if="certformL.ifSino == '是'">
                            <el-input v-model.trim="certformL.mail" placeholder="请输入关联邮箱" style="width: 90%;">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model.trim="certformL.remark" placeholder="请输入备注" style="width: 90%;">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-form-item label="上传厂商方向证书">
                        <el-upload class="upload-demo" ref="upload" :limit="1" :action="actionurl" :data="paramsData"
                            :on-preview="handlePreview" :on-remove="handleRemove" :on-success="handleASuccess"
                            :before-upload="beforeUpload1" :file-list="certfileList" accept=".jpg,.JPG,.jpeg,.png,.pdf">
                            <el-button size="small" type="primary" icon="el-icon-upload">点击这里上传文件</el-button>
                        </el-upload>
                    </el-form-item>
                </el-row>
            </el-form>

            <span slot="footer" class="dialog-footer">
                <el-button :disabled="senddis" class="OKButton" @click="resumesend('certformL',1)">{{certTitle =='重新提交技能证书'?'提交':'确定'}}</el-button>
                <el-button v-if="certTitle !='新增技能证书'" :disabled="senddis" class="OKButton" @click="resumesend('certformL',0)">撤销</el-button>
                <el-button @click="addmenuClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
            </span>
        </el-dialog>
        <el-dialog title="查看详情" :visible.sync="detailVisible" width="1000px" class="addP" top="3vh">
            <el-row class="bot">
                <el-col :span="8">
                    <el-col>
                        <p class="title">证书厂商</p>
                    </el-col>
                    <el-col>
                        <p class="text" v-if="certdetail.otherFirm">{{ certdetail.otherFirm }}</p>
                        <p class="text" v-else>{{ certdetail.firm }}</p>
                    </el-col>
                </el-col>
                <el-col :span="8">
                    <el-col>
                        <p class="title">名称级别</p>
                    </el-col>
                    <el-col>
                        <p class="text" v-if="certdetail.otherLevel">{{ certdetail.otherLevel }}</p>
                        <p class="text" v-else>{{ certdetail.certLevel }}</p>
                    </el-col>
                </el-col>
                <el-col :span="8">
                    <el-col>
                        <p class="title">领域方向</p>
                    </el-col>
                    <el-col>
                        <p class="text" v-if="certdetail.otherOrientation">{{ certdetail.otherOrientation }}</p>
                        <p class="text" v-else>{{ certdetail.orientation }}</p>
                    </el-col>
                </el-col>
            </el-row>
            <el-row class="bot">
                <el-col :span="8">
                    <el-col>
                        <p class="title">取证时间</p>
                    </el-col>
                    <el-col>
                        <p class="text">{{ certdetail.getTime }}</p>
                    </el-col>
                </el-col>
                <el-col :span="8">
                    <el-col>
                        <p class="title">证书编号</p>
                    </el-col>
                    <el-col>
                        <p class="text">{{ certdetail.certNo }}</p>
                    </el-col>
                </el-col>
                <el-col :span="8">
                    <el-col>
                        <p class="title">到期时间</p>
                    </el-col>
                    <el-col>
                        <p class="text">{{ certdetail.expireTime }}</p>
                    </el-col>
                </el-col>
            </el-row>
            <el-row class="bot">

                <el-col :span="8">
                    <el-col :span="8">
                        <el-col>
                            <p class="title">是否关联新桥</p>
                        </el-col>
                        <el-col>
                            <p class="text">{{ certdetail.ifSino }}</p>
                        </el-col>
                    </el-col>
                </el-col>
                <el-col :span="8" v-if="certdetail.ifSino == '是'">
                    <el-col>
                        <p class="title">关联邮箱</p>
                    </el-col>
                    <el-col>
                        <p class="text">{{ certdetail.mail }}</p>
                    </el-col>
                </el-col>
                <el-col :span="8">
                    <el-col>
                        <p class="title">备注</p>
                    </el-col>
                    <el-col>
                        <p class="text">{{ certdetail.remark }}</p>
                    </el-col>
                </el-col>

            </el-row>

            <el-row>
                <el-col>
                    <p class="title">上传厂商方向证书</p>
                </el-col>
                <el-col>
                    <a class="other" :href="certdetail.filePath" target="_blank" style="color: #1f9dd7">{{
                        certdetail.attachName
                        }}</a>
                </el-col>
            </el-row>

        </el-dialog>
    </div>
</template>

<script>
import { getCertList, updateCert, addCert, getCertFirm, getCertLevel, getCertOrientation } from "@/api/human/myCert";
import {resubmitCert} from "../../api/human/myCert";

export default {
    name: "myCert",
    data() {
        var sinoEmail = (rule, value, callback) => {
            const mailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/
            if (!value) {
                return callback(new Error('请输入正确的邮箱格式'))
            } else {
                setTimeout(() => {
                    if (mailReg.test(value)) {
                        callback()
                    } else {
                        callback(new Error('请输入正确的邮箱格式'))
                    }
                }, 100)
            }
        };
        return {
            tableData: [],
            certfileList: [],
            ifSinoData: [{ value: '是', label: '是' }, { value: '否', label: '否' }],
            stateData: [{ value: '0', label: '离职' }, { value: '1', label: '在职' }, { value: '2', label: '实习' }],
            pickerOptions: {
                shortcuts: [{
                    text: '自定义',
                    onClick(picker) {
                        picker.$emit('pick', new Date());
                    }
                }, {
                    text: '永久',
                    onClick(picker) {
                        const date = new Date();
                        date.setFullYear(date.getFullYear() + 1000);
                        picker.$emit('pick', date);
                    }
                }]
            },
            query: {
                staffName: '',
                staffCode: '',
                firm: '',
                certLevel: '',
                orientation: '',
                certState: '',
                RemotionTime: '',
                orgname: '',
                fuzzyQuery: "",
            },
            firms: [],
            certLevels: [],
            orientations: [],
            totalcount: null,
            senddis:false,
            dialogVisible: false,
            certTitle: '',
            certformL: {
                firm: "",
                otherFirm: "",
                otherLevel: "",
                otherOrientation: "",
                certLevel: "",
                orientation: "",
                getTime: "",
                certNo: "",
                expireTime: "",
                mail: "",
                remark: "",
                attachId: "",
                ifSino: '',
            },
            userData: [],//证书持有人
            prewAttachId: '',
            paramsData: {},
            certrule: {

                firm: [
                    { required: true, message: "请选择发证机构", trigger: "change" }
                ],
                otherFirm: [
                    { required: true, message: "请输入其他发证机构", trigger: "change" }
                ],
                otherLevel: [
                    { required: true, message: "请输入其他名称级别", trigger: "change" }
                ],
                otherOrientation: [
                    { required: true, message: "请输入其他领域方向", trigger: "change" }
                ],
                certLevel: [
                    { required: true, message: "请选择名称级别", trigger: "change" }
                ],
                orientation: [
                    { required: true, message: "请选择领域方向", trigger: "change" }
                ],
                getTime: [
                    { required: true, message: "请选择取证时间", trigger: "change" }
                ],
                mail: [
                    { required: true, message: "请正确输入关联邮箱", validator: sinoEmail, trigger: "blur" }
                ],
            },
            actionurl: '/ssoapi/record/uploadCert',
            certdetail: {},//详情
            detailVisible: false,
        };
    },
    created() {
        this.getTreeData();
        this.getCertFirm();
    },
    computed: {
     handlerNum(){
       let showTwo = 1
       let showThree = 1
       if(this.tableData && this.tableData.length>0){
          this.tableData.map(item=>{
            if(item.approveState != 'TGSP' && item.approveState == 'DBG'){
              showTwo = 2
            }
            if(item.approveState == 'BH' || item.approveState == 'BGBH' || item.approveState == 'SQBH'){
              showTwo = 2
            }
            if((item.approveState != 'TGSP' && item.approveState == 'DBG') && (item.approveState == 'BH' || item.approveState == 'BGBH' || item.approveState == 'SQBH')){
              showThree = 3
            }
          })
       }
       if(showThree >1){
         return 150 + 20*showThree
       }
       if(showTwo >1){
         return 150 + 20*showTwo
       }
       return 150
     },
    },
    methods: {
        resumesend(formName, flag) {
            this.$refs[formName].validate(valid => {
                if (valid) {
                    this.senddis = true;
                    var params = {
                        firm: this.certformL.firm,
                        certLevel: this.certformL.certLevel,
                        orientation: this.certformL.orientation,
                        otherFirm: this.certformL.otherFirm ? this.certformL.otherFirm : '',
                        otherLevel: this.certformL.otherLevel ? this.certformL.otherLevel : '',
                        otherOrientation: this.certformL.otherOrientation ? this.certformL.otherOrientation : '',
                        getTime: this.certformL.getTime,
                        certNo: this.certformL.certNo ? this.certformL.certNo : '',
                        expireTime: this.certformL.expireTime ? this.certformL.expireTime : '',
                        remark: this.certformL.remark ? this.certformL.remark : '',
                        ifSino: this.certformL.ifSino ? this.certformL.ifSino : '',
                        attachId: this.certformL.attachId ? this.certformL.attachId : '',
                        submitUser: localStorage.getItem('Susername'),
                        userId: localStorage.getItem('Susername'),
                        id: this.ids ? this.ids : '',
                    }
                    if(this.certformL.ifSino=='是'){
                        params.mail=this.certformL.mail ? this.certformL.mail : ''
                    }
                    if (this.certTitle == '修改技能证书') {
                        if(this.prewAttachId&&this.certformL.attachId==''){
                            params['attachId']=this.prewAttachId
                        }
                        params['message']=this.certformL.message
                        updateCert(params).then((res) => {
                            if (res.code == 200) {
                                this.senddis = false;
                                console.log(res);
                                this.$message({message: res.data});
                                this.getTreeData();
                                this.dialogVisible = false;
                                this.$refs.certformL.resetFields();
                                document.documentElement.style.overflow = "auto";
                            }
                        });
                    }
                  else if (this.certTitle == '重新提交技能证书') {
                    if(this.prewAttachId&&this.certformL.attachId==''){
                      params['attachId']=this.prewAttachId
                    }
                      params['message']=this.certformL.message
                      params['approveResult'] = flag
                      resubmitCert(params).then((res) => {
                      if (res.code == 200) {
                        this.senddis = false;
                        console.log(res);
                        this.$message.success({message: res.message});
                        this.getTreeData();
                        this.dialogVisible = false;
                        this.$refs.certformL.resetFields();
                        document.documentElement.style.overflow = "auto";
                      }
                    });
                  }
                  else if (this.certTitle == '新增技能证书') {
                        addCert(params).then((res) => {
                            if (res.code == 200) {
                                this.senddis = false;
                                this.$message({message: res.data});
                                this.getTreeData();
                                this.dialogVisible = false;
                                this.$refs.certformL.resetFields();
                                document.documentElement.style.overflow = "auto";
                            }
                        });
                    }
                }
            });
        },
        chooseori(val) {
            this.certformL.otherOrientation = ''
            this.$forceUpdate();
            this.$nextTick(() => {
                this.$refs.certformL.clearValidate();
            });
        },
        getCertFirm() {
            getCertFirm().then((res) => {
                if (res.code == 200) {
                    this.firms = res.data;
                }
            });
        },
        choosetime() {
            this.$forceUpdate();
            this.$nextTick(() => {
                this.$refs.certformL.clearValidate();
            });
        },
        forceUpdate() {
            this.$forceUpdate();
            this.$nextTick(() => {
                this.$refs.certformL.clearValidate();
            });
        },
        choosefirm() {
            this.certformL.otherFirm = ''
            this.certformL.certLevel = ''
            this.certformL.otherLevel = ''
            this.certformL.orientation = ''
            this.certformL.otherOrientation = ''
            if (this.certformL.firm) {
                //名称级别
                getCertLevel({ firm: this.certformL.firm }).then((res) => {
                    if (res.code == 200) {
                        this.certLevels = res.data;
                    }
                });
            } else {
                this.orientations = []
                this.certLevels = []
            }
            this.$nextTick(() => {
                this.$refs.certformL.clearValidate();
            });
        },
        chooseLevel() {
            this.$forceUpdate()
            this.certformL.otherLevel = ''
            this.certformL.orientation = ''
            this.certformL.otherOrientation = ''
            if (this.certformL.certLevel) {
                getCertOrientation({ firm: this.certformL.firm, certLevel: this.certformL.certLevel }).then((res) => {
                    if (res.code == 200) {
                        this.orientations = res.data;
                    }
                });
            } else {
                this.orientations = []
            }

            this.$nextTick(() => {
                this.$refs.certformL.clearValidate();
            });
        },
        //新增证书
        handleRemove(file, fileList) {
            console.log(file, fileList);
            this.certformL.attachId = '';
            this.certfileList = [];
        },
        search() {
            this.currentPage = 1;
            let param = {
                staffName: this.query.staffName,
                staffCode: this.query.staffCode,
                firm: this.query.firm,
                certLevel: this.query.certLevel,
                orientation: this.query.orientation,
                certState: this.query.certState,
                RemotionTime: this.query.RemotionTime,
                orgname: this.query.orgname,
            }
            this.getTreeData(param);
        },
        searchClose() {
            this.getTreeData({});
        },
        searchMore() {
            this.isAllSerarch = !this.isAllSerarch;
        },
        getTreeData(param) {
            getCertList({ userName: localStorage.getItem('Susername') }).then((res) => {
                if (res.code == 200) {
                    this.tableData = res.data;
                }
            });
        },
        //新增角色
        addmenu() {
            this.dialogVisible = true;
            this.certTitle = '新增技能证书'
            this.certformL = {};
            this.certfileList = [];
            this.$nextTick(() => {
                this.$refs.certformL.clearValidate();
            });
        },
        addmenuClose() {
            this.dialogVisible = false;
            this.$refs.certformL.resetFields();
        },
        certedit(row,type) {
            this.certformL = {};
            this.certfileList = [];
            if(type === 'BJ'){
              this.certTitle = '修改技能证书';
            }
            if(type === 'CXTJ'){
              this.certTitle = '重新提交技能证书';
            }
            this.ids = row.id;
            this.certformL = { ...row }
            this.dialogVisible = true;
            document.documentElement.style.overflow = "hidden";
            this.senddis = false;
            this.prewAttachId=row.attachId;
            getCertLevel({ firm: this.certformL.firm }).then((res) => {
                if (res.code == 200) {
                    this.certLevels = res.data;
                }
            });
            getCertOrientation({ firm: this.certformL.firm, certLevel: this.certformL.certLevel }).then((res) => {
                if (res.code == 200) {
                    this.orientations = res.data;
                }
            });
            if (this.certformL.filePath != null) {
                this.certfileList.push({ name: this.certformL.attachName, url: this.certformL.filePath });
            }
            this.$nextTick(() => {
                this.$refs.certformL.clearValidate();
            });
        },
        // 新增证书-上传服务器之前逻辑处理
        beforeUpload1(file) {
            const isJPG = (file.type === 'image/jpeg') || (file.type === 'image/png') || (file.type === 'image/jpg') || (file.type === 'application/pdf');
            const isLt2M = file.size / 1024 / 1024 < 10;
            if (!isJPG) {
                this.$message.error('只能上传图片或pdf文件!');
            }
            if (!isLt2M) {
                this.$message.error('上传图片大小不能超过 10MB!');
            }

        }, //成功上传
        handleASuccess(res, file) {
            this.certformL.attachId = res.data.attachId
        },
        //新增证书
        handlePreview(file) {
            console.log(file);
            let URL = window.URL || window.webkitURL;
            const userAgent = navigator.userAgent;
            if (!!window.ActiveXObject || "ActiveXObject" in window) {
                this.$message.warning("暂不支持预览");
            } else {
                if (file.raw) {
                    window.open(URL.createObjectURL(file.raw)) //blob格式地址
                } else {
                    window.open(file.url) //blob格式地址
                }
            }
        },
        viewDetail(row) {
            this.certdetail = { ...row }
            this.detailVisible = true;

        },
        getContent(row) {
            if (row.filePath == null && row.expireTime) {
                return '未上传证书附件'
            } else if (row.expireTime == '' && row.filePath) {
                return '未填写到期时间'
            } else {
                return '未上传证书附件且未填写到期时间'
            }
        },
    },
};
</script>

<style scoped lang="less">
.tab {
    background-color: #21274D;
    margin: -20px 0;
    height: calc(100vh - 90px);

    ::-webkit-scrollbar {
        width: 6px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #21274D;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #888;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

}

.bot {
    margin-bottom: 20px;
}

.searchBoxBg {
    background: #383d5f;
}



.title {
    height: 66px;
    line-height: 66px;
}

.search {
    height: 60px;
    line-height: 60px;
    margin-bottom: 15px;

    /deep/ .el-input .el-input__inner {
        background-color: rgba(255, 255, 255, 0.25) !important;
        color: rgba(199, 199, 199, 1) !important;
        border: 0px;
    }
}

.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 148px;
    height: 148px;
    line-height: 148px;
    text-align: center;
}

.avatar {
    width: 148px;
    height: 148px;
    display: block;
}

@media screen and (min-width: 600px) and (max-width: 1300px) {
    .tableT {
        margin-bottom: -5px
    }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
    .tableT {
        margin-bottom: 30px
    }
}
</style>
