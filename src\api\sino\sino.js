import request from '@/utils/request'
import qs from 'qs'

export const thirdPartyLogin = (para)=>{
    return new Promise((resolve)=>{
        request.post("/thirdPartyLogin?code=" + para.code + '&passCode=' + para.passCode + '&userId=' + para.userId).then(res=>{
            resolve(res)
        })
    })
}
export const thirdLogin = (para)=>{
  return new Promise((resolve, reject)=>{
      console.log('WeChat login request, code:', para.code);
      request.post("/thirdLogin?code=" + para.code).then(res=>{
          console.log('WeChat login API response:', res);
          resolve(res)
      }).catch(error => {
          console.error('WeChat login API request failed:', error);
          reject(error)
      })
  })
}
export const loginOut = (userName,buleiToken)=>{
    return new Promise((resolve)=>{
        request.post("/loginOut?username=" + userName +'&saber3-access-token='+ buleiToken).then(res=>{
            resolve(res)
        })
    })
}
//其它系统退出
export const loginQuit = (pend)=>{
  return new Promise((resolve)=>{
      request.get(`${pend}`).then(res=>{
          resolve(res)
      })
  })
}
export const listPowers = () => {
    return new Promise((resolve) => {
      request.get("/poweraction/listPowers").then((res) => {
        resolve(res);
      });
    });
  };


  export const getWechatUserInfo = (param) => {
    return new Promise((resolve) => {
      request.post("/pendingProcTask/getWechatUserInfo", param).then((res) => {
        resolve(res);
      });
    });
  };
  //待办事项
  export const todoList = (sys,param)=>{
    return new Promise((resolve)=>{
      let headerParams = {
        headers : {
            'Content-Type': 'application/json;charset=UTF-8',
        },
        dataType:'json'
    }
        request.post("/todo/todoList"+sys, param,headerParams).then(res=>{
            resolve(res)
        })
    })
}
//待办事项--快捷报销
export const todoListQUICK = (param) => {
    return new Promise((resolve) => {
      request.post("/todo/todoListQUICK", param).then((res) => {
        resolve(res);
      });
    });
};

export const loginToken = (token) => {
  return new Promise((resolve) => {
    request.get("/loginToken?token=" + token).then((res) => {
      resolve(res);
    });
  });
};

export const getUserNum = () => {
  return new Promise((resolve) => {
    request.get("/userNum").then((res) => {
      resolve(res);
    });
  });
};
//选择不再提示
export const saveRemFlag = (param) => {
  return new Promise((resolve) => {
    let headerParams = {
      headers : {
          'Content-Type': 'application/x-www-form-urlencoded'
      }
  }
    request.post("/saveRemFlag",qs.stringify(param),headerParams).then((res) => {
      resolve(res);
    });
  });
};
//获取是否选择不再提示
export const getRemFlag = (param) => {
  return new Promise((resolve) => {
    let headerParams = {
      headers : {
          'Content-Type': 'application/x-www-form-urlencoded'
      }
  }
    request.post("/getRemFlag",qs.stringify(param),headerParams).then((res) => {
      resolve(res);
    });
  });
};
export const getHelpPDF = () => {
  return new Promise((resolve) => {
    request.get("/appPerm/getHelpPDF").then((res) => {
      resolve(res);
    });
  });
};
export const getContractId = (parms) => {
    return new Promise((resolve) => {
        request.get("/todo/getContractId?processId="+parms.processId).then((res) => {
            resolve(res);
        });
    });
};
export const updPassword = (param) => {
  return new Promise((resolve) => {
    let headerParams = {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    };
    request
      .post("/updPassword", qs.stringify(param), headerParams)
      .then((res) => {
        resolve(res);
      });
  });
};

//身份证过期验证
export const saveIdentityCard = (param)=>{
    return new Promise((resolve)=>{
        request.post("/identityCard/saveIdentityCard?card1=" + param.card1 + '&card2=' + param.card2 + '&identityValidity=' + param.identityValidity).then(res=>{
            resolve(res)
        })
    })
}
//身份证过期验证
export const getIdentityCardState = () => {
    return new Promise((resolve) => {
        request.get("/identityCard/getIdentityCardState").then((res) => {
            resolve(res);
        });
    });
};


//身份证过期验证
export const  getUserInfoDetail = (param) => {

  return new Promise((resolve) => {
      request.get("/staff/getLoginEntryUser?userName="+param ).then((res) => {
          resolve(res);
      });
  });
};
