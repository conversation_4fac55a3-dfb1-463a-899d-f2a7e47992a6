<template>
  <div class="tab">
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23">
        <div  class="title_header">角色管理</div>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center" class="search">
      <el-col :span="23">
        <el-row type="flex" justify="space-between">
          <el-col :span="12" style="text-align: left">
            <el-button class="btnStyle" type="primary" style=" background-color: #0E61C9;" size="medium"
                       @click="addmenu">新增
            </el-button>
            <el-button class="buttonStyle" type="primary" style=" background-color:#31C97E;" size="medium"
                       @click="edit">修改
            </el-button>
            <el-button class="buttonStyle" type="primary" style=" background-color: #FD5252;" size="medium"
                       @click="deletemenu">删除
            </el-button>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-input placeholder="请输入内容" @keyup.enter.native="search" v-model.trim="searchword">
              <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer" @click="search"></i>
            </el-input>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center">
      <el-col :span="23">
        <el-table :data="tableData" class="tableT" ref="multipleTable" height="calc(100vh - 268px)" style="width: 100%;"
          :row-key="getRowKey" highlight-current-row @current-change="handleCurrentChange">
          <el-table-column prop="roleName" label="角色名称"> </el-table-column>
          <el-table-column prop="description" label="角色描述">
          </el-table-column>
          <el-table-column label="操作" width="300">
            <template slot-scope="scope">
              <span>
                <el-button type="success" size="mini" plain @click="() => menuclick(scope.row)">
                  配置菜单
                </el-button>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <div class="pager">
          <el-pagination @size-change="handleSizeChange" @current-change="pagehandleCurrentChange"
            :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pagesize"
            layout="total, sizes, prev, pager, next, jumper" :total="totalcount"></el-pagination>
        </div>
      </el-col>
    </el-row>

    <el-dialog title="新增角色" :visible.sync="adddialogVisible" width="560px" :show-close="false" class="addP">
      <div style="text-align: left">
        <div class="login-style">
          <div class="basicinfo" @click="clickbasicinfo" :class="{ account: showbasicinfo }">角色信息</div>
          <div class="moreinfo" @click="clickmoreinfo" :class="{ account: !showbasicinfo }">对应组织</div>
        </div>
        <el-form label-position="left" label-width="100px" :rules="addrule" ref="addform" :model="addform">
          <el-col v-if="showbasicinfo" style="margin-top:20px">
            <el-form-item label="角色名称" prop="roleName">
              <el-input placeholder="请输入角色名称" style="width: 274px" v-model="addform.roleName" maxlength="20"
                show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="角色描述" prop="description">
              <el-input placeholder="请输入角色描述" style="width: 274px" v-model="addform.description" maxlength="20"
                show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="!showbasicinfo" style="margin-top:20px">
            <el-form-item label="组织编号" prop="orgCode">
              <el-input placeholder="请输入组织编号" style="width: 274px" v-model="addform.orgCode" maxlength="20"
                show-word-limit></el-input>
            </el-form-item>
          </el-col>

        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="OKButton" :disabled="adddis" @click="resumesend('addform')">确定</el-button>
        <el-button @click="addmenuClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="修改角色" :visible.sync="editdialogVisible" width="560px" :show-close="false" class="addP">
      <div style="text-align: left">
        <el-form label-position="left" label-width="100px" :rules="editrule" ref="editform" :model="editform">
          <el-form-item label="角色名称" prop="roleName">
            <el-input placeholder="请输入角色名称" style="width: 274px" v-model="editform.roleName" maxlength="20"
              show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="角色描述" prop="description">
            <el-input placeholder="请输入角色描述" style="width: 274px" v-model="editform.description" maxlength="20"
              show-word-limit></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="OKButton" :disabled="editdis" @click="editmunusend('editform')">确定</el-button>
        <el-button @click="editmenuClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="配置菜单" :visible.sync="menudialogVisible" width="35%" :show-close="false" class="addP">
      <div class="viewtree">
        <el-tree class="modal-body" :data="postypelist" show-checkbox default-expand-all node-key="menuId" ref="tree"
          highlight-current :props="defaultProps" :expand-on-click-node="false"
          :default-checked-keys="[resourceCheckedKey]">
        </el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="OKButton" :disabled="confdis" @click="menusend()">确定</el-button>
        <el-button @click="menuClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog title="角色权限配置" :visible.sync="configuredialogVisible" width="65%" :show-close="false" class="addP">
      <div style="text-align: center;">
        <el-transfer class="powerlabel" style="text-align: left; display: inline-block;" v-model="value" filterable
          :render-content="renderFunc" :titles="['所有权限', '配置权限']" :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}',
          }" :data="transferData">
        </el-transfer>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="OKButton" :disabled="confdis" @click="confsend()">确定</el-button>
        <el-button @click="confClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="删除" :visible.sync="deletedialogVisible" width="30%" :show-close="false" class="deletemenu">
      <div style="text-align: center">
        <i class="el-icon-warning" style="font-size: 20px; color: #fcb543"><span
            style="font-size: 16px; color: #333; margin-left: 12px">确定删除该角色吗？</span></i>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="OKButton" :disabled="deldis" @click="deletesend">确 定</el-button>
        <el-button @click="deleteClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { listMenus } from "@/api/menu/menu";
import { listPowers } from "@/api/sino/sino";
import { getAllRole, addRole, updateRole, getSingleRole, delRole, getSingleMenu, addMenu, getSinglePower, addPower } from "@/api/sino/roleUrl";

export default {
  name: "rolemanage",
  data() {
    return {
      showbasicinfo: true,
      transferData: [],
      value: [],
      renderFunc(h, option) {
        return <span>{option.label}</span>;
      },
      showmoreinfo: false,
      tableData: [],
      checkedData: null,
      searchword: "",
      currentPage: 1,
      pagesize: 10,
      totalcount: null,
      adddialogVisible: false,
      addform: {
        roleName: "",
        description: "",
        // menuIds: [],
        orgCode: ""
      },
      addrule: {
        roleName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
        ],
      },
      postypelist: [],
      powerlist: [],
      props: {
        multiple: true,
        checkStrictly: true,
        emitPath: false,
        value: "menuId",
        label: "menuName",
        children: "children",
      },
      adddis: false,
      editdialogVisible: false,
      editform: {
        roleName: "",
        description: "",
      },
      editrule: {
        roleName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
        ],
      },
      editdis: false,
      deldis: false,
      confdis: false,
      configId: "",
      menusId: "",
      deletedialogVisible: false,
      configuredialogVisible: false,
      menudialogVisible: false,
      menuIds: [],
      menuarr: [],
      resourceCheckedKey: [],
      defaultProps: {
        value: "menuId",
        children: 'children',
        label: "menuName",
      }
    };
  },
  created() {
    this.getTreeData();
    listMenus().then((res) => {
      if (res.code == 200) {
        this.postypelist = res.data;
      }
    });

    listPowers().then((res) => {
      const allData = res.data;
      const data = [];
      for (let i = 0; i < allData.length; i++) {
        data.push({
          key: allData[i].powId,
          label: allData[i].powName,
        });
      }
      this.transferData = data;
    })
      .catch((error) => {
        this.$message.error(error);
      });
  },
  methods: {
    getTreeData() {
      getAllRole({ pagesize: this.pagesize, currentPage: this.currentPage, searchword: this.searchword }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.RoleSubject;
          this.totalcount = res.data.totalCount;
        }
      });
    },
    ifchecked(row) {
      this.checkedData = null;
      this.tableData.forEach((item) => {
        // 排他,每次选择时把其他选项都清除
        if (item.id !== row.id) {
          item.checked = false;
        }
      });
      if (row.checked) {
        this.checkedData = row;
      } else {
        this.checkedData = null;
      }
    },
    clickbasicinfo() {
      this.showbasicinfo = true;
    },
    clickmoreinfo() {
      this.showbasicinfo = false;
      this.$refs['addform'].clearValidate();
    },
    addmenuClose() {
      this.adddialogVisible = false;
      this.showmoreinfo = false;
      this.adddis = false;//
      this.$refs.addform.resetFields();
    },
    resumesend(formname) {
      if (!this.addform.roleName) {
        this.$message.warning({
          message: '请填写角色名称!'
        });
        this.showbasicinfo = true;
        return false;
      } else {
        this.$refs[formname].validate((valid) => {
          if (valid) {
            this.adddis = true;//确定按钮不可编辑
            // this.addform.menuIds = this.addform.menuIds.join(",");
            addRole(this.addform).then((res) => {
              if (res.code == 200) {
                this.adddis = false;
                this.adddialogVisible = false;
                this.getTreeData();
                this.$refs.addform.resetFields();
                document.documentElement.style.overflow = "auto";
                this.$message({
                  message: "新增成功",
                  type: "success",
                });
              } else {
                this.adddis = false;
                this.adddialogVisible = false;
                this.$message({
                  message: res.message || "新增失败",
                  type: "error",
                });
              }
            });
          } else {
            console.log("error submit!!");
            return false;
          }
        });

      }

    },
    //新增角色
    addmenu() {
      this.adddialogVisible = true;
      this.$nextTick(() => {
        this.$refs.addform.resetFields();
      });
      this.adddis = false;
      this.showmoreinfo = false;
    },
    showmore() {
      this.showmoreinfo = !this.showmoreinfo;
    },
    //编辑角色
    editmunusend(formname) {
      this.$refs[formname].validate((valid) => {
        if (valid) {
          this.editdis = true;
          // if(this.editform.menuIds){
          //   this.editform.menuIds = this.editform.menuIds.join(",");
          // }
          updateRole(this.editform).then((res) => {
            if (res.code == 200) {
              this.editdis = false;
              this.editdialogVisible = false;
              this.getTreeData();
              this.$refs.editform.resetFields();
              document.documentElement.style.overflow = "auto";
              this.$message({
                message: "修改成功",
                type: "success",
              });
            } else {
              this.editdialogVisible = false;
              this.editdis = false;
              this.$message({
                message: res.message || "修改失败",
                type: "error",
              });
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    editmenuClose() {
      this.editdialogVisible = false;
      this.showmoreinfo = false;
      this.editdis = false;
      this.$refs.editform.resetFields();//对整个表单进行重置
    },
    edit() {
      if (this.currentRow) {
        getSingleRole(this.currentRow.roleId
        ).then(res => {
          if (res.code == 200) {
            this.editform.roleName = res.data.roleName;
            this.editform.description = res.data.description;
            this.editform.roleId = res.data.roleId;
          }
        })
        this.editdialogVisible = true;
        this.$nextTick(() => {
          this.$refs.editform.resetFields()
        })
        this.editdis = false;
        this.showmoreinfo = false;
      } else {
        document.documentElement.style.overflow = "hidden";
        //选择多个表格行，展示的弹出框
        this.$message.warning({
          message: '未选择表格数据!'
        });
      }
    },
    deletemenu() {
      if (this.currentRow) {
        this.deletedialogVisible = true;
      } else {
        document.documentElement.style.overflow = "hidden";
        //选择多个表格行，展示的弹出框
        this.$message.warning({
          message: '未选择表格数据!'
        });
      }
    },
    //删除角色
    deletesend() {
      this.deletedialogVisible = false;
      this.deldis = false;
      delRole(this.currentRow.roleId).then((res) => {
        if (res.code == 200) {
          this.deletedialogVisible = false;
          this.deldis = false;
          this.getTreeData();
          this.$message({
            message: '删除成功！',
            type: 'success'
          });
          this.currentRow = null;
          this.$refs.multipleTable&&this.$refs.multipleTable.setCurrentRow();//取消选中
          document.documentElement.style.overflow = "auto";
        } else {
          this.deletedialogVisible = false;
          this.deldis = false;
          this.$message.error({
            message: res.message || '删除失败！'
          });
        }
      });
    },
    deleteClose() {
      this.deletedialogVisible = false;
      this.deldis = false;
    },
    search() {
      this.currentPage = 1;
      this.getTreeData();
    },
    //修改表格分页的展示条数
    handleSizeChange(val) {
      this.pagesize = val;
      this.getTreeData();
    },
    //修改表格分页的当前页
    pagehandleCurrentChange(val) {
      this.currentPage = val;
      this.getTreeData();
    },
    handleCurrentChange(val) {
      this.currentRow = val;
    },
    menuclick(rowData) {
      this.menusId = rowData.roleId;
      this.menudialogVisible = true;
      this.menuarr = []
      this.$nextTick(() => {
        this.$refs.tree&&this.$refs.tree.setCheckedKeys([]);//设置目前勾选的节点为空
      });
      getSingleMenu(rowData.roleId
      ).then(res => {
        if (res.code == 200) {
          if (res.data.length > 0) {
            this.menuarr = res.data//后端返回的所有id
            this.menuarr.forEach(i => {
              var node = this.$refs.tree&&this.$refs.tree.getNode(i);//根据id 拿到 Tree 组件中的node的所有信息
              if (node.isLeaf) {//node.isLeaf：判断当前节点是否为子节点
                this.$refs.tree&&this.$refs.tree.setChecked(node, true);//如果是子节点，就把状态设置成选中
              }
            })
          }

        }
      })
    },
    // 配置菜单
    menusend() {
      let menuKeys = this.$refs.tree.getCheckedKeys()//返回目前被选中的节点的 key 所组成的数组
      let halfkey=this.$refs.tree.getHalfCheckedKeys()
      menuKeys=menuKeys.concat(halfkey)
      addMenu({
        roleId: this.menusId,
        menuIds: menuKeys.join(","),
      }).then((res) => {
        if (res.code == 200) {
          this.menudialogVisible = false;
          this.menuarr = []
          this.$nextTick(() => {
            this.$refs.tree&&this.$refs.tree.setCheckedKeys([]);//设置目前勾选的节点为空
          });
          this.getTreeData();
          document.documentElement.style.overflow = "auto";//控制主页面的滚动条
          this.$message({
            message: "配置菜单成功",
            type: "success",
          });
        } else {
          this.$message({
            message: res.message || "配置菜单失败",
            type: "error",
          });
        }
      })
    },
    menuClose() {
      this.menudialogVisible = false;
      this.$refs.multipleTable.clearSelection();//清空用户的选择
    },

    handleChange(value, direction, movedKeys) {
      console.log(value, direction, movedKeys);
    },
    getRowKey(row) {
      return row.roleId;
    },
    confsend() {
      addPower({
        roleId: this.configId,
        powers: this.value.join(","),
      })
        .then((res) => {
          if (res.code == 200) {
            this.configuredialogVisible = false;
            this.getTreeData();
            this.$refs.multipleTable.clearSelection();//清空用户的选择
            this.value = [];
            document.documentElement.style.overflow = "auto";
            this.$message({
              message: "配置权限成功",
              type: "success",
            });
          } else {
            this.$message({
              message: res.message || "配置权限失败",
              type: "success",
            });
          }
        });
    },
    confClose() {
      this.value = [];
      this.configuredialogVisible = false;
      this.$refs.multipleTable.clearSelection();//清空用户的选择
    },
  },
};
</script>

<style scoped lang="less">
.tab {
  background-color: #21274D;
  margin: -20px 0;
  height: calc(100vh - 90px);

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #21274D;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.title {
  height: 66px;
  line-height: 66px;
  // border-bottom: 1px solid #eee;
  /*margin-bottom: 10px;*/

  /deep/ .el-input .el-input__inner {
    //border-radius: 34px !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(199, 199, 199, 1) !important;
    border: 0px;
    width: 306px;
  }
}

.search {
  height: 40px;
  line-height: 40px;
  // border-bottom: 1px solid #eee;
  margin-bottom: 15px;

  /deep/ .el-input .el-input__inner {
    //border-radius: 34px !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(199, 199, 199, 1) !important;
    border: 0px;
    width: 306px;
  }
}
.pager {
  margin-top: 20px;
}

.pager {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-top: 10px;
  margin-bottom: 10px;
}

.el-pagination {
  float: right;
}

.modal-body {
  position: relative;
  max-height: 360px;
  padding: 15px;
  overflow-y: auto;
}

.basicinfo,
.moreinfo {
  float: left;
  width: 49%;
  height: 30px;
  color: #333;
  margin-bottom: 5px;
  border-bottom: 1px solid #f4f4f4;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  font-family: "microsoft yahei";
  font-size: 16px;
}

.basicinfo {
  border-right: 1px solid #f4f4f4;
}

.account {
  color: #389AE2 !important;
  font-weight: bold;
}

.viewtree {
  text-align: left;

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #fff;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #333;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #777;
  }
}

@media screen and (min-width: 600px) and (max-width: 1300px) {
  .tableT {
    margin-bottom: -5px
  }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
  .tableT {
    margin-bottom: 30px
  }
}
</style>
