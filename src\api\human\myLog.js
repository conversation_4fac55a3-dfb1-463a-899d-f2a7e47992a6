import request from "@/utils/request";
import qs from "qs";
import queryString from "query-string";
//获取所有角色
export const getAllRole = (param) => {
  return new Promise((resolve) => {
      request.get("/role/getAllRole?pageSize=" + param.pagesize + "&&pageNum=" +
          param.currentPage + "&&sSearch=" + param.searchword
      ).then((res) => {
          resolve(res);
      });
  });
};
//获取列表
// export const getWorkDaily = (param) => {
//     return new Promise((resolve) => {
//         let headerParams = {
//             headers : {
//                 'Content-Type': 'application/x-www-form-urlencoded'
//             }
//         }
//         request.post("/workDiary/listWorkDaily", qs.stringify(param),headerParams).then((res) => {
//             resolve(res);
//         });
//     });
// };
export const getWorkDaily = (qs) => {
    return new Promise((resolve) => {
        request
            .get("/workDiary/listWorkDaily?" + queryString.stringify(qs))
            .then((res) => {
                resolve(res);
            });
    });
};