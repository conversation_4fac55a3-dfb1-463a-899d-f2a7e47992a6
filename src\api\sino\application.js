import request from "@/utils/request";
import qs from "qs";
export const getAllStaffByOrgs = (id) => {
  let str = id
    ? `/app/getAllStaffByOrgs?id=${id}&state=1`
    : "/app/getAllStaffByOrgs?state=1";
  return new Promise((resolve) => {
    request.get(str).then((res) => {
      resolve(res);
    });
  });
};

export const saveApp = (param) => {
  return new Promise((resolve) => {
    let headerParams = {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      dataType:'json'
    };
    request.post("/app/saveApp", param,headerParams).then((res) => {
      resolve(res);
    });
  });
};
//删除
export const deleteAll = (param) => {
  let headerParams = {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  };
  return new Promise((resolve) => {
    request
      .post("/app/deleteAll", qs.stringify(param), headerParams)
      .then((res) => {
        resolve(res);
      });
  });
};

export const searchApp = () => {
  return new Promise((resolve) => {
    request.get("/app/searchApp").then((res) => {
      resolve(res);
    });
  });
};
//获取详情
export const listAppAll = (param) => {
  let headerParams = {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  };
  return new Promise((resolve) => {
    request
      .post("/app/listAppAll", qs.stringify(param), headerParams)
      .then((res) => {
        resolve(res);
      });
  });
};
//修改应用
export const updateAppAndPower = (param) => {
  return new Promise((resolve) => {
    let headerParams = {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      dataType:'json'
    };
    request.post("/app/updateAppAndPower", param, headerParams).then((res) => {
      resolve(res);
    });
  });
};
