<template>
  <div class="tab" id="version">
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23">
        <div class="title_header">规章制度发布</div>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center" class="search">
      <el-col :span="23" class="searchBox">
        <template>
          <el-col :span="8" style="text-align: left">
            <el-button
              class="btnStyle"
              type="primary"
              style="background-color: #0e61c9; width: 80px"
              size="medium"
              @click="openAddDialog"
              >添加
            </el-button>
          </el-col>
          <el-col :span="16" style="text-align: right">
            <el-input
              placeholder="请输入文章名称"
              @keyup.enter.native="search()"
              style="width: 30%"
              v-model.trim="query.title"
              @input="search()"
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                style="cursor: pointer"
                @click="search()"
              ></i>
            </el-input>
          </el-col>
        </template>
      </el-col>
    </el-row>

    <el-row type="flex" justify="center">
      <el-col :span="23">
        <el-table
          ref="versionTable"
          :data="tableData"
          element-loading-background="rgb(33,39,77, 0.8)"
          height="calc(100vh - 290px)"
          style="width: 100%"
          class="tableT"
          v-loading="loading"
          highlight-current-row
        >
        
          <el-table-column label="文章名称" prop="title">
            <template slot-scope="props">
              <span
                @click="showDialogVisible(props.row)"
                style="color: #0e61c9; cursor: pointer"
                >{{ props.row.title }}</span
              >
            </template>
          </el-table-column>
          <el-table-column label="制度类别" prop="topicTypeValue"> </el-table-column>
          <el-table-column label="发布人" prop="creator"> </el-table-column>
          <el-table-column label="发布时间" prop="createtime">
            <template slot-scope="props">
              <span>{{ formatDate(props.row.createtime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="state">
            <template slot-scope="scope">
              <!-- State  1 已发布，3未发布 -->
              <span>{{ scope.row.state == 1 ? "已发布" : "未发布" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button @click="deleteNews(scope.row)" type="text" size="small"
                >删除</el-button
              >
              <el-button
                v-if="scope.row.state != 1"
                @click="pubLishNews(scope.row)"
                type="text"
                size="small"
                >发布</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="pager">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="pagehandleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[5, 10, 15]"
            :page-size="pagesize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalcount"
          ></el-pagination>
        </div>
      </el-col>
    </el-row>

    <!--添加-->
    <el-dialog
      ref="changeDialog"
      :title="titleDia"
      :visible.sync="adddialogVisible"
      width="900px"
      class="addP"
      :show-close="false"
      :close-on-click-modal="false"
      :before-close="addmenuClose"
    >
      <div style="text-align: left">
        <el-form
          label-position="left"
          label-width="120px"
          :rules="addrule"
          ref="addform"
          :model="addform"
        >
          <el-col :span="20">
            <el-form-item label="文章名称" prop="title">
              <el-input
                v-model.trim="addform.title"
                placeholder="请输入文章名称"
                maxlength="36"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :offset="1" :span="3">
            <el-button class="OKButton" @click="preview()">预览</el-button>
          </el-col>

          <el-col :span="24">
            <el-form-item label="文章内容" prop="contentText">
              <editor v-model="addform.contentText" :min-height="120" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="制度类别" prop="topicType">
              <el-select
                v-model="addform.topicType"
                placeholder="请选择制度类别"
              >
                <el-option
                  v-for="item in systemList"
                  :key="item.dmvalue"
                  :label="item.dmvaluemeaning"
                  :value="item.dmvalue"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否强制阅读" prop="popUp">
              <el-radio-group v-model="addform.popUp">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="addform.popUp == 1">
            <el-form-item label="截止时间" prop="deadlineTime">
              <el-date-picker
                style="width: 50%"
                size="small"
                v-model="addform.deadlineTime"
                :picker-options="pickerOptions"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择截止时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24" v-if="addform.popUp==1">
                        <el-form-item label="强制阅读内容" prop="fileUrl">
                            <el-upload class="upload-demo" ref="upload" :action="actionurl"
                                       style="width:50%"
                                       :show-file-list="true"
                                       v-model="addform.fileUrl"
                                       :on-preview="handlePreview" :on-remove="handleRemove" :on-success="handleASuccess"
                                       :before-upload="beforeUpload1" :file-list="fileList" multiple accept=".jpg,.jpeg,.png,.pdf">
                                <el-button size="small" type="primary">上传</el-button>
                               <div slot="tip" class="el-upload__tip"  style="color:#b8b6c0;margin-top:-10px">仅支持pdf/图片格式的文件</div>
                            </el-upload>
                        </el-form-item>
                    </el-col> -->
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <div style="width: 100%; text-align: right">
          <el-button
            class="OKButton"
            :disabled="adddis"
            @click="handleVersionAdd()"
            >确定</el-button
          >
          <el-button @click="addClose" class="CancButton">取 消</el-button>
        </div>
      </span>
    </el-dialog>

    <!--详情-->
    <el-dialog
      ref="detailDialog"
      title="查看"
      :visible.sync="detailDialogVisible"
      width="900px"
      class="addP"
      :fullscreen="true"
      :close-on-click-modal="false"
      :before-close="detailmenuClose"
      :show-close="false"
    >
      <div style="text-align: left">
        <div class="text01">{{ detailform.title }}</div>
        <div style="margin-top: 15px">
          <span class="text02"
            >时间：{{ formatDate(detailform.createtime) }}</span
          >
          <!-- <span class="clickTimes">点击次数：{{ detailform.clicktimes }}</span> -->
        </div>
        <div
          style="
            margin-top: 15px;
            border: #d2d2d2 solid 1px;
            background-color: #f1f1f1;
          "
          v-if="detailform.contentText && detailform.contentText.length > 0"
        >
          <EditorDteail
            id="contentText"
            v-model="detailform.contentText"
            :min-height="30"
            :showType="'table'"
          />
        </div>
      </div>
      <span
        slot="footer"
        class="dialog-footer"
        style="text-align: center; margin-top: 10px"
      >
        <el-button @click="detailmenuClose" class="CancButton">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getRecordList,
  createTopic,
  articleTopicGetDictList,
  publishRecordItem,
  deleteRecordItem,
} from "@/api/sino/newRelease";
import Editor from "@/components/Editor";
import EditorDteail from "@/components/Editor/detail.vue";

import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import moment from "moment";
export default {
  name: "newRelease",
  components: {
    Editor,
    EditorDteail,
  },
  data() {
    return {
      currentRow: null,
      //版本更新列表
      tableData: [],
      //版本更新列表查询参数
      query: {
        title: "",
        catalogid: "",
      },
      //版本更新详情页数据
      detailform: {
        title: "",
        createtime: "",
        clicktimes: "",
        catalogid: "",
        articleContent: {},
        contentText: "",
      },
      //版本更新loading
      loading: false,
      //用户手册loading
      currentPage: 1,
      pagesize: 10,
      searchword: "",
      totalcount: null,
      adddialogVisible: false,
      detailDialogVisible: false,

      //系统列表
      systemList: [],
      //添加 or  修改表单数据
      addform: {
        catalogid: "",
        title: "",
        contentText: "",
        popUp: 0,
        deadlineTime: "",
        fileUrl: "",
        topicType: "",
      },
      fileList: [],
      actionurl: process.env.VUE_APP_BASE_UPLOAD + "record/upload",
      paramsData: {},
      //弹框标题
      titleDia: "新增规章制度",
      //添加
      addrule: {
        catalogid: [
          { required: true, message: "请选择栏目分类", trigger: "change" },
        ],

        title: [{ required: true, message: "请输入文章标题", trigger: "blur" }],
        topicType: [
          { required: true, message: "请选择制度类别", trigger: "blur" },
        ],
        contentText: [
          {
            required: true,
            message: "请输入文章内容",
            trigger: ["change", "blur"],
          },
        ],
        popUp: [
          { required: true, message: "请选择是否强制阅读", trigger: "change" },
        ],
        deadlineTime: [
          { required: true, message: "请选择截止时间", trigger: "change" },
        ],
        fileUrl: [
          { required: false, message: "请选择上传文件", trigger: "change" },
        ],
      },
      adddis: false, //新增按钮状态
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 如果现在时间之前，则不可选
        },
      },
    };
  },
  computed: {},

  created() {
    //列表页获取
    this.search();
  },
  mounted() {
    articleTopicGetDictList("topicType").then((res) => {
      this.systemList = res.data;
    });
  },
  watch: {
    "addform.fileUrl": function (newVal) {
      if (newVal) {
        if (this.$refs.addform) {
          this.$refs.addform.validateField("fileUrl", () => {
            return true;
          });
        }
      }
    },
    "addform.contentText": function (newVal) {
      if (newVal) {
        if (this.$refs.addform) {
          this.$refs.addform.validateField("contentText", () => {
            return true;
          });
        }
      }
    },
  },
  methods: {
    deleteNews(item) {
      this.$confirm("确定删除该公告?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteRecordItem(item.id)
            .then((res) => {
              if (res.code == 200) {
                this.search();
              }
              this.$message({
                type: res.code == 200 ? "success" : "warning",
                message: res.data,
              });
            })
            .catch((e) => {
              this.$message({
                type: "error",
                message: "删除失败",
              });
            });
        })
        .catch(() => {});
    },
    pubLishNews(item) {
      this.$confirm("确定发布该公告?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          publishRecordItem({ id: item.id, state: 1 }).then((res) => {
            if (res.code == 200) {
              this.search();
            }
            this.$message({
              type: res.code == 200 ? "success" : "error",
              message: res.data,
            });
          });
        })
        .catch(() => {});
    },
    //查询置空
    handResertSearch() {
      this.query = {
        title: "",
        catalogid: "",
      };
    },
    //版本列表查询
    search() {
      this.currentPage = 1;
      this.getListDate();
    },

    //修改表格分页的展示条数
    handleSizeChange(val) {
      this.pagesize = val;
      this.getListDate();
    },
    //修改表格分页的当前页
    pagehandleCurrentChange(val) {
      this.currentPage = val;
      this.getListDate();
    },
    // 修改表格分页的展示条数
    handleCurrentChange(val) {
      this.currentRow = val;
    },
    //获取列表数据
    getListDate() {
      this.loading = true;
      let param = {
        ...this.query,
        pageNum: this.currentPage,
        pageSize: this.pagesize,
      };
      getRecordList(param).then((res) => {
        this.tableData = res.data.staffSubject;
        this.totalcount = res.data.totalCount;
        this.loading = false;
      });
    },

    // 添加弹框取消
    addmenuClose() {
      this.adddialogVisible = false;
      this.adddis = false;
      this.$refs.addform.resetFields();
      this.handleReset();
    },

    showDialogVisible(row) {
      this.detailDialogVisible = true;
      this.detailform = {
        ...row,
        contentText: row.articleContent.contentText
          ? row.articleContent.contentText
          : "",
      };
    },
    preview() {
      let that = this;
      this.$refs.addform.validate((valid) => {
        if (valid) {
          that.detailDialogVisible = true;
          that.detailform = {
            title: that.addform.title,
            createtime: new Date(),
            clicktimes: "0",
            topicType: that.addform.topicType,
            catalogid: that.addform.catalogid,
            contentText: that.addform.contentText
              ? that.addform.contentText.toString()
              : "",
          };
        }
      });
    },
    //时间格式化
    formatDate(dateStr) {
      return moment(dateStr).format("YYYY-MM-DD");
    },

    //新增证书
    handleRemove(file) {
      this.addform.fileUrl = "";
      this.fileList = this.fileList.filter((item) => item.url !== file.url);
    },
    // -上传服务器之前逻辑处理
    beforeUpload1(file) {
      //上传的文件只能是图片或pdf文件，且大小不能超过10MB
      const isJPGOrPdf =
        file.type === "image/jpeg" ||
        file.type === "image/png" ||
        file.type === "image/jpg" ||
        file.type === "application/pdf";
      const isLt2M = file.size / 1024 / 1024 < 10;
      if (!isJPGOrPdf) {
        this.$message.error("只能上传图片或pdf文件!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 10MB!");
      }
      return isJPGOrPdf && isLt2M;
    }, //成功上传
    handleASuccess(res, file) {
      this.addform.fileUrl = res.data;
      this.fileList.push({
        name: file.name,
        url: res.data,
      });
    },
    //上传
    handlePreview(file) {
      let URL = window.URL || window.webkitURL;
      if (!!window.ActiveXObject || "ActiveXObject" in window) {
        this.$message.warning("暂不支持预览");
      } else {
        if (file.raw) {
          window.open(URL.createObjectURL(file.raw)); //blob格式地址
        } else {
          window.open(file.url); //blob格式地址
        }
      }
    },
    //关闭更新详情弹框
    detailmenuClose() {
      this.detailDialogVisible = false;
      this.detailform = {
        title: "",
        createtime: "",
        clicktimes: "",
        catalogid: "",
        articleContent: {},
        contentText: "",
      };
    },

    //打开添加弹框
    openAddDialog() {
      this.handleReset();
      this.adddialogVisible = true;
    },
    //添加记录
    handleVersionAdd() {
      this.$refs.addform.validate((valid) => {
        if (valid) {
          this.adddis = true;
          let filePath = this.fileList.map((item) => item.url).join(",");
          createTopic({
            creator: window.localStorage.getItem("Susername"),
            catalogid: "2000",
            title: this.addform.title,
            articleContent: { contentText: this.addform.contentText },
            popUp: this.addform.popUp,
            topicType: this.addform.topicType,
            deadlineTime: this.addform.deadlineTime,
            fileUrl: filePath,
          }).then((res) => {
            if (res.code == 200) {
              this.currentPage = 1;
              this.getListDate();
              this.$message({
                message: "成功！",
                type: "success",
              });
              this.adddis = false;
              this.addClose();
              this.adddialogVisible = false;
            } else {
              this.adddis = false;
              this.$message.error({
                message: res.message || "失败！",
              });
            }
          });
        }
      });
    },
    //关闭 添加弹框
    addClose() {
      this.adddialogVisible = false;
      this.adddis = false;
      this.$refs.addform.resetFields();
      this.addform.fileUrl = "";
      this.fileList = [];
    },

    //置空操作
    handleReset() {
      this.addform = {
        catalogid: "",
        title: "",
        contentText: "",
        popUp: 0,
        deadlineTime: "",
        fileUrl: "",
        topicType: "",
      };
      this.detailform = {
        title: "",
        createtime: "",
        clicktimes: "",
        catalogid: "",
        articleContent: {},
        contentText: "",
      };

      this.$nextTick(() => {
        if (this.$refs.addform) {
          this.$refs.addform.resetFields();
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.tab {
  background-color: #21274d;
  margin: -20px 0;
  height: calc(100vh - 90px);

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #21274d;
  }

  /* Handle */

  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */

  ::-webkit-scrollbar-thumb:hover {
    //background: #555;
  }
}

.pager {
  width: 100%;
  overflow: hidden;
  position: relative;
  margin-top: 10px;
  margin-bottom: 10px;
}

.el-pagination {
  float: right;
}

.title {
  height: 66px;
  line-height: 66px;

  /deep/ .el-input .el-input__inner {
    //border-radius: 34px !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(199, 199, 199, 1) !important;
    border: 0px;
  }
}

.search {
  height: 70px;
  line-height: 40px;

  .searchBox {
    padding: 10px 0;
    position: relative;
  }

  /deep/ .el-input .el-input__inner,
  .el-range-editor.el-input__inner,
  .el-range-editor.el-range-input,
  .el-range-editor.el-range-separator {
    //border-radius: 34px !important;
    background-color: rgba(255, 255, 255, 0.25) !important;
    color: rgba(199, 199, 199, 1) !important;
    border: 0px;
  }
}

.add,
.edit {
  color: #fff;
  margin-left: 15px;
  border: none;
}

.add {
  background-color: #037cb3;
}

.edit {
  background-color: #17b3d9;
}

.sure {
  background: none repeat scroll 0 0 #61b0e9;
  border-color: #389ae2;
  color: #fff;
  text-shadow: none;
  text-transform: uppercase;
}

.text01 {
  font-size: 30px;
  font-family: "微软雅黑";
  color: #08657e;
  margin-left: 15px;
}

.text02 {
  font-size: 18px;
  font-family: "微软雅黑";
  color: #767676;
  margin-left: 15px;
}

.clickTimes {
  font-size: 12px;
  margin-left: 15px;
}

/* 13寸 */
@media screen and (min-width: 600px) and (max-width: 1300px) {
  .tableT {
    margin-bottom: -5px;
  }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
  .tableT {
    margin-bottom: 30px;
  }
}

/deep/ .addP .el-dialog__footer {
  text-align: center !important;
}

ul {
  list-style-type: disc !important;
}

/deep/ .el-loading-mask {
  background-color: #21274d !important;
}

/deep/ #haneleDetailId .el-loading-mask {
  background-color: rgba(255, 255, 255, 1) !important;
}

.myIcon {
  color: #409eff;
  padding: 0 5px;
  cursor: pointer;
}

/deep/ .el-icon-d-arrow-right {
  font-size: 15px !important;
  color: white !important;
}

/deep/ #recordContent .ql-editor {
}

/deep/ #recordContent li {
  padding: 0px 0px !important;
  line-height: 25px;
}

/deep/ #recordContent .ql-editor ol {
  padding-left: 30px !important;
}

/deep/ #recordContent .ql-editor ul {
  margin-left: -42px !important;
  margin-top: -5px;
}

/deep/ #recordContent .ql-editor p {
  padding-left: 0px !important;
}

/deep/ #recordContent .ql-editor li:not(.ql-direction-rtl)::before {
  color: #d8d8d8;
  font-size: 38px;
  vertical-align: middle;
  /* 调整垂直对齐方式 */
}

/deep/ strong {
  font-weight: bold !important;
}

/deep/ .addP .el-form-item__label {
  color: #939393;
}

/deep/ .search .el-range-editor.el-input__inner {
  width: 100%;
}

/deep/ #updateDetail {
  .el-form-item {
    margin: 0;
  }
}
::v-deep .el-upload-list__item {
  transition: none !important;
  -webkit-transition: nonne !important;
}
::v-deep .el-upload-list__item-name {
  transition: none !important;
  -webkit-transition: nonne !important;
}
</style>
