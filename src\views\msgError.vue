<template>
    <div class="outDiv">
        <div style="text-align:center">
            <img src="../assets/images/sign.png" alt="logo" class="imgSty" />
            <div style="color:#2C3D6E;font-size: 28px;font-weight: bold;" id="msg">{{ msg || '验证失败'}}</div>
            <div style="color:#A9A9A9;font-size: 16px;margin-top: 30px">{{ tip||'请重新登录' }}</div>
        </div>
    </div>

</template>

<script>
export default {
    name: "msgError",
    data() {
        return {
            msg: '',
            tip:''
        };
    },
    created() {
    //根据code显示对应的信息
        if (window.location.href.indexOf("code") != -1) {
            let indexOne = window.location.href.indexOf("code=");
            var indexTwo = window.location.href.lastIndexOf("#/");
            let code = window.location.href.substring(indexOne + 5, indexTwo);
            if (code == 1001) {
                this.msg = '系统认证失败'
                this.tip = '请联系管理员'
            } else if (code == 1003) {
                this.msg = '权限认证失败'
                this.tip = '请联系管理员'
            }
            else {
                this.msg = '验证失败'
                this.tip = '请重新登录'
            }
        }
    },
    methods: {

    }

}
</script>

<style scoped>
.outDiv {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    overflow: hidden;
}
.imgSty{
    height: 500px;
}
@media screen and (min-width: 600px) and (max-width: 1300px) {
    .imgSty {
        height: 400px;
    }
}
</style>
