{"name": "authon", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"animate.css": "^3.7.2", "axios": "^0.19.0", "core-js": "^2.6.5", "element-ui": "^2.13.0", "js-base64": "^3.7.7", "lodash": "^4.17.15", "moment": "^2.24.0", "nprogress": "^0.2.0", "qs": "^6.9.0", "query-string": "^6.13.7", "quill": "^1.3.7", "tesseract.js": "^5.1.1", "vue": "^2.6.10", "vue-print-nb": "^1.7.5", "vue-router": "^3.0.3", "vuex": "^3.1.1", "vuex-persistedstate": "^2.7.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.12.0", "@vue/cli-plugin-eslint": "^3.12.0", "@vue/cli-service": "^3.12.0", "babel-eslint": "^10.0.1", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "less": "^3.0.4", "less-loader": "^5.0.0", "sm-crypto": "^0.3.13", "vue-template-compiler": "^2.6.10"}}