const path = require('path')
const resolve = (dir) => path.join(__dirname, dir)
module.exports = {
  //baseUrl: './',
  publicPath: '/oa/oa',
  runtimeCompiler: true,
  chainWebpack: (config) => {
    config.resolve.alias.set('@', resolve('src'))
  },
  // devServer: {
  //   proxy: {
  //     // '/api': {
  //     //     // target: 'http://192.168.0.103:8099',
  //     //     // target: 'http://192.168.177.34:8099',
  //     //     // target: 'http://172.16.4.18:8099',
  //     //     target: 'http://192.168.103.47:8099',
  //     //     // target: 'https://scrm.sino-bridge.com:8098',
  //     //     changeOrigin: true,
  //     //     ws: true,
  //     //     pathRewrite: {
  //     //         '^/api': ''
  //     //     }
  //     // },
  //     '/sso/ssoapi/': {
  //     //   //target: 'http://192.168.177.34:8099',
  //     //   // target: 'http://172.16.4.18:8099',
  //     //   target: 'http://192.168.179.66:8099',
  //     //   changeOrigin: true,
  //     //   ws: true,
  //     //   pathRewrite: {
  //     //     '^/sso/ssoapi/': '/',
  //     //   },
  //     // },
  //     '/api': {
  //     //   //target: 'http://192.168.177.34:8099',
  //     //   // target: 'http://172.16.4.18:8099',
  //     //   // target: 'http://192.168.179.66:8099',
  //     //   // target: 'http://192.168.103.47:8099',
  //     //   target: 'https://scrm.sino-bridge.com:8098/oa/oa-api',
  //     //   // target: 'https://scrm.sino-bridge.com:8098/oa',
  //     //   // target: 'http://192.168.213.120:8099',
  //     //   changeOrigin: true,
  //     //   ws: true,
  //     //   pathRewrite: {
  //     //     '^/api': '',
  //     //   },
  //     // },
  //     '/group1': {
  //     //   target: 'http://192.168.176.9:8888',
  //     //   changeOrigin: true,
  //     //   ws: true,
  //     //   pathRewrite: {
  //     //     '^/group1': '/group1',
  //     //   },
  //     // },
  //     '/sso/group1': {
  //     //   target: 'http://192.168.176.9:8888',
  //     //   changeOrigin: true,
  //     //   ws: true,
  //     //   pathRewrite: {
  //     //     '^/sso/group1': '/group1',
  //     //   },
  //     // },
  //   },
    devServer: {
        proxy: {
            // '/api': {
            //     // target: 'http://192.168.0.103:8099',
            //     // target: 'http://192.168.177.34:8099',
            //     // target: 'http://172.16.4.18:8099',
            //     target: 'http://192.168.103.47:8099',
            //     // target: 'https://scrm.sino-bridge.com:8098',
            //     changeOrigin: true,
            //     ws: true,
            //     pathRewrite: {
            //         '^/api': ''
            //     }
            // },
            '/sso/ssoapi/': {
                //target: 'http://192.168.177.34:8099',
                 // target: 'http://172.16.4.18:8099',
                target: 'http://192.168.179.66:8099',
                changeOrigin: true,
                ws: true,
                pathRewrite: {
                    '^/sso/ssoapi/': '/'
                }
            },
            '/api': {
                //target: 'http://192.168.177.34:8099',
                // target: 'http://172.16.4.18:8099',
                // target: 'http://192.168.179.66:8099',
                // target: 'http://192.168.103.47:8099',
                // target: 'http://192.168.103.75:8099',
                // target: 'http://192.168.213.83:8098/oa/oa-api',
                target: 'https://scrm.sino-bridge.com:8098/oa/oa-api',
                // target: 'http://p8c2aa.natappfree.cc',    
                changeOrigin: true,
                ws: true,
                pathRewrite: {
                    '^/api': ''
                }
            },
            // https://scrm.sino-bridge.com:8098/oa/blade-api/blade-system/user/listRoleUser
            '/rewnwuzhongxin': {
                target: 'https://scrm.sino-bridge.com:8098',
                changeOrigin: true,
                ws: true,
                secure: false,
                onProxyReq: function(proxyReq) {
                    proxyReq.setHeader('X-Forwarded-Proto', 'https');
                },
                pathRewrite: {
                    '^/api/rewnwuzhongxin': ''
                }
            },
  
            '/sso/group1': {
                target: 'http://192.168.176.9:8888',
                changeOrigin: true,
                ws: true,
                pathRewrite: {
                    '^/sso/group1': '/group1'
                }
            }
        }
    },
    productionSourceMap: false,
}
