<template>
    <div class="outDiv">
        <div style="text-align: center;height:40px;line-height:40px;color: aliceblue;">当前在线人数：{{ num }}</div>
        <div style="text-align: center;">
            <el-table :data="tableData" height="78vh" style="width: 60%;margin: 0 20%;" class="tableT">
                <el-table-column label="账号" prop="UserName"></el-table-column>
                <el-table-column label="姓名" prop="StaffName"></el-table-column>
                <el-table-column label="部门" prop="OrgName"></el-table-column>
            </el-table>
            <div class="pagerCount">
                <el-pagination @size-change="handleSizeChange" @current-change="pagehandleCurrentChange"
                    :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="totalCount"></el-pagination>
            </div>

        </div>
    </div>
</template>
<script>
import { getCountUser } from "@/api/app/app";

export default {
    name: "countUser",
    data() {
        return {
            tableData: [],
            num: '',
            currentPage: 1,
            pageSize: 10,
            totalCount: null,
        };
    },
    created() {
        this.getData()
    },
    methods: {
        getData() {
            getCountUser({
                pageSize: this.pageSize,
                currentPage: this.currentPage,
            }).then((res) => {
                this.num = res.data.total;
                this.tableData = res.data && res.data.list;
                this.totalCount = res.data.total;
            });
        },
        //修改表格分页的展示条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.getData();
        },
        //修改表格分页的当前页
        pagehandleCurrentChange(val) {
            this.currentPage = val;
            this.getData();
        },
        handleCurrentChange(val) {
            this.currentRow = val;
        },
    }
}
</script>
<style scoped lang="less">
.outDiv {
    height: 100vh;
    width: 100%;
    background: url("../assets/images/background/three.jpg") no-repeat;
    background-size: 100% 100%;
    background-attachment: fixed;

    ::-webkit-scrollbar {
        width: 6px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #21274D;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #888;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
}

.pagerCount {
    margin-top: 10px;
}
</style>
