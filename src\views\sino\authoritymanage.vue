<template>
  <div class="tab">
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23">
        <div  class="title_header">权限菜单管理</div>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center">
      <el-col :span="23">
        <div style="margin-bottom: 15px">
          <el-button class="btnStyle" type="primary" style=" background-color: #0E61C9;" size="medium"
                     @click="addmenu">新增
          </el-button>
          <el-button class="buttonStyle" type="primary" style=" background-color:#31C97E;" size="medium"
                     @click="edit">修改
          </el-button>
          <el-button class="buttonStyle" type="primary" style=" background-color: #FD5252;" size="medium"
                     @click="deletemenu">删除
          </el-button>
        </div>
        <el-table :data="tableData" style="width: 100%;margin-bottom: 20px;" height="calc(100vh - 190px)" class="tableT" ref="singleTable"
          row-key="menuId" highlight-current-row @expand-change="changeicon" @current-change="handleCurrentChange">
          <el-table-column label="系统名称/菜单名称">
            <template slot-scope="prop">
              <span v-show="!prop.row.ifexpand && prop.row.children">
                <img src="../../assets/images/windows.png" alt=""
                  style="width: 17px; position: relative; top: 4px; left: 2px">
              </span>
              <span v-show="prop.row.ifexpand && prop.row.children">
                <img src="../../assets/images/windows-open.png" alt=""
                  style="width: 18px; position: relative; top: 5px; left: 2px">
              </span>
              <span v-show="!prop.row.children">
                <img src="../../assets/images/liebiao.png" alt=""
                  style="width: 18px; position: relative; top: 5px; left: 2px">
              </span>
              <span style="margin-left: 10px">{{ prop.row.menuName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="menuDesc" label="菜单描述" width="200">
          </el-table-column>
          <el-table-column prop="menuUrl" label="URL">
          </el-table-column>
          <el-table-column prop="icon" label="图标">
          </el-table-column>
          <el-table-column width="100" prop="menuLevel" label="级别">
          </el-table-column>

        </el-table>
      </el-col>
    </el-row>


    <el-dialog title="新增菜单" :visible.sync="adddialogVisible" width="500px" class="addP" :show-close="false">
      <div style="text-align: left">
        <div class="login-style">
          <div class="basicinfo" @click="clickbasicinfo" :class="{ account: showbasicinfo }">基本信息</div>
          <div class="moreinfo" @click="clickmoreinfo" :class="{ account: !showbasicinfo }">更多信息</div>
        </div>
        <el-form label-position="left" label-width="100px" :rules="addrule" ref="addform" :model="addform">
          <el-col v-if="showbasicinfo">
            <el-form-item label="菜单名称" prop="menuName" style="margin-top: 20px;">
              <el-input placeholder="请输入菜单名称" style="width: 274px" v-model="addform.menuName" maxlength="20"
                show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="类型" prop="menuType">
              <el-radio v-model="addform.menuType" :label="0">模块</el-radio>
              <el-radio v-model="addform.menuType" :label="1">菜单</el-radio>
            </el-form-item>
            <el-form-item label="功能URL" prop="menuUrl">
              <el-input placeholder="请输入功能URL" style="width: 274px" v-model="addform.menuUrl" maxlength="100"
                show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="!showbasicinfo" style="margin-top:20px">
            <el-form-item label="顺序号" prop="menuOrder">
              <el-input-number placeholder="请输入顺序号(0-9999)" style="width: 274px" :min="0" :max="9999"
                v-model="addform.menuOrder"></el-input-number>
            </el-form-item>
            <el-form-item label="参数" prop="urlParam">
              <el-input placeholder="请输入参数" style="width: 274px" v-model="addform.urlParam" maxlength="20"
                show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="图标" prop="icon">
              <el-input placeholder="请输入图标" style="width: 274px" v-model="addform.icon" maxlength="20"
                show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="功能类型" prop="abilityType">
              <el-input placeholder="请输入功能类型" style="width: 274px" v-model="addform.abilityType" maxlength="20"
                show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" style="text-align:right;margin-right:8%">
        <el-button class="OKButton" :disabled="adddis" @click="resumesend('addform')">确 定</el-button>
        <el-button @click="addmenuClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="修改菜单" :visible.sync="editdialogVisible" width="500px" class="addP" :show-close="false">
      <div style="text-align: left">
        <div class="login-style">
          <div class="basicinfo" @click="clickbasicinfo" :class="{ account: showbasicinfo }">基本信息</div>
          <div class="moreinfo" @click="clickmoreinfoNext" :class="{ account: !showbasicinfo }">更多信息</div>
        </div>
        <el-form label-position="left" label-width="100px" :rules="editrule" ref="editform" :model="editform">
          <el-col v-if="showbasicinfo">
            <el-form-item label="菜单名称" prop="menuName" style="margin-top: 20px;">
              <el-input placeholder="请输入菜单名称" style="width: 274px" v-model="editform.menuName" maxlength="20"
                show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="类型" prop="menuType">
              <el-radio v-model="editform.menuType" :label="0">模块</el-radio>
              <el-radio v-model="editform.menuType" :label="1">菜单</el-radio>
            </el-form-item>
            <el-form-item label="子系统" prop="subSystem">
              <el-select v-model.trim="editform.subSystem" style="width :274px">
                <el-option v-for="(item, index) in systemlist" :key="index" :value="item.value" :label="item.label">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="功能URL" prop="menuUrl">
              <el-input placeholder="请输入功能URL" style="width: 274px" v-model="editform.menuUrl" maxlength="100"
                show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="!showbasicinfo" style="margin-top:20px">
            <el-form-item label="顺序号" prop="menuOrder">
              <el-input-number placeholder="请输入顺序号(0-9999)" style="width: 274px" :min="0" :max="9999"
                v-model="editform.menuOrder"></el-input-number>
            </el-form-item>
            <el-form-item label="参数" prop="urlParam">
              <el-input placeholder="请输入参数" style="width: 274px" v-model="editform.urlParam" maxlength="20"
                show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="图标" prop="icon">
              <el-input placeholder="请输入图标" style="width: 274px" v-model="editform.icon" maxlength="30"
                show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="功能类型" prop="abilityType">
              <el-input placeholder="请输入功能类型" style="width: 274px" v-model="editform.abilityType" maxlength="20"
                show-word-limit></el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" style="text-align:right;margin-right:8%">
        <el-button class="OKButton" :disabled="editdis" @click="editmunusend('editform')">确 定</el-button>
        <el-button @click="editmenuClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="删除" :visible.sync="deletedialogVisible" width="30%" :show-close="false" class="deletemenu">
      <div style="text-align: center">
        <i class="el-icon-warning" style="font-size: 20px;color: #fcb543;"><span
            style="font-size: 16px;color: #333;margin-left: 12px;">确定删除该菜单吗？</span></i>
      </div>
      <span slot="footer" class="dialog-footer" style="text-align:right">
        <el-button class="OKButton" :disabled="deldis" @click="deletesend">确 定</el-button>
        <el-button @click="deleteClose" class="CancButton">取 消</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { listMenus, menuAdd, menuUpdate, getDetil, menuDel } from "@/api/menu/menu";
export default {
  name: "authoritymanage",
  data() {
    return {
      showbasicinfo: true,
      currentRow: null,
      tableData: [],
      adddialogVisible: false,
      systemlist: [
        {
          value: 'MacSys',
          label: 'EOSS系统'
        }, {
          value: 'CrmSys',
          label: 'CRM系统'
        }, {
          value: 'PmsSys',
          label: 'PMS系统'
        }, {
          value: 'HRSys',
          label: 'EHR系统'
        }
      ],
      addform: {
        menuName: '',
        menuType: '',
        subSystem: '',
        menuUrl: '',
        menuOrder: '',
        urlParam: '',
        icon: '',
        abilityType: ''
      },
      addrule: {
        'menuName': [
          { required: true, message: '请输入菜单名称', trigger: 'blur' }],
        'menuType': [
          { required: true, message: '请选择类型', trigger: 'change' }],
        'subSystem': [
          { required: true, message: '请选择子系统', trigger: 'change' }],
        'menuUrl': [
          { required: true, message: '请输入功能URL', trigger: 'blur' },
          {
            pattern: /^[A-Za-z\d\/]+$/,
            message: "请输入正确的路径格式",
            trigger: "blur"
          }]
      },
      adddis: false,//新增按钮状态
      showmoreinfo: false,
      editdialogVisible: false,//编辑弹出框
      editform: {
        menuName: '',
        menuType: '',
        subSystem: '',
        menuUrl: '',
        menuOrder: '',
        urlParam: '',
        icon: '',
        abilityType: ''
      },
      editrule: {
        'menuName': [
          { required: true, message: '请输入菜单名称', trigger: 'blur' }],
        'menuType': [
          { required: true, message: '请选择类型', trigger: 'change' }],
        'subSystem': [
          { required: true, message: '请选择子系统', trigger: 'change' }],
        'menuUrl': [
          { required: true, message: '请输入功能URL', trigger: 'blur' },
          {
            pattern: /^[A-Za-z\d\/]+$/,
            message: "请输入正确的路径格式",
            trigger: "blur"
          }]
      },
      editdis: false,//编辑按钮状态
      deldis: false,//删除按钮状态
      deletedialogVisible: false,//删除弹出框
    }
  },
  created() {
    this.getTreeData()
  },
  methods: {
    //获取权限菜单管理数据
    getTreeData() {
      listMenus().then(res => {
        if (res.code == 200) {
          this.tableData = res.data;
        }
      })
    },
    changeicon(row) {
      row.ifexpand = !row.ifexpand;
    },
    handleCurrentChange(val) {
      this.currentRow = val;
    },
    //新增弹框
    addmenuClose() {
      this.adddialogVisible = false;
      this.showmoreinfo = false;
      this.adddis = false;
      this.showbasicinfo = true;
      this.$refs.addform.resetFields()
      this.addform.subSystem = "";
    },
    //新增
    resumesend(formname) {
      this.$refs[formname].validate((valid) => {
        if (valid) {
          this.adddis = true;
          menuAdd(this.addform)
            .then(res => {
              if (res.code == 200) {
                this.adddis = false;
                this.adddialogVisible = false;
                this.showbasicinfo = true;
                this.getTreeData();//获取菜单数据
                this.$refs.addform.resetFields();//清空表单数据
                this.addform.subSystem = "";
                document.documentElement.style.overflow = "auto";
                this.$message({
                  message: "新增成功",
                  type: "success",
                });
              } else {
                this.$message({
                  message: res.message || "新增失败",
                  type: "error",
                });
              }
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    addmenu() {
      if (this.currentRow) {
        // this.addform.orgId = this.currentRow.orgId;
        this.adddialogVisible = true;
        this.$nextTick(() => {
          this.$refs.addform.resetFields()
        })
        if (this.currentRow.subSystem) {
          this.addform.subSystem = this.currentRow.subSystem;
        }
        this.adddis = false;
        this.showmoreinfo = false;
      } else {
        this.$message.warning({
          message: '请选择表格中的数据'
        });
      }
    },
    clickbasicinfo() {
      this.showbasicinfo = true;
    },
    // 基本信息
    clickmoreinfo() {
      this.showbasicinfo = false;
      this.$refs['addform'].clearValidate();
    },
    // 更多信息
    clickmoreinfoNext() {
      this.showbasicinfo = false;
      this.$refs['editform'].clearValidate();
    },
    //修改菜单
    editmunusend(formname) {
      this.$refs[formname].validate((valid) => {
        if (valid) {
          this.editdis = true;
          menuUpdate(this.editform)
            .then(res => {
              if (res.code == 200) {
                this.editdis = false;
                this.editdialogVisible = false;
                this.showbasicinfo = true;
                this.getTreeData();//获取菜单数据
                this.$refs.editform.resetFields();//清空表单数据
                document.documentElement.style.overflow = "auto";
                this.$message({
                  message: "修改成功",
                  type: "success",
                });
              } else {
                this.$message({
                  message: res.message || "修改失败",
                  type: "error",
                });
              }
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    editmenuClose() {
      this.editdialogVisible = false;
      this.showmoreinfo = false;
      this.editdis = false;
      this.showbasicinfo = true;
      this.$refs.editform.resetFields()//清空表单数据
    },
    edit() {
      if (this.currentRow && this.currentRow.isMenu == "1") {
        getDetil(this.currentRow.menuId).then(res => {
          if (res.code == 200) {
            this.editform = res.data
          }
        })
        this.editdialogVisible = true;
        this.$nextTick(() => {
          this.$refs.editform.resetFields()//清空表单数据
        })
        this.editdis = false;
        this.showmoreinfo = false;
      } else {
        this.$message.warning({
          message: '请选择表格中的数据!'
        });
      }
    },
    deletemenu() {
      if (this.currentRow && this.currentRow.isMenu == "1") {
        this.deletedialogVisible = true;
      } else {
        this.$message.warning({
          message: '请选择表格中的数据!'
        });
      }
    },
    //删除
    deletesend() {
      this.deletedialogVisible = false;
      this.deldis = true;
      menuDel(this.currentRow.menuId).then(res => {
        if (res.code == 200) {
          this.deletedialogVisible = false;
          this.deldis = false;
          this.getTreeData()
          this.$message({
            message: '删除成功！',
            type: 'success'
          });
          this.currentRow = null
          this.$refs.singleTable&&this.$refs.singleTable.setCurrentRow();//取消选中
          document.documentElement.style.overflow = 'auto';
        } else if (res.code == -200) {
          this.deletedialogVisible = false;
          this.deldis = false;
          this.$message.error({
            message: res.message
          });
        }
      })
    },
    deleteClose() {
      this.deletedialogVisible = false;
      this.deldis = false;
    }
  }
}
</script>

<style scoped lang="less">
.tab {
  background-color: #21274D;
  margin: -20px 0;

  /deep/ .el-table__expand-icon {
    background: url('../../assets/images/jia1.png') 100% 100%;
    background-size: cover !important;
  }

  /deep/ .el-table__expand-icon--expanded {
    -webkit-transform: rotate(0deg) !important;
    transform: rotate(0deg) !important;
    background: url('../../assets/images/jian1.png') 100% 100% !important;
    background-size: cover !important;
  }

  /deep/ .el-table__expand-icon .el-icon-arrow-right:before {
    display: none;
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #21274D;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.title {
  height: 66px;
  line-height: 66px;
  /* border-bottom: 1px solid #eee; */
  /*margin-bottom: 10px;*/
}

.basicinfo,
.moreinfo {
  float: left;
  width: 49%;
  height: 30px;
  color: #333;
  margin-bottom: 5px;
  border-bottom: 1px solid #f4f4f4;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  font-family: "microsoft yahei";
  font-size: 16px;
}

.basicinfo {
  border-right: 1px solid #f4f4f4;
}

.account {
  color: #389AE2 !important;
  font-weight: bold;
}

</style>
