<template>
    <div class="tab" id="version">
        <el-row type="flex" justify="center" class="title">
            <el-col :span="23">
                <div class="title_header">文件传阅</div>
            </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="search">
            <el-col :span="23" class="searchBox">
                <template>
                    <el-col :span="8" style="text-align: left">
                        <el-button class="btnStyle" type="primary" style="background-color: #0E61C9; width: 80px;"
                            size="medium" @click="openAddDialog">添加
                        </el-button>
                    </el-col>
                    <el-col :span="16" style="text-align: right">
                        <el-input placeholder="请输入标题" @keyup.enter.native="search()" style="width: 30%"
                            v-model.trim="query.title" @input="search()">
                            <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer"
                                @click="search()"></i>
                        </el-input>
                    </el-col>
                </template>
            </el-col>
        </el-row>

        <el-row type="flex" justify="center">
            <el-col :span="23">            
                <el-table ref="versionTable" :data="tableData" element-loading-background="rgb(33,39,77, 0.8)"
                    height="calc(100vh - 290px)" style="width: 100%; " class="tableT" 
                    v-loading="loading"  highlight-current-row
                    
                    >
                    <el-table-column label="标题" prop="title" >
                        <template slot-scope="props">
                            {{
                                props.row.title }}
                        </template>
                    </el-table-column>
                    <el-table-column label="发布人" prop="userName">
                    </el-table-column>
                    <el-table-column label="发布时间" prop="createtime">
                        <template slot-scope="props">
                            <span>{{ formatDate(props.row.createtime) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="state">
                        <template slot-scope="scope">
                            <!-- State  2 已发布，1未发布 -->
                            <span>{{ scope.row.status == 2 ? '已发布':'未发布' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        label="操作"
                        width="180">
                        <template slot-scope="scope">
                            <el-button @click="handleDetail(scope.row)" type="text" size="small">详情</el-button>
                            <el-button v-if="scope.row.status != 2" @click="deleteNews(scope.row)" type="text" size="small">删除</el-button>
                            <el-button v-if="scope.row.status != 2" @click="handleEdit(scope.row)" type="text" size="small">编辑</el-button>
                            <el-button v-if="scope.row.status != 2" @click="pubLishNews(scope.row)" type="text" size="small">发布</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pager">
                    <el-pagination @size-change="handleSizeChange" @current-change="pagehandleCurrentChange"
                        :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pagesize"
                        layout="total, sizes, prev, pager, next, jumper" :total="totalcount"></el-pagination>
                </div>
            </el-col>
        </el-row>
     
        <!--添加/编辑-->
        <el-dialog ref="changeDialog" :title="titleDia" :visible.sync="adddialogVisible" width="900px" class="addP"
            :show-close="false" :close-on-click-modal="false" :before-close="addmenuClose">
            <div style="text-align: left">
                <el-form label-position="left" label-width="120px" :rules="addrule" ref="addform" :model="addform">
                    <el-col :span="24">
                        <el-form-item label="标题" prop="title">
                            <el-input v-model.trim="addform.title" placeholder="请输入标题" maxlength="36" show-word-limit>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :offset="1" :span="3">
                        <!-- <el-button class="OKButton" @click="preview()">预览</el-button> -->
                    </el-col>
             
                    <el-col :span="24">
                        <el-form-item label="传阅内容简要说明" prop="contentText">
                            <editor v-model="addform.contentText" :min-height="120" />
                        </el-form-item>
                    </el-col>
                
                    <el-col :span="24" >
                        <el-form-item label="传阅内容附件" prop="fileUrl">
                            <el-upload class="upload-demo" ref="upload" :action="actionurl"
                                       style="width:50%"
                                       :show-file-list="true"
                                       v-model="addform.fileUrl"
                                       :on-preview="handlePreview" :on-remove="handleRemove" :on-success="handleASuccess"
                                       :before-upload="beforeUpload1" :file-list="fileList" multiple accept=".pdf"
                                       :limit="5">
                                <el-button size="small" type="primary">上传</el-button>
                               <div slot="tip" class="el-upload__tip"  style="color:#b8b6c0;margin-top:-10px">仅支持pdf格式的文件，最多上传5个文件</div>
                            </el-upload>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="传阅对象" prop="receiver">
                            <el-tree
                                :data="treeData"
                                show-checkbox
                                ref="tree"
                                node-key="id"
                                highlight-current
                                :default-expanded-keys="[-1,-2]"
                                :default-checked-keys="[]"
                                :props="defaultProps">
                                </el-tree>
                        </el-form-item>
                        </el-col>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
             <div style="width: 100%;text-align: right;">
                
                <el-button class="OKButton" :disabled="adddis" @click="handleVersionAdd(2)">发布</el-button>
                <el-button class="CancButton" :disabled="adddis" @click="handleVersionAdd(1)">保存草稿</el-button>
                <el-button @click="addClose" class="CancButton">取 消</el-button>
             </div>
            </span>
        </el-dialog>

        <!--详情弹框-->
        <el-dialog ref="detailDialog" title="传阅文件详情" :visible.sync="detailDialogVisible" width="900px" class="addP"
            :close-on-click-modal="false" :before-close="detailmenuClose" :show-close="false">
            <div style="text-align: left">
                <el-form label-position="left" label-width="120px">
                    <el-col :span="24">
                        <el-form-item label="标题">
                            <span>{{ detailData.title }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="简要说明">
                            <div v-html="detailData.content"></div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="附件">
                            <div v-for="(item, index) in detailData.attach" :key="index" class="attachment-item">
                              <div>{{ item.attachName }}</div> 
                                <el-button type="text" size="small" @click="handleDownload(item)">浏览</el-button>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="传阅对象">
                            <span>{{ detailData.userNameStr }}</span>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <div style="width: 100%;text-align: right;">
                <el-button @click="detailmenuClose" class="CancButton">关 闭</el-button>
            </div>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {
    downLoadFile,publishAttachPass,updateAttachPassRead,articleTopicGetAttachPassPage,insertAttachPass, updateAttachPass ,userListRoleUser,deleteAttachPass,getAttachPassById
} from "@/api/sino/newRelease";
import Editor from '@/components/Editor';
import EditorDteail from '@/components/Editor/detail.vue';

import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
import moment from 'moment';
export default {
    name: "newRelease",
    components: {
        Editor,
        EditorDteail
    },
    data() {
        return {
            defaultProps: {
          children: 'children',
          label: 'name'
        },
            currentRow: null,
            //版本更新列表
            tableData: [],
            //版本更新列表查询参数
            query: {
                title: '',
                catalogid: '',

            },
            //版本更新详情页数据
            detailform: {
                title: '',
                createtime: '',
                clicktimes: '',
                catalogid: '',
                contentText: '',
            },
            //版本更新loading
            loading: false,
            //用户手册loading
            currentPage: 1,
            pagesize: 10,
            searchword: "",
            totalcount: null,
            adddialogVisible: false,
            detailDialogVisible: false,

            //系统列表
            systemList: [],
            //添加 or  修改表单数据
            addform: {
                catalogid: "",
                title: '',
                contentText: '',
                popUp: 0,
                deadlineTime: '',
                fileUrl: '',
            },
            fileList: [],
            treeData: [],
            actionurl: process.env.VUE_APP_BASE_UPLOAD +'appPerm/upAppLog',
            paramsData: {},
            //弹框标题
            titleDia: '新增传阅文件',
            //添加
            addrule: {
                title: [
                    { required: true, message: "请输入名称", trigger: "blur" },
                ],
                contentText: [
                    { required: true, message: "请输入传阅内容简要说明", trigger: ['change', 'blur'] },
                ],
                fileUrl: [
                    { 
                        required: true, 
                        message: "请上传传阅内容附件", 
                        trigger: "change",
                        validator: (rule, value, callback) => {
                            if (this.fileList.length === 0) {
                                callback(new Error('请上传传阅内容附件'));
                            } else {
                                callback();
                            }
                        }
                    },
                ],
                receiver: [
                    { required: false, message: "请选择传阅对象", trigger: "change" },
                ],
            },
            adddis: false,//新增按钮状态
          pickerOptions: {
            disabledDate(time) {
              return time.getTime() < Date.now() - 8.64e7; // 如果现在时间之前，则不可选
            },
          },
          isEdit: false,  // 是否是编辑模式
          editId: '',     // 编辑时的ID
          detailData: {
                title: '',
                content: '',
                attach: [],
                userNames: ''
            },
            isUploading: false, // 添加上传状态标记
        }
    },
    computed: {

    },

    created() {
        //列表页获取
        this.search();


    },
    mounted() {
        this.onLoad()
    },
    watch:{
      'addform.fileUrl':function(newVal){
         if(newVal){
           if (this.$refs.addform) {
             this.$refs.addform.validateField('fileUrl', () => {
               return true
             })
           }
         }
      },
      'addform.contentText':function(newVal){
         if(newVal){
           if (this.$refs.addform) {
             this.$refs.addform.validateField('contentText', () => {
               return true
             })
           }
         }
      },
    },
    methods: {
        onLoad() {
            userListRoleUser(window.localStorage.getItem('Susername')).then(res => {
                let data = res.data;
                data.forEach((item,index)=>{
                data[index].id = index - 1;
                data[index].name = item.label
                })

                this.treeData = data;

                // this.changeDefaultChecked();
            }).catch((e)=>{
                console.log(e)
            });
        },
        deleteNews(item){
            this.$confirm('确定删除该数据?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
                }).then(() => {
                    deleteAttachPass(item.id).then(res=>{
                        if(res.code ==200){
                                this.search();
                            }
                            this.$message({
                                type: res.code == 200 ? 'success' : 'warning',
                                message: res.data
                            });
                    }).catch(e=>{
                        this.$message({
                                type:  'error',
                                message: '删除失败'
                            });
                    })
                }).catch(() => {        
                });
        },
        pubLishNews(item) {
            this.$confirm('确定要发布该传阅文件吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                publishAttachPass(item.id).then(res => {
                    if (res.code === 200) {
                        this.$message({
                            type: 'success',
                            message: '发布成功'
                        });
                        this.search();
                    } else {
                        this.$message.error(res.message || '发布失败');
                    }
                }).catch(() => {
                    this.$message.error('发布失败');
                });
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消发布'
                });
            });
        },
        //查询置空
        handResertSearch() {
            this.query = {
                title: '',
                catalogid: '',
            }
        },
        //版本列表查询
        search() {
            this.currentPage = 1;
            this.getListDate()
        },

        //修改表格分页的展示条数
        handleSizeChange(val) {
            this.pagesize = val;
            this.getListDate();
        },
        //修改表格分页的当前页
        pagehandleCurrentChange(val) {
            this.currentPage = val;
            this.getListDate();
        },
        // 修改表格分页的展示条数
        handleCurrentChange(val) {
            this.currentRow = val;
        },
        //获取列表数据
        getListDate() {
            this.loading = true;
            let param = {
                ...this.query,
                readFlag:'0',
                pageNum: this.currentPage,
                pageSize: this.pagesize,
                type:'type_1'
            }
            articleTopicGetAttachPassPage(param).then(res => {
                this.tableData = res.data.subject;
                this.totalcount = res.data.totalCount;
                this.loading = false;
            })
        },
    
        // 添加弹框取消
        addmenuClose() {
            this.adddialogVisible = false;
            this.adddis = false;
            this.$refs.addform.resetFields();
            this.handleReset()
        },

        showDialogVisible(row) {
            this.detailDialogVisible = true
            this.detailform = { ...row, contentText: row.contentText ? row.contentText : '' }
        },
        preview() {
            let that = this
            this.$refs.addform.validate((valid) => {
                if (valid) {
                    that.detailDialogVisible = true
                    that.detailform = {
                        title: that.addform.title,
                        createtime: new Date(),
                        clicktimes: '0',
                        catalogid: that.addform.catalogid,
                        contentText: that.addform.contentText ? that.addform.contentText.toString() : ''
                    }
                }
            })
        },
        //时间格式化
        formatDate(dateStr) {
            return moment(dateStr).format('YYYY-MM-DD');
        },

        //新增证书
        handleRemove(file) {
            this.addform.fileUrl = '';
            this.fileList = this.fileList.filter(item => item.url !== file.url);
        },
        // -上传服务器之前逻辑处理
        beforeUpload1(file) {
            this.isUploading = true;
            //上传的文件只能是word或pdf文件，且大小不能超过10MB
            const isWordOrPdf = (file.type === 'application/pdf') || 
                              (file.type === 'application/msword') || 
                              (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isWordOrPdf) {
                this.$message.error('只能上传word或pdf文件!');
                this.isUploading = false;
                return false;
            }
            if (!isLt10M) {
                this.$message.error('上传文件大小不能超过 10MB!');
                this.isUploading = false;
                return false;
            }
            if (this.fileList.length >= 5) {
                this.$message.error('最多只能上传5个文件!');
                this.isUploading = false;
                return false;
            }
            return true;
        },
        //成功上传
        handleASuccess(res,file) {
            this.addform.fileUrl =  res.data
            this.fileList.push({
                name: file.name,
                url: res.data,
            });
            this.isUploading = false;
        },
        //上传
        handlePreview(file) {
            let URL = window.URL || window.webkitURL;
            if (!!window.ActiveXObject || "ActiveXObject" in window) {
                this.$message.warning("暂不支持预览");
            } else {
                if (file.raw) {
                    window.open(URL.createObjectURL(file.raw)) //blob格式地址
                } else {
                    window.open(file.url) //blob格式地址
                }
            }
        },
        //关闭更新详情弹框
        detailmenuClose() {
            this.detailDialogVisible = false
            this.detailData = {
                title: '',
                content: '',
                attach: [],
                userNames: ''
            }
        },

        //打开添加弹框
        openAddDialog() {
            this.isEdit = false;
            this.titleDia = '新增传阅文件';
            this.handleReset();
            this.adddialogVisible = true;
            // 重置树形选择器的选中状态并收起
            this.$nextTick(() => {
                if (this.$refs.tree) {
                    this.$refs.tree.setCheckedKeys([]);
                    // 收起所有展开的节点
                    const expandedKeys = this.$refs.tree.store.expandedKeys;
                    expandedKeys.forEach(key => {
                        this.$refs.tree.store.expandNodes(key, false);
                    });
                }
            });
        },

        // 打开编辑弹框
        handleEdit(row) {
            this.isEdit = true;
            this.titleDia = '编辑传阅文件';
            this.editId = row.id;
            
            // 调用接口获取详情
            getAttachPassById(row.id).then(res => {
                if (res.code === 200) {
                    const detail = res.data;
                    // 设置表单数据
                    this.addform = {
                        title: detail.title,
                        contentText: detail.content || '',
                        fileUrl: detail.attach && detail.attach.length > 0 ? detail.attach[0].filePath : '',
                    };

                    // 设置附件列表
                    if (detail.attach && detail.attach.length > 0) {
                        this.fileList = detail.attach.map(item => ({
                            name: item.attachName,
                            url: item.filePath
                        }));
                    } else {
                        this.fileList = [];
                    }

                    // 设置树形选择器的选中状态
                    this.$nextTick(() => {
                        if (detail.userIds) {
                            // 将userIds转换为account数组
                            const accounts = detail.userIds.split(',');
                            // 根据account找到对应的节点并设置选中状态
                            const nodes = this.$refs.tree.store.nodesMap;
                            const keys = [];
                            Object.keys(nodes).forEach(key => {
                                const node = nodes[key];
                                if (accounts.includes(node.data.account)) {
                                    keys.push(node.key);
                                }
                            });
                            this.$refs.tree.setCheckedKeys(keys);
                        }
                    });

                    this.adddialogVisible = true;
                } else {
                    this.$message.error(res.message || '获取详情失败');
                }
            }).catch(() => {
                this.$message.error('获取详情失败');
            });
        },

        //添加记录
        handleVersionAdd(status) {
            if (this.isUploading) {
                this.$message.warning('文件正在上传中，请等待上传完成后再保存');
                return;
            }

            this.$refs.addform.validate((valid) => {
                if (valid) {
                    this.adddis = true;
                    const attachData = this.fileList.map(item => {
                        return {
                            attachName: item.name || '',
                            filePath: item.url || ''
                        };
                    });
                    
                    let selectId = this.$refs.tree.getCheckedNodes();
                    if (selectId.length === 0) {
                        this.$message.warning('请选择传阅对象');
                        this.adddis = false;
                        return;
                    }
                    
                    // 使用account字段
                    const accounts = selectId.map(item => item.account).join(',');
                    
                    const requestData = {
                        userName: window.localStorage.getItem("Susername"),
                        title: this.addform.title,
                        content: this.addform.contentText,
                        userIds: accounts.replace(/^,|(,){2,}/g, '$1'),  // 去掉开头的逗号，连续的逗号保留一个
                        attach: attachData,
                        status: status,  // 1:草稿 2:发布
                        type:"type_1"
                    };

                    if (this.isEdit) {
                        requestData.id = this.editId;
                        // 编辑时使用updateAttachPass接口
                        updateAttachPass(requestData).then(res => {
                            if (res.code == 200) {
                                this.currentPage = 1;
                                this.getListDate();
                                this.$message({
                                    message: status === 1 ? "保存草稿成功！" : "编辑成功！",
                                    type: "success",
                                });
                                this.adddis = false;
                                this.addClose();
                                this.adddialogVisible = false;
                            } else {
                                this.adddis = false;
                                this.$message.error({
                                    message: res.message || (status === 1 ? "保存草稿失败！" : "编辑失败！"),
                                });
                            }
                        });
                    } else {
                        // 新增时使用insertAttachPass接口
                       
                        insertAttachPass(requestData).then(res => {
                            if (res.code == 200) {
                                this.currentPage = 1;
                                this.getListDate();
                                this.$message({
                                    message: status === 1 ? "保存草稿成功！" : "新增成功！",
                                    type: "success",
                                });
                                this.adddis = false;
                                this.addClose();
                                this.adddialogVisible = false;
                            } else {
                                this.adddis = false;
                                this.$message.error({
                                    message: res.message || (status === 1 ? "保存草稿失败！" : "新增失败！"),
                                });
                            }
                        });
                    }
                }
            })
        },
        //关闭 添加弹框
        addClose() {
            this.adddialogVisible = false;
            this.adddis = false;
            this.$refs.addform.resetFields();
            this.addform.fileUrl = '';
            this.fileList = [];
        },

        //置空操作
        handleReset() {
            this.addform = {
                catalogid: "",
                title: '',
                contentText: '',
                popUp: 0,
                deadlineTime: '',
                fileUrl: '',

            };
            this.detailform = {
                title: '',
                createtime: '',
                clicktimes: '',
                catalogid: '',
                contentText: ''
            }

            this.$nextTick(() => {
                if (this.$refs.addform) {
                    this.$refs.addform.resetFields();
                }
            })
        },
        // 查看详情
        handleDetail(row) {
            getAttachPassById(row.id).then(res => {
                if (res.code === 200) {
                    res.data.userNameStr = res.data.userNameList.length > 0 ? res.data.userNameList.join(',') : '';
                    this.detailData = res.data;
                    this.detailDialogVisible = true;
                } else {
                    this.$message.error(res.message || '获取详情失败');
                }
            }).catch(() => {
                this.$message.error('获取详情失败');
            });
        },

        // 更新已读状态
        handleUpdateRead() {
            updateAttachPassRead(this.detailData.id,window.localStorage.getItem("Susername")).then(res => {
                if (res.code === 200) {
                    this.$message.success('已更新为已读状态');
                    this.detailData.readFlag = '1';
                    // 刷新列表
                    this.getListDate();
                } else {
                    this.$message.error(res.message || '更新已读状态失败');
                }
            }).catch(() => {
                this.$message.error('更新已读状态失败');
            });
        },
        // 下载附件
        handleDownload(item) {
            const isPdf = item.attachName.toLowerCase().endsWith('.pdf');
            downLoadFile(item.filePath).then(res => {
                if (isPdf) {
                    const blob = new Blob([res], { type: 'application/pdf' });
                    const pdfUrl = window.URL.createObjectURL(blob);
                    window.open(pdfUrl, '_blank'); // 新标签页打开
                    // 可选：延迟释放 URL，避免 PDF 未加载完就被释放
                    setTimeout(() => {
                        window.URL.revokeObjectURL(pdfUrl);
                    }, 10000); // 10秒后释放
                } else {
                    const blob = new Blob([res], {
                        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    });
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement("a");
                    link.style.display = "none";
                    link.href = url;
                    link.setAttribute("download", item.attachName);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                }
            }).catch(() => {
                this.$message.error('下载失败');
            });
        },
    }
};
</script>

<style scoped lang="less">
.tab {
    background-color: #21274D;
    margin: -20px 0;
    height: calc(100vh - 90px);

    ::-webkit-scrollbar {
        width: 6px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #21274D;
    }

    /* Handle */

    ::-webkit-scrollbar-thumb {
        background: #888;
    }

    /* Handle on hover */

    ::-webkit-scrollbar-thumb:hover {
        //background: #555;
    }
}

.pager {
    width: 100%;
    overflow: hidden;
    position: relative;
    margin-top: 10px;
    margin-bottom: 10px;
}

.el-pagination {
    float: right;
}

.title {
    height: 66px;
    line-height: 66px;

    /deep/ .el-input .el-input__inner {
        //border-radius: 34px !important;
        background-color: rgba(255, 255, 255, 0.25) !important;
        color: rgba(199, 199, 199, 1) !important;
        border: 0px;

    }
}


.search {
    height: 70px;
    line-height: 40px;

    .searchBox {
        padding: 10px 0;
        position: relative;
    }

    /deep/ .el-input .el-input__inner,
    .el-range-editor.el-input__inner,
    .el-range-editor.el-range-input,
    .el-range-editor.el-range-separator {
        //border-radius: 34px !important;
        background-color: rgba(255, 255, 255, 0.25) !important;
        color: rgba(199, 199, 199, 1) !important;
        border: 0px;
    }
}

.add,
.edit {
    color: #fff;
    margin-left: 15px;
    border: none;
}

.add {
    background-color: #037cb3;
}

.edit {
    background-color: #17b3d9;
}

.sure {
    background: none repeat scroll 0 0 #61b0e9;
    border-color: #389ae2;
    color: #fff;
    text-shadow: none;
    text-transform: uppercase;
}

.text01 {
    font-size: 30px;
    font-family: "微软雅黑";
    color: #08657E;
    margin-left: 15px;
}

.text02 {
    font-size: 18px;
    font-family: "微软雅黑";
    color: #767676;
    margin-left: 15px;
}

.clickTimes {
    font-size: 12px;
    margin-left: 15px;
}

/* 13寸 */
@media screen and (min-width: 600px) and (max-width: 1300px) {
    .tableT {
        margin-bottom: -5px
    }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
    .tableT {
        margin-bottom: 30px
    }
}

/deep/ .addP .el-dialog__footer {
    text-align: center !important;
}

ul {
    list-style-type: disc !important;
}

/deep/ .el-loading-mask {
    background-color: #21274d !important;
}

/deep/ #haneleDetailId .el-loading-mask {
    background-color: rgba(255, 255, 255, 1) !important;
}

.myIcon {
    color: #409eff;
    padding: 0 5px;
    cursor: pointer;
}

/deep/ .el-icon-d-arrow-right {
    font-size: 15px !important;
    color: white !important;
}

/deep/ #recordContent .ql-editor {}

/deep/ #recordContent li {
    padding: 0px 0px !important;
    line-height: 25px;
}

/deep/ #recordContent .ql-editor ol {
    padding-left: 30px !important;
}

/deep/ #recordContent .ql-editor ul {
    margin-left: -42px !important;
    margin-top: -5px;
}

/deep/ #recordContent .ql-editor p {
    padding-left: 0px !important;
}

/deep/ #recordContent .ql-editor li:not(.ql-direction-rtl)::before {
    color: #d8d8d8;
    font-size: 38px;
    vertical-align: middle;
    /* 调整垂直对齐方式 */
}

/deep/ strong {
    font-weight: bold !important;
}



/deep/ .addP .el-form-item__label {
    color: #939393;
}

/deep/ .search .el-range-editor.el-input__inner {
    width: 100%;
}

/deep/ #updateDetail {
    .el-form-item {
        margin: 0;
    }
}
::v-deep  .el-upload-list__item{
  transition:none !important;
  -webkit-transition:nonne !important;
}
::v-deep   .el-upload-list__item-name{
  transition:none !important;
  -webkit-transition:nonne !important;
}
.attachment-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-bottom: 10px;
}
</style>
