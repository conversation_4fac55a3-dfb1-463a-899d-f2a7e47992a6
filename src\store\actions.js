import {GET_COMPANY_LIST, CREATE_ORG, UPDATE_STAFF, GET_ORG_LIST, DELETE_STAFF, UPDATE_ORG,
  GET_DEPT_LIST, GET_STAFF_LIST, DELETE_ORG, GET_COMPANY_STAFF_LIST, CREATE_STAFF} from "../api";
import request from '../utils/request';
import queryString from 'query-string';

// 获取所属组织下拉列表
export const getOrgList = ({commit}) => {
  const reqUrl = GET_ORG_LIST;
  return request.get(reqUrl).then(res => {
    const orgTreeList = res && res.data || [];
    commit('storeOrgList', orgTreeList);
  });
};

// 获取所有公司列表
export const getCompanyList = ({commit}) => {
  const reqUrl = GET_COMPANY_LIST;
  request.get(reqUrl).then(res => {
    commit('storeCompanyList', [res.data]);
  });
};
// 获取员工列表
export const getStaffList = ({commit}, params) => {
  const reqUrl = GET_STAFF_LIST;
  const qs = params || {
    pageSize: 10000,
    pageNum: 1,
    orgId: '',
    search: '',
    state: 1
  };

  const stringified = queryString.stringify(qs);
  const url = reqUrl + '?' + stringified;
  return request.get(url).then(res => {
    const data = res.data;
    commit('storeStaffList', {
      totalCount: data.totalCount,
      staffList: data.staffSubject
    });
    return data.staffSubject;
  });
};
// 获取部门下拉列表
export const getDeptList = ({commit}) => {
  const reqUrl = GET_DEPT_LIST;
  request.get(reqUrl).then(res => {
    commit('storeDeptList', res.data);
  });
};
// 获取分公司员工列表
export const getCompanyStaffList = ({commit}, params) => {
  const reqUrl = GET_COMPANY_STAFF_LIST;
  const isExport = +params.export === 1;
  const url = reqUrl + '?' + queryString.stringify(params);
  const suffUrl =window.location.href.indexOf('/sso')>-1?'/ssoapi':'/api';
  if (isExport) {
    const win = window.open(suffUrl + url, '_blank');
    win.focus();
    return;
  } else {
    request.get(url).then(res => {
      const data = res.data;
      commit('storeCompanyStaffList', data.userList);
      commit('storeCompanyStaffCount', data.totalCount);
    });
  }
};
// 新增员工
export const createStaff = ({commit}, params) => {
  const reqUrl = CREATE_STAFF;
  return request.post(reqUrl, params).then(() => { commit
  });
};
// 修改员工
export const updateStaff = ({commit}, params) => {
  const reqUrl = UPDATE_STAFF;
  return request.post(reqUrl, params).then(() => { commit });
};
// 删除员工
export const deleteStaff = ({commit}, userName) => {
  const reqUrl = DELETE_STAFF;
  request.post(`${reqUrl}?userName=${userName}`).then(() => { commit
  });
};
// 新增组织
export const createOrg = ({commit}, params) => {
  const reqUrl = CREATE_ORG;
  return request.post(reqUrl, params).then(() => { commit
  });
};
// 修改组织
export const updateOrg = ({commit}, params) => {
  const reqUrl = UPDATE_ORG;
  return request.post(reqUrl, params).then(() => { commit
  })
};
// 删除组织
export const deleteOrg = ({commit}, params) => {
  const reqUrl = DELETE_ORG;
  return request.post(`${reqUrl}?orgId=${params.orgId}`).then(() => { commit });
};
