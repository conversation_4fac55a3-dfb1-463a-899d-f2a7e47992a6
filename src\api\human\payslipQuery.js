import request from "@/utils/request";
export const determinePwd = (param) => {
    return new Promise((resolve) => {
        request.get("/salary/determinePwd?userName=" + param.userName + "&&passWord=" +
            param.passWord
        ).then((res) => {
            resolve(res);
        });
    });
};


export const getAllInfo = (param) => {
      return new Promise((resolve) => {
      request.get("/salary/getSraffTableGrid?pageSize=" + param.pageSize + "&&pageNum=" +
          param.pageNum + "&&userName=" + param.userName
      ).then((res) => {
          resolve(res);
      });
  });
};
//修改
export const modiSalaryPas = (param) => {
    return new Promise((resolve) => {
        request.get("/salary/modiSalaryPas?newPassword1=" + param.newPassword1 + "&&newPassword2=" +
            param.newPassword2 + "&&userName=" + param.userName
            ).then((res) => {
            resolve(res);
        });
    });
};
