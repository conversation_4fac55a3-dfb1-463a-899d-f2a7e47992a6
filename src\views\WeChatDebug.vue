<template>
  <div class="wechat-debug">
    <h2>企业微信登录调试页面</h2>
    
    <div class="debug-section">
      <h3>当前URL信息</h3>
      <div class="info-item">
        <label>完整URL:</label>
        <span>{{ currentUrl }}</span>
      </div>
      <div class="info-item">
        <label>Search参数:</label>
        <span>{{ searchParams }}</span>
      </div>
      <div class="info-item">
        <label>Hash参数:</label>
        <span>{{ hashParams }}</span>
      </div>
    </div>

    <div class="debug-section">
      <h3>企业微信Code信息</h3>
      <div class="info-item">
        <label>从URL提取的Code:</label>
        <span>{{ extractedCode || '未检测到' }}</span>
      </div>
      <div class="info-item">
        <label>SessionStorage中的Code:</label>
        <span>{{ sessionCode || '无' }}</span>
      </div>
    </div>

    <div class="debug-section">
      <h3>登录状态</h3>
      <div class="info-item">
        <label>isLogin:</label>
        <span>{{ isLogin }}</span>
      </div>
      <div class="info-item">
        <label>scanLogin:</label>
        <span>{{ scanLogin }}</span>
      </div>
      <div class="info-item">
        <label>用户名:</label>
        <span>{{ username || '无' }}</span>
      </div>
    </div>

    <div class="debug-section">
      <h3>操作</h3>
      <button @click="refreshInfo" class="btn">刷新信息</button>
      <button @click="clearStorage" class="btn">清除存储</button>
      <button @click="testLogin" class="btn" :disabled="!extractedCode">测试登录</button>
      <button @click="goToScanSign" class="btn">返回扫码页面</button>
    </div>

    <div class="debug-section" v-if="loginResult">
      <h3>登录结果</h3>
      <pre>{{ JSON.stringify(loginResult, null, 2) }}</pre>
    </div>
  </div>
</template>

<script>
import { thirdLogin } from "@/api/sino/sino";

export default {
  name: "WeChatDebug",
  data() {
    return {
      currentUrl: '',
      searchParams: '',
      hashParams: '',
      extractedCode: '',
      sessionCode: '',
      isLogin: '',
      scanLogin: '',
      username: '',
      loginResult: null
    };
  },
  mounted() {
    this.refreshInfo();
  },
  methods: {
    refreshInfo() {
      this.currentUrl = window.location.href;
      this.searchParams = window.location.search;
      this.hashParams = window.location.hash;
      
      // 提取code
      const urlParams = new URLSearchParams(window.location.search);
      const hashParams = new URLSearchParams(window.location.hash.split('?')[1]);
      this.extractedCode = urlParams.get('code') || hashParams.get('code') || '';
      
      // 获取存储信息
      this.sessionCode = sessionStorage.getItem('weChatcode') || '';
      this.isLogin = window.localStorage.getItem("isLogin") || '';
      this.scanLogin = window.localStorage.getItem("scanLogin") || '';
      this.username = window.localStorage.getItem("Susername") || '';
    },
    
    clearStorage() {
      sessionStorage.removeItem('weChatcode');
      window.localStorage.removeItem("isLogin");
      window.localStorage.removeItem("scanLogin");
      window.localStorage.removeItem("Susername");
      window.localStorage.removeItem("LoginName");
      this.refreshInfo();
      this.$message.success('存储已清除');
    },
    
    async testLogin() {
      if (!this.extractedCode) {
        this.$message.error('没有检测到code');
        return;
      }

      try {
        this.loginResult = null;
        this.$message.info('正在测试登录...');

        // 添加请求开始时间
        const startTime = Date.now();
        console.log('开始测试登录，时间:', new Date().toISOString());

        const result = await thirdLogin({ code: this.extractedCode });

        const endTime = Date.now();
        console.log('登录请求完成，耗时:', endTime - startTime, 'ms');

        this.loginResult = {
          ...result,
          requestTime: endTime - startTime + 'ms',
          timestamp: new Date().toISOString()
        };

        if (result.code === 200) {
          this.$message.success('登录测试成功');
        } else {
          this.$message.error('登录测试失败: ' + result.message);
        }
      } catch (error) {
        const endTime = Date.now();
        console.error('登录请求失败:', error);
        this.loginResult = {
          error: error.message || '网络错误',
          errorDetails: error,
          timestamp: new Date().toISOString()
        };
        this.$message.error('登录测试出错: ' + error.message);
      }
    },
    
    goToScanSign() {
      this.$router.push('/scanSign');
    }
  }
};
</script>

<style scoped>
.wechat-debug {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.debug-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.info-item {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: bold;
  width: 200px;
  flex-shrink: 0;
}

.info-item span {
  word-break: break-all;
  color: #666;
}

.btn {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn:hover:not(:disabled) {
  background: #66b1ff;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
