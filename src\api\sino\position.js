import request from "@/utils/request";
//获取岗位类型
export const getPositionType = () => {
  return new Promise((resolve) => {
    request.get("/position/getPositionType").then((res) => {
      resolve(res);
    });
  });
};

export const getListTwo = () => {
  return new Promise((resolve) => {
    request.get("/position/getListTwo").then((res) => {
      resolve(res);
    });
  });
};
//新增岗位
export const positionAdd = (param) => {
  return new Promise((resolve) => {
    request.post("/position/add", param).then((res) => {
      resolve(res);
    });
  });
};
//修改岗位
export const positionUpdate = (param) => {
  return new Promise((resolve) => {
    request.post("/position/update", param).then((res) => {
      resolve(res);
    });
  });
};
//获取岗位详情
export const positionDetail = (positionId) => {
  return new Promise((resolve) => {
    request.get("/position/getDetail?positionId=" + positionId).then((res) => {
      resolve(res);
    });
  });
};

export const positionDel = (positionId) => {
  return new Promise((resolve) => {
    request.get("/position/del?positionId=" + positionId).then((res) => {
      resolve(res);
    });
  });
};
