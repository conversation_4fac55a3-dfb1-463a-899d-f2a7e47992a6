import Vue from 'vue'
import Vuex from 'vuex'
import {getNewToken} from '../api'
import createPersistedState from 'vuex-persistedstate'
import * as mutations from './mutations';
import * as actions from './actions';
import * as getters from './actions';
import { set } from 'lodash';

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    access_token: "",
    refresh_token: "",
    userName: "",
    dialogs: 0,
    noticeNum: 0,
  },
  mutations: {
    edit_access_token(state, value) {
      state.access_token = value;
      document.cookie = "access_token =" + res.data.access_token
    },
    edit_refresh_token(state, value) {
      state.refresh_token = value;
      window.localStorage.setItem("refresh_token", value)
    },
    edit_username(state, value) {
      state.userName = value;
    },

    setDialogs(state, value) {
      state.dialogs = value;
    },
    setNoticeNum(state, value) {
      state.noticeNum = value;
    },
    ...mutations
  },
  actions: {
    REFRESH_TOKEN({commit, state}) {
      getNewToken().then(res => {
        commit(edit_access_token, res.access_token)
        commit(edit_refresh_token, res.refresh_token)
      })
    },
    EDIT_USER({commit}, value) {
      commit('edit_username', value)
    },
    SET_DIALOGS({commit}, value) {
      commit('setDialogs', value)
      
    },
    SET_NOTICE_NUM({commit}, value) {
      commit('setNoticeNum', value)
      
    },
    ...actions
  },
  getters: {
    ...getters,
    accessToken(state) {
      return state.access_token
    },
    refreshToken(state) {
      return state.refresh_token
    }
  },
  plugins: [createPersistedState({
    storage: {
      getItem: (key) => localStorage.getItem(key),
      setItem: (key, value) => {
        let newValue = '';
        if (key === 'vuex') {
          const parsed = JSON.parse(value);
          parsed.orgList = [];
          newValue = JSON.stringify(parsed);
        }
        localStorage.setItem(key, newValue);
      },
      removeItem: (key) => localStorage.removeItem(key)
    }
  })],
})
