<template>
  <div class="container">
    <div class="system-box">
      <div class="sys-container" v-for="(item, index) in systems" :key="item.id"
        :class="{ containerActive: index == hoverIndex }" @mouseenter="hoverIndex = index"
        @mouseleave="hoverIndex = null" @click="sysClick(index, item.url)">
        <div class="icon-box" :class="index == activeIndex ? 'active' : ''">
          <img :src="item.appLogo" alt="">
        </div>
        <i class="el-icon-delete" id="clicked" v-if="index == activeIndex" @click="deleClick(item.id)"></i>
        <i class="el-icon-edit-outline" id="clicked1" v-if="index == activeIndex" @click="editClick(item.id)"></i>
        <p class="refer">{{ item.appName }}</p>
        <p class="name">{{ item.appShortName }}</p>
      </div>
      <el-dialog title="应用修改" :visible.sync="dialogVisible" width="40%">
        <div style="max-height:400px;overflow: auto;">
          <el-form ref="form" :model="form" label-width="100px" :rules="rules">
            <el-form-item label="应用名称" prop="name">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
            <el-form-item label="应用logo" :required="true">
              <el-upload class="avatar-uploader" action="/api/appPerm/upAppLog" :show-file-list="false"
                :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
                <img v-if="imageUrl" :src="imageUrl" class="avatar" alt="imageUrl"/>
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
            </el-form-item>
            <el-form-item label="应用简称" prop="simpleName">
              <el-input v-model="form.simpleName"></el-input>
            </el-form-item>
            <el-form-item label="应用地址" prop="appUrl">
              <el-input v-model="form.appUrl"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="warning" @click="power">配置应用权限</el-button>
            </el-form-item>
            <el-form-item v-if="activeTags.length > 0">
              <el-button round type="success" size="mini" v-for="(item, index) in activeTags" :key="index">
                {{ item.name }}
                <i class="el-icon-close el-icon--right" @click="deleteActiveTag(item.id)"></i>
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="addApplicationBtn('form')">添加应用</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-dialog title="配置权限" append-to-body :visible.sync="innerDialogVisible">
          <div class="left-box">
            <el-menu :default-active="innerActiveIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
              <el-menu-item index="1">组织架构</el-menu-item>
              <el-menu-item index="2">标签</el-menu-item>
            </el-menu>
            <el-tree v-if="index == 1" :data="treeData" :props="defaultProps" @node-click="handleNodeClick"
              @check="handleNodeCheck" show-checkbox :default-expand-all="true" :accordion="true"
              :expand-on-click-node="false"></el-tree>
            <div v-if="index == 2">
              <p class="tag" v-for="item in tags" :key="item.roleId" @click="tagClick(item.roleName, item.isClick)">
                {{ item.roleName }}</p>
            </div>
          </div>
          <div class="right-box">
            <p class="title">选择项</p>
            <p v-for="(item, index) in activeTags" class="tag" :key="index" @click="clickedItem(item.name)">{{ item.name
}}
            </p>
          </div>
          <div style="clear:both"></div>
          <div class="button-box">
            <el-button type="primary" @click="confirm">确定</el-button>
            <el-button type="danger" @click="cancel">取消</el-button>
          </div>
        </el-dialog>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="addApplicationBtn('form')">修改应用</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { deleteAll, searchApp, listAppAll, getAllStaffByOrgs, updateAppAndPower } from "@/api/sino/application";
import { listRoles } from "@/api/sino/roleUrl";
export default {
  name: "application",
  data() {
    var checkHttp = (rule, value, callback) => {
      const httpReg = /(http|https):\/\/([\w.]+\/?)\S*/;
      if (!value) {
        return callback(new Error("网址不能为空"));
      }
      setTimeout(() => {
        if (httpReg.test(value)) {
          callback();
        } else {
          callback(new Error("这网址不是以http://、https://开头，或者不是网址！"));
        }
      }, 100);
    };
    return {
      activeIndex: null,
      hoverIndex: null,
      innerDialogVisible: false,
      dialogVisible: false,
      systems: [],
      innerActiveIndex: "1",
      index: 1,
      form: {
        name: "",
        simpleName: "",
        appUrl: ""
      },
      imageUrl: "",
      postUrl: "",
      rules: {
        name: [{ required: true, message: "请输入应用名称", trigger: "blur" }],
        simpleName: [
          { required: true, message: "请输入应用简称", trigger: "blur" }
        ],
        appUrl: [
          { validator: checkHttp, required: true, trigger: "blur" }
        ],
      },
      treeData: [],
      defaultProps: {
        children: "children",
        label: "text"
      },
      activeId: null,
      tags: [
        {
          id: 1,
          name: "总经理",
          isClick: false
        },
        {
          id: 2,
          name: "部门经理",
          isClick: false
        },
        {
          id: 3,
          name: "项目经理",
          isClick: false
        }
      ],
      activeTags: []
    };
  },
  created() {
    this.getSystems();
  },
  methods: {
    getSystems() {
      searchApp().then(res => {
        if (res) {
          res.data.forEach((ele) => {
            if (ele.appLogo) {
              ele.appLogo = ele.appLogo.substr(ele.appLogo.indexOf("=") + 1)
            }
          })
          this.systems = res.data
        }
      });
    },
    sysClick(index, url) {
      this.activeIndex = index;
      // var reg = new RegExp("(^| )access_token=([^;]*)(;|$)");
      // url += "?username=zhuwch&token="+document.cookie.match(reg)[1];
      if (url) {
        window.open(url);
      }
    },
    deleClick(id) {
      deleteAll({ id }).then(res => {
        this.getSystems();
      })
      this.activeIndex = null;
    },
    editClick(id) {
      this.activeId = id;
      listAppAll({
        id: id
      })
        .then(res => {
          if (res) {
            this.form.name = res.data.appName;
            this.form.simpleName = res.data.appShortName;
            this.form.appUrl = res.data.appUrl;
            this.imageUrl = res.data.appLogo && res.data.appLogo.substr(res.data.appLogo.indexOf("=") + 1);
            this.postUrl = res.data.appLogo;
            this.powerArr = res.data.appPower;
            this.dialogVisible = true;
            this.activeTags = res.data.appPower;
            this.activeTags.forEach(v => {
              v.name = v.powerName
              v.id = v.powerId
            });
          }
        });
    },
    getTreeData(id) {
      // let str = id? `/app/getAllStaffByOrgs?id=${id}&state=1`: "/app/getAllStaffByOrgs?state=1"
      getAllStaffByOrgs(id).then(res => {
        if (res) {
          if (this.treeData.length < 1) {
            let data = JSON.parse(res.data)
            data.forEach(v => {
              v.isClick = false;
            });
            this.treeData = data
            this.$set(this.treeData[0], "children", [])
          } else {
            this.forE(this.treeData, res, id)
          }

        }
      })
    },
    getTags() {
      listRoles().then(res => {
        if (res) {
          res.data.forEach(v => {
            v.isClick = false
          })
          this.tags = res.data
        }
      })
    },
    forE(arr, res, id) {  //递归调用函数
      if (arr) {
        arr.forEach(v => {
          if (v.id == id) {
            JSON.parse(res.data).forEach(item => {
              item.isClick = false;
            })
            this.$set(v, "children", JSON.parse(res.data))
            return
          } else {
            this.forE(v.children, res, id)
          }
        })
      } else {
        return
      }
    },
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
      this.postUrl = '/api' + res;
    },
    deleteActiveTag(id) {
      this.activeTags.forEach((v, k) => {
        if (v.id == id) {
          this.activeTags.splice(k, 1)
        }
      })
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/png" || file.type === "image/jpeg";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传的系统logo请选择png、jpg、jpeg等图片格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传系统logo大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    power() {
      this.innerDialogVisible = true;
      this.getTreeData();
      this.getTags();
    },
    handleSelect(key, keyPath) {
      this.index = key;
    },
    handleNodeCheck(data) {
      if (this.activeTags.length > 0) {
        let key = true;
        this.activeTags.forEach(v => {
          if (v.name === data.text) {
            key = false;
          }
        });
        if (key) {
          this.activeTags.push({
            name: data.text,
            orgcode: data.orgcode || null,
            id: data.id,
            type: data.type
          });
        }
      } else {
        this.activeTags.push({
          name: data.text,
          orgcode: data.orgcode || null,
          id: data.id,
          type: data.type
        });
      }
    },
    handleNodeClick(data) {
      this.getTreeData(data.id)
    },
    tagClick(name, isClick) {
      if (!isClick) {
        this.activeTags.push({
          name,
          type: "3"
        });
        this.tags.forEach((v, k) => {
          if (v.roleName == name) {
            v.isClick = !v.isClick;
          }
        });
      }
    },
    clickedItem(item) {
      this.activeTags.forEach((v, k) => {
        if (v.name == item) {
          this.activeTags.splice(k, 1);
        }
      });
      this.tags.forEach(v => {
        if (v.roleName == item) {
          v.isClick = !v.isClick;
        }
      });
      this.treeData[0].children.forEach(v => {
        if (v.name == item) {
          v.isClick = !v.isClick;
        }
      });
    },
    cancel() {
      this.innerDialogVisible = false;
    },
    confirm() {
      this.innerDialogVisible = false;
    },
    addApplicationBtn(formName) {
      let powerArr = [];
      this.activeTags.forEach(v => {
        if (v.type == 'department') {
          powerArr.push({
            powervalue: v.orgcode,
            powertype: v.type
          })
        } else if (v.type == 'account') {
          powerArr.push({
            powervalue: v.id,
            powertype: v.type
          })
        } else if (v.powerType) {
          powerArr.push({
            // powervalue:v.id,
            // powertype:v.type
            powervalue: v.powerValue,
            powertype: v.powerType
          })
        } else {
          powerArr.push({
            powervalue: v.name,
            powertype: 'role'
          })
        }
      })
      this.$refs[formName].validate(valid => {
        if (valid && this.imageUrl) {
          updateAppAndPower({
            id: this.activeId,
            appname: this.form.name,
            appshortname: this.form.simpleName,
            applogo: this.postUrl,
            // appurl:this.postUrl,
            appurl: this.form.appUrl,
            power: powerArr
          }).then(res => {
            if (res.code == 200) {
              this.dialogVisible = false;
              this.$message.success("修改成功！")
              this.getSystems();
              this.form = {
                name: "",
                simpleName: "",
                appUrl: ""
              }
              this.$refs.form.resetFields();
            }
          })
        } else {
          this.$message.error("请将信息填写完整!")
        }
      })
    }
  }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.container {
  .system-box {
    margin-top: 70px;
    margin-left: 70px;

    .sys-container {
      width: 260px;
      border-radius: 10px;
      background-color: rgba(59, 72, 176, 0.45);
      float: left;
      margin-right: 30px;
      margin-top: 30px;
      text-align: center;
      padding-top: 30px;
      padding-bottom: 37px;
      cursor: pointer;
      position: relative;

      #clicked {
        font-size: 24px;
        position: absolute;
        top: 6px;
        right: 10px;
        color: #fff;
        cursor: pointer;
      }

      #clicked1 {
        font-size: 26px;
        position: absolute;
        top: 6px;
        right: 38px;
        color: #fff;
        cursor: pointer;
      }

      .icon-box {
        width: 60px;
        height: 60px;
        overflow: hidden;
        border-radius: 50%;
        text-align: center;
        margin: 0 auto;
        background-color: #fff;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;

        img {
          width: 60px;
          height: 60px;
          z-index: 999;
        }
      }

      .refer {
        color: #fff;
        font-size: 26px;
        margin-top: 28px;
      }

      .name {
        margin-top: 19px;
        color: #fff;
        font-size: 24px;
      }
    }

    .add-system {
      width: 258px;
      height: 222px;
      float: left;
      margin-right: 30px;
      cursor: pointer;
      border-radius: 10px;
      border: 1px solid #fff;
      margin-top: 30px;
      text-align: center;
      line-height: 222px;
    }
  }
}

.sys-box::after {
  content: "";
  display: block;
  clear: both;
}

.active {
  background-color: #f3a21f !important;
}

.active i {
  color: #fff !important;
}

.containerActive {
  box-shadow: 0 0 10px #aabdff;
}

.left-box {
  width: 50%;
  float: left;
  max-height: 370px;
  padding: 15px;
  overflow-y: auto;
}

.right-box {
  margin-left: 30px;
  float: left;
  max-height: 370px;
  padding: 15px;
  overflow-y: auto;
  min-height: 100px;
  padding-left: 30px;
  border-left: 1px solid #949494;
}

.button-box {
  margin-top: 30px;
  width: 150px;
  margin: 0 auto;
}

.tag {
  font-size: 16px;
  line-height: 20px;
  cursor: pointer;
  margin: 10px 0 10px 20px;
}
</style>
