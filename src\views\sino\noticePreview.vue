<template>
    <div class="tab" id="version">
        <el-row type="flex" justify="center" class="title">
            <el-col :span="23">
                <div class="title_header">通知公示阅览</div>
            </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="search">
            <el-col :span="23" class="searchBox">
                <template>
                    <el-col :span="24" style="text-align: right">
                        <el-input placeholder="请输入标题" @keyup.enter.native="search()" style="width: 30%"
                            v-model.trim="query.title" @input="handleInputSearch">
                            <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer"
                                @click="search()"></i>
                        </el-input>
                    </el-col>
                </template>
            </el-col>
        </el-row>

        <el-row type="flex" justify="center">
            <el-col :span="23">
                <el-table ref="versionTable" :data="tableData" element-loading-background="rgb(33,39,77, 0.8)"
                    height="calc(100vh - 290px)" style="width: 100%; " class="tableT" v-loading="loading"
                    highlight-current-row>
                    <el-table-column label="标题" prop="title">
                        <template slot-scope="props">
                            {{ props.row.title }}
                        </template>
                    </el-table-column>
                    <el-table-column label="发布人" prop="userName">
                    </el-table-column>
                    <el-table-column label="发布时间" prop="createtime">
                        <template slot-scope="props">
                            <span>{{ formatDate(props.row.createtime) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="state">
                        <template slot-scope="scope">
                            <span>{{ scope.row.readFlag == 1 ? '已读' : '未读' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                        <template slot-scope="scope">
                            <el-button @click="handleDetail(scope.row)" type="text" size="small">详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pager">
                    <el-pagination @size-change="handleSizeChange" @current-change="pagehandleCurrentChange"
                        :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pagesize"
                        layout="total, sizes, prev, pager, next, jumper" :total="totalcount"></el-pagination>
                </div>
            </el-col>
        </el-row>

        <!--详情弹框-->
        <el-dialog ref="detailDialog" title="通知公示详情" :visible.sync="detailDialogVisible" width="900px" class="addP"
            :close-on-click-modal="false" :before-close="detailmenuClose" :show-close="false">
            <div style="text-align: left">
                <el-form label-position="left" label-width="120px">
                    <el-col :span="24">
                        <el-form-item label="标题">
                            <span>{{ detailData.title }}</span>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="内容">
                            <div v-html="detailData.content"></div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="附件">
                            <div v-for="(item, index) in detailData.attach" :key="index" class="attachment-item">
                                <div>{{ item.attachName }}</div>
                                <el-button type="text" size="small" @click="handleDownload(item)">浏览</el-button>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="已读状态">
                            <el-tag :type="detailData.readFlag === '1' ? 'success' : 'warning'">
                                {{ detailData.readFlag === '1' ? '已读' : '未读' }}
                            </el-tag>
                            <el-button v-if="detailData.readFlag !== '1'" type="text" size="small"
                                style="margin-left: 10px" @click="handleUpdateRead">
                                标记为已读
                            </el-button>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <div style="width: 100%;text-align: right;">
                    <el-button @click="detailmenuClose" class="CancButton">关 闭</el-button>
                </div>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import {
    downLoadFile, updateAttachPassRead, getDetailByUserId, getAttachPassByUserId
} from "@/api/sino/newRelease";
import { mapActions } from 'vuex';
import moment from 'moment';
export default {
    name: "NoticePreview",
    data() {
        return {
            defaultProps: {
                children: 'children',
                label: 'name'
            },
            currentRow: null,
            //版本更新列表
            tableData: [],
            //版本更新列表查询参数
            query: {
                title: '',
                catalogid: '',
            },
            //版本更新loading
            loading: false,
            //用户手册loading
            currentPage: 1,
            pagesize: 10,
            searchword: "",
            totalcount: null,
            detailDialogVisible: false,
            detailData: {
                title: '',
                content: '',
                attach: [],
                userNames: ''
            },
            searchDebounceTimer: null,
        }
    },

    created() {
        //列表页获取
        this.search();
    },
    mounted() {
        this.getListDateUnread()
    },
    methods: {
        ...mapActions(['SET_NOTICE_NUM']),
        //查询置空
        handResertSearch() {
            this.query = {
                title: '',
                catalogid: '',
            }
        },
        //版本列表查询
        search() {
            this.currentPage = 1;
            this.getListDate()
        },

        //修改表格分页的展示条数
        handleSizeChange(val) {
            this.pagesize = val;
            this.getListDate();
        },
        //修改表格分页的当前页
        pagehandleCurrentChange(val) {
            this.currentPage = val;
            this.getListDate();
        },
        // 修改表格分页的展示条数
        handleCurrentChange(val) {
            this.currentRow = val;
        },
        //获取列表数据
        getListDate() {
            this.loading = true;
            let param = {
                ...this.query,
                pageNum: this.currentPage,
                pageSize: this.pagesize,
                userId: window.localStorage.getItem("Susername"),
                type: 'type_2'
            }

            getAttachPassByUserId(param).then(res => {
                this.tableData = res.data.subject;
                this.totalcount = res.data.totalCount;
                this.loading = false;
            })
        },
        getListDateUnread() {
            let param = {
                title: '',
                catalogid: '',
                pageNum: 1,
                pageSize: 10000,
                userId: window.localStorage.getItem("Susername"),
                type: 'type_2'
            }
            getAttachPassByUserId(param).then(res => {
                let len = res.data.subject.filter((item) => item.readFlag == "0");
                // this.$store.dispatch('SET_NOTICE_NUM', newDialogs)
                this.SET_NOTICE_NUM(len.length);
            })
        },

        //时间格式化
        formatDate(dateStr) {
            return moment(dateStr).format('YYYY-MM-DD');
        },

        //关闭更新详情弹框
        detailmenuClose() {
            this.detailDialogVisible = false
            this.detailData = {
                title: '',
                content: '',
                attach: [],
                userNames: ''
            }
        },

        // 查看详情
        handleDetail(row) {

            getDetailByUserId(row.id, window.localStorage.getItem("Susername"), 'type_2').then(res => {
                if (res.code === 200) {
                    this.detailData = res.data;
                    this.detailDialogVisible = true;
                } else {
                    this.$message.error(res.message || '获取详情失败');
                }
            }).catch(() => {
                this.$message.error('获取详情失败');
            });
        },

        // 更新已读状态
        handleUpdateRead() {
            updateAttachPassRead(this.detailData.id, window.localStorage.getItem("Susername")).then(res => {
                if (res.code === 200) {
                    this.$message.success('已更新为已读状态');
                    this.detailData.readFlag = '1';

                    this.getListDateUnread()
                    this.getListDate();
                } else {
                    this.$message.error(res.message || '更新已读状态失败');
                }
            }).catch(() => {
                this.$message.error('更新已读状态失败');
            });
        },
        // 下载附件
        handleDownload(item) {
            const isPdf = item.attachName.toLowerCase().endsWith('.pdf');
            downLoadFile(item.filePath).then(res => {
                if (isPdf) {
                    const blob = new Blob([res], { type: 'application/pdf' });
                    const pdfUrl = window.URL.createObjectURL(blob);
                    window.open(pdfUrl, '_blank'); // 新标签页打开
                    // 可选：延迟释放 URL，避免 PDF 未加载完就被释放
                    setTimeout(() => {
                        window.URL.revokeObjectURL(pdfUrl);
                    }, 10000); // 10秒后释放
                } else {
                    const blob = new Blob([res], {
                        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    });
                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement("a");
                    link.style.display = "none";
                    link.href = url;
                    link.setAttribute("download", item.attachName);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                }
            }).catch(() => {
                this.$message.error('下载失败');
            });
        },
        handleInputSearch() {
            clearTimeout(this.searchDebounceTimer);
            this.searchDebounceTimer = setTimeout(() => {
                this.search();
            }, 500); // 500ms内只会触发一次
        },
    },
};
</script>

<style scoped lang="less">
.tab {
    background-color: #21274D;
    margin: -20px 0;
    height: calc(100vh - 90px);

    ::-webkit-scrollbar {
        width: 6px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #21274D;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #888;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        //background: #555;
    }
}

.pager {
    width: 100%;
    overflow: hidden;
    position: relative;
    margin-top: 10px;
    margin-bottom: 10px;
}

.el-pagination {
    float: right;
}

.title {
    height: 66px;
    line-height: 66px;

    /deep/ .el-input .el-input__inner {
        background-color: rgba(255, 255, 255, 0.25) !important;
        color: rgba(199, 199, 199, 1) !important;
        border: 0px;
    }
}

.search {
    height: 70px;
    line-height: 40px;

    .searchBox {
        padding: 10px 0;
        position: relative;
    }

    /deep/ .el-input .el-input__inner,
    .el-range-editor.el-input__inner,
    .el-range-editor.el-range-input,
    .el-range-editor.el-range-separator {
        background-color: rgba(255, 255, 255, 0.25) !important;
        color: rgba(199, 199, 199, 1) !important;
        border: 0px;
    }
}

.add,
.edit {
    color: #fff;
    margin-left: 15px;
    border: none;
}

.add {
    background-color: #037cb3;
}

.edit {
    background-color: #17b3d9;
}

.sure {
    background: none repeat scroll 0 0 #61b0e9;
    border-color: #389ae2;
    color: #fff;
    text-shadow: none;
    text-transform: uppercase;
}

.text01 {
    font-size: 30px;
    font-family: "微软雅黑";
    color: #08657E;
    margin-left: 15px;
}

.text02 {
    font-size: 18px;
    font-family: "微软雅黑";
    color: #767676;
    margin-left: 15px;
}

.clickTimes {
    font-size: 12px;
    margin-left: 15px;
}

/* 13寸 */
@media screen and (min-width: 600px) and (max-width: 1300px) {
    .tableT {
        margin-bottom: -5px
    }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
    .tableT {
        margin-bottom: 30px
    }
}

/deep/ .addP .el-dialog__footer {
    text-align: center !important;
}

ul {
    list-style-type: disc !important;
}

/deep/ .el-loading-mask {
    background-color: #21274d !important;
}

/deep/ #haneleDetailId .el-loading-mask {
    background-color: rgba(255, 255, 255, 1) !important;
}

.myIcon {
    color: #409eff;
    padding: 0 5px;
    cursor: pointer;
}

/deep/ .el-icon-d-arrow-right {
    font-size: 15px !important;
    color: white !important;
}

/deep/ #recordContent .ql-editor {}

/deep/ #recordContent li {
    padding: 0px 0px !important;
    line-height: 25px;
}

/deep/ #recordContent .ql-editor ol {
    padding-left: 30px !important;
}

/deep/ #recordContent .ql-editor ul {
    margin-left: -42px !important;
    margin-top: -5px;
}

/deep/ #recordContent .ql-editor p {
    padding-left: 0px !important;
}

/deep/ #recordContent .ql-editor li:not(.ql-direction-rtl)::before {
    color: #d8d8d8;
    font-size: 38px;
    vertical-align: middle;
    /* 调整垂直对齐方式 */
}

/deep/ strong {
    font-weight: bold !important;
}

/deep/ .addP .el-form-item__label {
    color: #939393;
}

/deep/ .search .el-range-editor.el-input__inner {
    width: 100%;
}

/deep/ #updateDetail {
    .el-form-item {
        margin: 0;
    }
}

::v-deep .el-upload-list__item {
    transition: none !important;
    -webkit-transition: nonne !important;
}

::v-deep .el-upload-list__item-name {
    transition: none !important;
    -webkit-transition: nonne !important;
}

.attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-bottom: 10px;
}
</style>
