import Vue from 'vue'
import Router from 'vue-router'
const originalPush = Router.prototype.push
Router.prototype.push = function push(location, onResolve, onReject) {
    if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
    return originalPush.call(this, location).catch(err => err)
}
Vue.use(Router);


export default new Router({
    mode: 'hash',
    routes: [
        {
            path: '/',
            name: 'login',
            component: () => import('@/views/Login.vue')
        },
        {
            path: '/accLogin',
            name: 'accLogin',
            component: () => import('@/views/Login.vue')
        },
        {
            path: '/scanSign',
            name: 'scanSign',
            component: () => import('@/views/ScanSign.vue')
        },
        {
            path: '/wechatDebug',
            name: 'wechatDebug',
            component: () => import('@/views/WeChatDebug.vue')
        },
        {
            path: '/msgError',
            name: 'msgError',
            component: () => import('../../src/views/msgError.vue')
        },
        {
            path: '/countUser',
            name: 'countUser',
            component: () => import('../../src/views/countUser.vue')
        },
        {
            path: '/sino',
            name: 'sino',
            redirect: '/sino/index',
            component: () => import('@/views/Sino.vue'),
            children: [
                {
                    path: 'index',
                    name: 'index',
                    component: () => import('@/views/sino/menuIndex.vue')
                }
                ,
                {
                    path: "login.js",
                    name: "login.js",
                    component: () => import('@/views/sino/menuIndex.vue')
                },
                {
                    path: "waiting",
                    name: "waiting",
                    component: () => import('@/views/sino/toDoList.vue')
                },
                {
                    path: 'organization',
                    name: 'organization',
                    component: () => import('@/views/sino/orgStructure.vue')
                },
                {
                    path: "application",
                    name: "application",
                    component: () => import('@/views/sino/accessmanage.vue')
                },
                {
                    path: "addApplication",
                    name: "addApplication",
                    component: () => import('@/views/sino/addApplication.vue')
                },
                {
                    path: "authoritymanage",
                    name: "authoritymanage",
                    component: () => import('@/views/sino/authoritymanage.vue')
                },
                {
                    path: "postmanage",
                    name: "postmanage",
                    component: () => import('@/views/sino/postmanage.vue')
                },
                {
                    path: "rolemanage",
                    name: "rolemanage",
                    component: () => import('@/views/sino/rolemanage.vue')
                },
                {
                    path: 'staffmanage',
                    name: 'staffmanage',
                    component: () => import('@/views/sino/staffmanage.vue')
                },
                {
                    path: 'branchmanage',
                    name: 'branchmanage',
                    component: () => import('@/views/sino/branchmanage.vue')
                },
                {
                    path: 'accountmanage',
                    name: 'accountmanage',
                    component: () => import('@/views/sino/accountmanage.vue')
                },
                {
                    path: 'menuIndex',
                    name: 'menuIndex',
                    component: () => import('@/views/sino/menuIndex.vue')
                },
                {
                    path: "menuIndexMoreNews",
                    name: "menuIndexMoreNews",
                    component: () => import("@/views/sino/menuIndexMoreNews.vue"),
                },
                {
                    path: 'accessmanage',
                    name: 'accessmanage',
                    component: () => import('@/views/sino/accessmanage.vue')
                },
                {
                    path: 'orgStructure',
                    name: 'orgStructure',
                    component: () => import('@/views/sino/orgStructure.vue')
                },
                {
                    path: 'addressbook',
                    name: 'addressbook',
                    component: () => import('@/views/sino/addressbook.vue')
                },
                {
                    path: 'versionUpdateRecords',
                    name: 'versionUpdateRecords',
                    component: () => import('@/views/sino/versionUpdateRecords.vue')
                },
                {
                    path: 'payslipQuery',
                    name: 'payslipQuery',
                    component: () => import('@/views/human/payslipQuery.vue')
                },
                {
                    path: 'resignApplication',
                    name: 'resignApplication',
                    component: () => import('@/views/human/resignApplication.vue')
                },
                {
                    path: 'certManager',
                    name: 'certManager',
                    component: () => import('@/views/human/certManager.vue')
                },
                {
                    path: 'myCert',
                    name: 'myCert',
                    component: () => import('@/views/human/myCert.vue')
                },
                {
                    path: 'resignApplication',
                    name: 'resignApplication',
                    component: () => import('@/views/human/resignApplication.vue')
                },
                {
                    path: 'newRelease',
                    name: 'newRelease',
                    component: () => import('@/views/sino/newRelease.vue')
                },
                {
                    path: 'attachPass',
                    name: 'attachPass',
                    component: () => import('@/views/sino/attachPass.vue')
                },
                {
                    path: 'noticePreview',
                    name: 'NoticePreview',
                    component: () => import('@/views/sino/noticePreview.vue')
                },
                {
                    path: 'noticePass',
                    name: 'noticePass',
                    component: () => import('@/views/sino/noticePass.vue')
                },
                {
                    path: 'attachPreview',
                    name: 'attachPreview',
                    component: () => import('@/views/sino/attachPreview.vue')
                },
                {
                    path: 'noticeReadStatistics',
                    name: 'noticeReadStatistics',
                    component: () => import('@/views/human/noticeReadStatistics.vue')
                },
                {
                    path: 'attachPassStatistics',
                    name: 'attachPassStatistics',
                    component: () => import('@/views/human/attachPassStatistics.vue')
                },
                {
                    path: 'noticePassStatistics',
                    name: 'noticePassStatistics',
                    component: () => import('@/views/human/noticePassStatistics.vue')
                },
                {
                    path: 'logmanage',
                    name: 'logmanage',
                    component: () => import('@/views/human/logmanage.vue')
                }
            ]
        },
        {
            path: '/404',
            component: () => import('@/views/error/404.vue'),
            hidden: true
        },
        {
            path: '*',
            redirect: '/404',
            hidden: true
        },

    ]
})
