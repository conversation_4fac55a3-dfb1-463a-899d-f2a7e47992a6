<template>
  <div class="org_wrapper" v-loading="pageLoading" element-loading-background="rgb(33,39,77, 0.8)">
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23">
        <div  class="title_header">分公司管理</div>
        <div style="margin-bottom:20px;float: right;">
          <el-row type="flex" justify="space-between">
            <div>
            </div>
          </el-row>
        </div>
      </el-col>
    </el-row>

    <el-row type="flex" justify="center">
      <el-col :span="23">
        <div class="main_container">

            <el-row class="search_line">
                <el-col :span="12">
                    <el-cascader style="width: 300px" placeholder="输入公司名称进行过滤" :show-all-levels="false" :options="companyList"
                                 v-model="dmValue" :props="{
                  checkStrictly: true,
                  label: 'name',
                  value: 'dmValue',
                  emitPath: false
                }" :clearable="false" @change="onCompanyChange" filterable>

                    </el-cascader>
                </el-col>
                <el-col :span="12" style="text-align: right">
                    <el-button class="add" size="medium" @click="onExport" style="margin-right: 10px">导出
                    </el-button>
                    <el-input placeholder="请输入内容"  @keyup.enter.native="onSearchTxtChange" v-model="searchTxt" style="width:306px" class="input-with-select">
                        <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer"
                           @click="onSearchTxtChange"></i>
                    </el-input>
                </el-col>
            </el-row>
            <el-table :data="tableData" style="width: 100%;margin-bottom: 8px;" height="calc(100vh - 200px)" row-key="id"
              class="tableT" highlight-current-row :expand-row-keys="expandRowKeys" @expand-change="onExpandChange"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
              <el-table-column label="员工姓名" prop="staffname">
              </el-table-column>
              <el-table-column prop="state" label="状态">
              </el-table-column>
              <el-table-column prop="username" label="登录帐号">
              </el-table-column>
              <el-table-column prop="positionnam" label="职务">
              </el-table-column>
              <el-table-column prop="mobilephone"  label="手机号">
              </el-table-column>
              <el-table-column prop="conactphone"  label="电话">
              </el-table-column>
            </el-table>
            <div class="paginator">
              <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="totalcount">
              </el-pagination>
            </div>

        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getCompanyStaff, getCompanyList } from "@/api/sino/company";
export default {
  name: "branchmanage",
  data() {
    return {
      pageLoading: false,
      searchTxt: '',
      filterText: '',
      pageSize: 10,
      currentPage: 1,
      formLabelWidth: '120px',
      dmValue: '',
      expandRowKeys: ['200'],
      totalcount: null,
      tableData: [],
      companyList:[]
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree2.filter(val);//查询
    }
  },
  created() {
    this.getCompanyData();
  },
  methods: {
    getCompanyData() {
      getCompanyList().then(res => {
        if (res.code == 200) {
          this.companyList = [res.data];
          this.dmValue = res.data.dmValue;
          this.getCompanyStaffListWithParams();
        }
      })
    },
    //导出
    async onExport() {
      const params = {
        pageSize: this.pageSize,
        pageNum: this.currentPage,
        userName: this.searchTxt,
        export: '1'
      };
      if (this.dmValue) {
        params.dmValue = this.dmValue;
      }
      this.pageLoading = true;
      try {
        await this.getCompanyStaffList(params);
        this.pageLoading = false;
      } catch (e) {
        this.pageLoading = false;
      }
    },
    //切换分公司 获取员工数据
    onCompanyChange() {
      this.currentPage = 1;
      this.getCompanyStaffListWithParams();
    },
    onSearchTxtChange() {
      this.currentPage = 1;
      this.getCompanyStaffListWithParams();
    },
    getCompanyStaffListWithParams() {
      const params = {
        pageSize: this.pageSize,
        pageNum: this.currentPage,
        userName: this.searchTxt
      };
      if (this.dmValue) {
        params.dmValue = this.dmValue;
      }
      getCompanyStaff(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.userList;
          this.totalcount = res.data.totalCount;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getCompanyStaffListWithParams();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getCompanyStaffListWithParams();
    },
    //张开分支后获取数据
    onExpandChange(row, expanded) {
      if (expanded) {
        this.expandRowKeys.push(row.id)
      } else {
        this.expandRowKeys = this.expandRowKeys.filter(key => key !== row.id)
      }
    },
    getCompanyStaffList(params) {
      getCompanyStaff(params)
    }
  },
  computed: {
  }
}
</script>

<style scoped lang="less">
.paginator {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-bottom: 20px;
}

.org_wrapper {
  background-color: #21274D;
  margin: -20px 0;
  height: calc(100vh - 90px);
}

.add {
  width: 98px;
  //border-radius: 54px;
  background-color: #0e61c9;
  color: #ffffff;
  border: 0px;
}

.title {
  height: 66px;
  line-height: 66px;
  // border-bottom: 1px solid #eee;
  /*margin-bottom: 10px;*/
}

.main_container {
  display: flex;
  flex-direction: column;
  height:calc(100vh - 160px);
    .search_line {
      /*width: 300px;*/
      /*float: right;*/
      margin-bottom: 15px;
      display: flex;
      justify-content: space-between;

      /deep/ .el-input .el-input__inner {
        //border-radius: 34px !important;
        background-color: rgba(255, 255, 255, 0.25) !important;
        color: rgba(199, 199, 199, 1) !important;
        border: 0px;
      }
    }

    ::-webkit-scrollbar {
      width: 6px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
      background: #21274D;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
      background: #888;
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

}

</style>
