import request from "@/utils/request";
import qs from "qs";
export const listOrganizations = () => {
  return new Promise((resolve) => {
    request.get("/dept/selectDept").then((res) => {
      resolve(res);
    });
  });
};
//员工管理
export const selectUser = () => {
  return new Promise((resolve) => {
    request.get("/dept/selectUser").then((res) => {
      resolve(res);
    });
  });
};
export const selectPrincipal = () => {
  return new Promise((resolve) => {
    request.get("/dept/selectPrincipal").then((res) => {
      resolve(res);
    });
  });
};
//新增组织
export const addOrg = (param) => {
  return new Promise((resolve) => {
    request.post("/dept/addDept", param).then((res) => {
      resolve(res);
    });
  });
};
//修改组织
export const updateOrg = (param) => {
  return new Promise((resolve) => {
    request.post("/dept/updDept", param).then((res) => {
      resolve(res);
    });
  });
};
//删除组织
export const deleteOrg = (param) => {
  let headerParams = {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  };
  return new Promise((resolve) => {
    request
      .post("/dept/delDept", qs.stringify(param), headerParams)
      .then((res) => {
        resolve(res);
      });
  });
};
