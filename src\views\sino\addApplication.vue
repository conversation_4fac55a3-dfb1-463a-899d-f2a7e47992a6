<template>
  <div class="container">
    <el-form ref="formU" :model="form" label-width="100px" :rules="rules">
      <el-form-item label="应用名称" prop="name">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item label="应用logo" :required="true">
        <el-upload class="avatar-uploader" action="/api/appPerm/upAppLog" :show-file-list="false"
          :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload">
          <img v-if="imageUrl" :src="imageUrl" class="avatar" alt="imageUrl"/>
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="应用简称" prop="simpleName">
        <el-input v-model="form.simpleName"></el-input>
      </el-form-item>
      <el-form-item label="应用地址" prop="appUrl">
        <el-input v-model="form.appUrl"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="warning" @click="power">配置应用权限</el-button>
      </el-form-item>
      <el-form-item v-if="activeTags.length > 0">
        <el-button round type="success" size="mini" v-for="(item, index) in activeTags" :key="index">{{ item.name
}}<i class="el-icon-close el-icon--right" @click="deleteActiveTag(item.id)"></i></el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addApplicationBtn('formU')">添加应用</el-button>
      </el-form-item>
    </el-form>
    <el-dialog title="配置权限" :visible.sync="dialogVisible">
      <div class="left-box">
        <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
          <el-menu-item index="1">组织架构</el-menu-item>
          <el-menu-item index="2">标签</el-menu-item>
        </el-menu>
        <el-tree v-if="index == 1" :data="treeData" :props="defaultProps" @node-click="handleNodeClick"
          @check="handleNodeCheck" :default-expand-all="true" :accordion="true" :expand-on-click-node="false"
          show-checkbox></el-tree>
        <div v-if="index == 2">
          <p class="tag" v-for="item in tags" :key="item.roleId" @click="tagClick(item.roleName, item.isClick)">
            {{ item.roleName }}
          </p>
        </div>
      </div>
      <div class="right-box">
        <p class="title">选择项</p>
        <p v-for="(item, index) in activeTags" class="tag" :key="index" @click="clickedItem(item.name)">
          {{ item.name }}
        </p>
      </div>
      <div style="clear: both"></div>
      <div class="button-box">
        <el-button type="primary" @click="confirm">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getAllStaffByOrgs, saveApp } from "@/api/sino/application";
import { listRoles } from "@/api/sino/roleUrl";
export default {
  name: "addApplication",
  data() {
    var checkHttp = (rule, value, callback) => {
      const httpReg = /(http|https):\/\/([\w.]+\/?)\S*/;
      if (!value) {
        return callback(new Error("网址不能为空"));
      }
      setTimeout(() => {
        if (httpReg.test(value)) {
          callback();
        } else {
          callback(
            new Error("这网址不是以http://、https://开头，或者不是网址！")
          );
        }
      }, 100);
    };
    return {
      dialogVisible: false,
      activeIndex: "1",
      index: 1,
      form: {
        name: "",
        simpleName: "",
        appUrl: "",
      },
      imageUrl: "",
      postUrl: "",
      rules: {
        name: [{ required: true, message: "请输入应用名称", trigger: "blur" }],
        simpleName: [
          { required: true, message: "请输入应用简称", trigger: "blur" },
        ],
        appUrl: [{ validator: checkHttp, required: true, trigger: "blur" }],
      },
      treeData: [],
      defaultProps: {
        children: "children",
        label: "text",
      },
      tags: [
        {
          id: 1,
          name: "总经理",
          isClick: false,
        },
        {
          id: 2,
          name: "部门经理",
          isClick: false,
        },
        {
          id: 3,
          name: "项目经理",
          isClick: false,
        },
      ],
      activeTags: [],
    };
  },
  created() { },
  methods: {
    getTreeData(id) {
      getAllStaffByOrgs(id).then((res) => {
        if (res) {
          if (this.treeData.length < 1) {
            res.forEach((v) => {
              v.isClick = false;
            });
            this.treeData = res;
            this.$set(this.treeData[0], "children", []);
          } else {
            this.forE(this.treeData, res, id);
          }
        }
      });
    },
    getTags() {
      listRoles().then((res) => {
        if (res) {
          res.data.forEach((v) => {
            v.isClick = false;
          });
          this.tags = res.data;
        }
      });
    },
    forE(arr, res, id) {
      //递归调用函数
      if (arr) {
        arr.forEach((v) => {
          if (v.id == id) {
            res.data.forEach((ele) => {
              ele.isClick = false;
            });
            this.$set(v, "children", res.data);
            return;
          } else {
            this.forE(v.children, res, id);
          }
        });
      } else {
        return;
      }
    },
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
      this.postUrl = "/api" + res;
    },
    deleteActiveTag(id) {
      this.activeTags.forEach((v, k) => {
        if (v.id == id) {
          this.activeTags.splice(k, 1);
        }
      });
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/png" || file.type === "image/jpeg";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("上传的系统logo请选择png、jpg、jpeg等图片格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传系统logo大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    power() {
      this.dialogVisible = true;
      this.getTreeData();
      this.getTags();
    },
    handleSelect(key) {
      this.index = key;
    },
    handleNodeCheck(data) {
      if (this.activeTags.length > 0) {
        let key = true;
        this.activeTags.forEach((v) => {
          if (v.name === data.text) {
            key = false;
          }
        });
        if (key) {
          this.activeTags.push({
            name: data.text,
            orgcode: data.orgcode || null,
            id: data.id,
            type: data.type,
          });
        }
      } else {
        this.activeTags.push({
          name: data.text,
          orgcode: data.orgcode || null,
          id: data.id,
          type: data.type,
        });
      }
    },
    handleNodeClick(data) {
      this.getTreeData(data.id);
    },
    tagClick(name, isClick) {
      if (!isClick) {
        this.activeTags.push({
          name,
          type: "3",
        });
        this.tags.forEach((v) => {
          if (v.roleName == name) {
            v.isClick = !v.isClick;
          }
        });
      }
    },
    clickedItem(item) {
      this.activeTags.forEach((v, k) => {
        if (v.name == item) {
          this.activeTags.splice(k, 1);
        }
      });
      this.tags.forEach((v) => {
        if (v.roleName == item) {
          v.isClick = !v.isClick;
        }
      });
      this.treeData[0].children.forEach((v) => {
        if (v.name == item) {
          v.isClick = !v.isClick;
        }
      });
    },
    cancel() {
      this.dialogVisible = false;
    },
    confirm() {
      this.dialogVisible = false;
    },
    addApplicationBtn(formName) {
      let powerArr = [];
      this.activeTags.forEach((v) => {
        if (v.type == "department") {
          powerArr.push({
            powervalue: v.orgcode,
            powertype: v.type,
          });
        } else if (v.type == "account") {
          powerArr.push({
            powervalue: v.id,
            powertype: v.type,
          });
        } else if (v.powerType) {
          powerArr.push({
            // powervalue:v.id,
            // powertype:v.type
            powervalue: v.powerValue,
            powertype: v.powerType,
          });
        } else {
          powerArr.push({
            powervalue: v.name,
            powertype: "role",
          });
        }
      });
      this.$refs[formName].validate((valid) => {
        if (valid && this.imageUrl) {
          saveApp({
            appname: this.form.name,
            appshortname: this.form.simpleName,
            applogo: this.postUrl,
            appurl: this.form.appUrl,
            power: powerArr,
          }).then((res) => {
            if (res.code == 200) {
              this.$message.success("添加成功！");
              this.form = {
                name: "",
                simpleName: "",
                appUrl: "",
              };
              this.activeTags = [];
              this.imageUrl = "";
              this.$refs.formU.resetFields();
              // this.$router.go(0);
              // this.$router.push('/sino/application')
            }
          });
        } else {
          this.$message.error("请将信息填写完整!");
        }
      });
    },
  },
};
</script>
<style scoped lang="less">
.container {
  margin-left: 30%;
  padding: 20px 50px;
  border-radius: 10px;
  background-color: #fff;
  width: 328px;
}

.tag {
  font-size: 16px;
  line-height: 20px;
  cursor: pointer;
  margin: 10px 0 10px 20px;
}

.left-box {
  width: 50%;
  float: left;
  max-height: 550px;
  padding: 15px;
  overflow-y: auto;
}

.right-box {
  margin-left: 30px;
  float: left;
  max-height: 550px;
  padding: 15px;
  overflow-y: auto;
  min-height: 100px;
  padding-left: 30px;
  border-left: 1px solid #949494;
}

.button-box {
  margin-top: 30px;
  width: 150px;
  margin: 0 auto;
}
</style>
