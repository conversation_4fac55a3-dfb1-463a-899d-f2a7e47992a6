import request from "@/utils/request";
import qs from 'qs'
//获取企业微信扫码地址
export const thirdPartyLoginQR = (refreshToken) => {
  const param = {
    grant_type: "refresh_token",
    refresh_token: refreshToken,
    client_id: "oauth",
    client_secret: "oauth",
  };
  return new Promise((resolve) => {
    request.post("/thirdPartyLoginQR", param).then((res) => {
      resolve(res);
    });
  });
};

export const thirdLoginQR = () => {
  return new Promise((resolve) => {
    request.post("/thirdLoginQR").then((res) => {
      resolve(res);
    });
  });
};


export const createToken = (param) => {
  return new Promise((resolve) => {
    let headerParams = {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    };
    request.post("/createToken", qs.stringify(param), headerParams).then((res) => {
      resolve(res);
    });
  });
};
//登录接口
export const loginIn = (param) => {
  return new Promise((resolve) => {
    let headerParams = {
        headers : {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    }
    request.post("/login", qs.stringify(param),headerParams).then((res) => {
      resolve(res);
    });
  });
};
//切换账号
export const switchLogin = (param) => {
  return new Promise((resolve) => {
    let headerParams = {
        headers : {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    }
    request.post("/switchLogin", qs.stringify(param),headerParams).then((res) => {
      resolve(res);
    });
  });
};

export const switchLoginCheck = (param) => {
  return new Promise((resolve) => {
    let headerParams = {
        headers : {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    }
    request.post("/switchLoginCheck", qs.stringify(param),headerParams).then((res) => {
      resolve(res);
    });
  });
};
//登录接口
export const leavelogin = (param) => {
  return new Promise((resolve) => {
    request
      .get(
        "leavelogin?userName=" +
          param.userName +
          "&&passWord=" +
          param.passWord +
          "&&key=" +
          param.key
      )
      .then((res) => {
        resolve(res);
      });
  });
};
// 密码过期时间
export const passwordExpiration = (userName) => {
  return new Promise((resolve) => {
    request
      .get(
        "/passwordExpire?userName=" + userName 
      )
      .then((res) => {
        resolve(res);
      });
  });
};