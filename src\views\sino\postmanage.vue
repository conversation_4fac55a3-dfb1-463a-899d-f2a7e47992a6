<template>
  <div class="tab">
    <el-row type="flex" justify="center" class="title">
      <el-col :span="23" style="margin-bottom:20px;">
        <div  class="title_header">岗位管理</div>
      </el-col>
    </el-row>
    <el-row type="flex" justify="center">
      <el-col :span="23">
        <div style="margin-bottom: 15px">
          <el-button class="btnStyle" type="primary" style=" background-color: #0E61C9;" size="medium"
                     @click="addmenu">新增
          </el-button>
          <el-button class="buttonStyle" type="primary" style=" background-color:#31C97E;" size="medium"
                     @click="edit">修改
          </el-button>
          <el-button class="buttonStyle" type="primary" style=" background-color: #FD5252;" size="medium"
                     @click="deletemenu">删除
          </el-button>
        </div>
        <el-table :data="tableData" height="calc(100vh - 190px)" style="width: 100%;margin-bottom: 20px;" class="tableT" ref="singleTable"
          :expand-row-keys="expends" row-key="positionId" highlight-current-row @expand-change="changeicon"
          @current-change="handleCurrentChange">
          <el-table-column label="所属组织/岗位名称">
            <template slot-scope="prop">
              <span v-show="prop.row.children">
                <img src="../../assets/images/sino.png" alt=""
                  style="width: 23px; position: relative; top: 5px; left: 2px">
              </span>
              <span v-show="prop.row.ifchild" class="tree_img tree_icon"></span>
              <span v-if="prop.row.orgName" style="margin-left: 10px">{{ prop.row.orgName }}</span>
              <span v-if="prop.row.positionName">
                <img src="../../assets/images/card.png" alt=""
                  style="width: 16px; position: relative; top: 4px; left: 2px">
              </span>
              <span v-if="prop.row.positionName" style="margin-left: 10px">{{ prop.row.positionName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="posTypeName" label="岗位类型">
          </el-table-column>
          <el-table-column prop="positionDesc" label="岗位描述">
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>


    <el-dialog title="新增岗位" :visible.sync="adddialogVisible" width="560px" :show-close="false" class="addP">
      <div style="text-align: left">
        <el-form label-position="left" label-width="100px" :rules="addrule" ref="addform" :model="addform">
          <el-form-item label="岗位名称" prop="positionName">
            <el-input placeholder="请输入岗位名称" style="width: 274px" v-model="addform.positionName" maxlength="20"
              show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="拥有角色" prop="roleIds">
            <el-select v-model="addform.roleIds" filterable multiple placeholder="请选择角色" style="width: 274px">
              <el-option v-for="item in rolelist" :key="item.roleId" :label="item.roleName" :value="item.roleId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="岗位类型" prop="posType">
            <el-select v-model.trim="addform.posType" style="width :274px">
              <el-option v-for="(item, index) in postypelist" :key="index" :value="item.posType"
                :label="item.posTypeName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="岗位描述" prop="positionDesc">
            <el-input placeholder="请输入岗位描述" type="textarea" style="width: 274px" v-model="addform.positionDesc"
              :autosize="{ minRows: 4 }" maxlength="100" show-word-limit></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" style="text-align:right;margin-right:15%">
        <el-button class="OKButton" :disabled="adddis" @click="resumesend('addform')">确定</el-button>
        <el-button @click="addmenuClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="修改岗位" :visible.sync="editdialogVisible" width="560px" :show-close="false" class="addP">
      <div style="text-align: left">
        <el-form label-position="left" label-width="100px" :rules="editrule" ref="editform" :model="editform">
          <el-form-item label="岗位名称" prop="positionName">
            <el-input placeholder="请输入岗位名称" style="width: 274px" v-model="editform.positionName" maxlength="20"
              show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="拥有角色" prop="roleIds">
            <el-select v-model="editform.roleIds" filterable multiple style="width:274px" placeholder="请选择角色">
              <el-option v-for="item in rolelist" :key="item.roleId" :label="item.roleName" :value="item.roleId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="岗位类型" prop="posType">
            <el-select v-model.trim="editform.posType" style="width :274px">
              <el-option v-for="(item, index) in postypelist" :key="index" :value="item.posType"
                :label="item.posTypeName">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="岗位描述" prop="positionDesc">
            <el-input placeholder="请输入岗位描述" type="textarea" style="width: 274px" v-model="editform.positionDesc"
              :autosize="{ minRows: 4 }" maxlength="100" show-word-limit></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" style="text-align:right;margin-right:15%">
        <el-button class="OKButton" :disabled="editdis" @click="editmunusend('editform')">确定</el-button>
        <el-button @click="editmenuClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="删除" :visible.sync="deletedialogVisible" width="30%" :show-close="false" class="deletemenu">
      <div style="text-align: center">
        <i class="el-icon-warning" style="font-size: 14px;color: #fcb543;"><span
            style="font-size: 16px;color: #333;margin-left: 12px;">确定删除该岗位，以及岗位绑定的角色和员工关联关系吗？</span></i>
      </div>
      <span slot="footer" class="dialog-footer" style="text-align:right;">
        <el-button class="OKButton" :disabled="deldis" @click="deletesend">确 定</el-button>
        <el-button @click="deleteClose" class="CancButton" style="margin-left: 10px;">取 消</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { getPositionType, getListTwo, positionAdd, positionUpdate, positionDetail, positionDel } from "@/api/sino/position";
import { listRoles } from "@/api/sino/roleUrl";
export default {
  name: "postmanage",
  data() {
    return {
      currentRow: null,
      expends: [],
      tableData: [],
      adddialogVisible: false,
      systemlist: [],
      addform: {
        positionName: '',
        roleIds: [],
        posType: '',
        positionId: '',
        positionDesc: '',
        orgId: ""
      },
      addrule: {
        'positionName': [
          { required: true, message: '请输入岗位名称', trigger: 'blur' }
        ],
        'posType': [
          { required: true, message: '请选择岗位类型', trigger: 'change' }
        ]
      },
      adddis: false,
      showmoreinfo: false,
      editdialogVisible: false,
      editform: {
        positionName: '',
        roleIds: [],
        posType: '',
        positionId: '',
        positionDesc: ''
      },
      editrule: {
        'positionName': [
          { required: true, message: '请输入岗位名称', trigger: 'blur' }
        ],
        'posType': [
          { required: true, message: '请选择岗位类型', trigger: 'change' }
        ]
      },
      editdis: false,
      deldis: false,
      deletedialogVisible: false,
      rolelist: [],//所有角色数据
      postypelist: [],
    }
  },
  created() {
    this.getTreeData()
    getPositionType().then(res => {
      if (res.code == 200) {
        this.postypelist = res.data;
      }
    })
    listRoles().then(res => {
      this.rolelist = res.data;
    })
  },
  methods: {
    getTreeData() {
      getListTwo().then(res => {
        if (res.code == 200) {
          this.tableData = res.data;
          this.expends.push(res.data[0].positionId)
        }
      })
    },
    changeicon(row) {
      row.ifexpand = !row.ifexpand;
    },
    handleCurrentChange(val) {
      this.currentRow = val;
    },
    addmenuClose() {
      this.adddialogVisible = false;
      this.showmoreinfo = false;
      this.adddis = false;
      this.$refs.addform.resetFields()
    },
    resumesend(formname) {
      this.$refs[formname].validate((valid) => {
        if (valid) {
          this.adddis = true;
          // this.addform.orgId = this.currentRow.orgId;
          positionAdd(this.addform)
            .then(res => {
              if (res.code == 200) {
                this.adddis = false;
                this.adddialogVisible = false;
                this.getTreeData();
                this.$refs.addform.resetFields();
                document.documentElement.style.overflow = "auto";
                this.$message({
                  message: "新增成功",
                  type: "success",
                });
              } else {
                this.$message({
                  message: "新增失败",
                  type: "error",
                });
              }
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    addmenu() {
      if (this.currentRow && this.currentRow.isPosition == "0") {
        this.addform.orgId = this.currentRow.orgId;
        this.adddialogVisible = true;
        this.$nextTick(() => {
          this.$refs.addform.resetFields()
        })
        this.adddis = false;
        this.showmoreinfo = false;
      } else {
        this.$message.warning({
          message: '请选择表格中的组织数据'
        });
      }
    },
    showmore() {
      this.showmoreinfo = true;
    },
    editmunusend(formname) {
      this.$refs[formname].validate((valid) => {
        if (valid) {
          this.editdis = true;
          positionUpdate(this.editform)
            .then(res => {
              if (res.code == 200) {
                this.editdis = false;
                this.editdialogVisible = false;
                this.getTreeData();//获取组织数据
                this.$refs.editform.resetFields();
                document.documentElement.style.overflow = "auto";
                this.$message({
                  message: "修改成功",
                  type: "success",
                });
              } else {
                this.$message({
                  message: "修改失败",
                  type: "error",
                });
              }
            });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    editmenuClose() {
      this.editdialogVisible = false;
      this.showmoreinfo = false;
      this.editdis = false;
      this.$refs.editform.resetFields()
    },

    edit() {
      if (this.currentRow && this.currentRow.isPosition == "1") {
        positionDetail(this.currentRow.positionId).then(res => {
          if (res.code == 200) {
            this.editform = res.data
            // var rolelist = res.data.roleList
            this.editform.roleIds = res.data.roleIds;
          }
        })
        this.editdialogVisible = true;
        this.$nextTick(() => {
          this.$refs.editform.resetFields()
        })
        this.editdis = false;
        this.showmoreinfo = false;
      } else {
        this.$message.warning({
          message: '请选择表格中的岗位数据'
        });
      }
    },
    deletemenu() {
      if (this.currentRow && this.currentRow.isPosition == "1") {
        this.deletedialogVisible = true;
      } else {
        this.$message.warning({
          message: '请选择表格中的岗位数据'
        });
      }
    },
    //删除岗位
    deletesend() {
      this.deletedialogVisible = false;
      this.deldis = false;
      positionDel(this.currentRow.positionId).then(res => {
        if (res.code == 200) {
          this.deletedialogVisible = false;
          this.deldis = false;
          this.getTreeData()
          this.$message({
            message: '删除成功！',
            type: 'success'
          });
          this.currentRow = null;
          this.$refs.singleTable&&this.$refs.singleTable.setCurrentRow();//取消选中
          document.documentElement.style.overflow = 'auto';
        } else {
          this.$message.error({
            message: '删除失败！'
          });
        }
      })
    },
    deleteClose() {
      this.deletedialogVisible = false;
      this.deldis = false;
    }
  }
}
</script>

<style scoped lang="less">
.tab {
  background-color: #21274D;
  margin: -20px 0;

  /deep/ .el-table__expand-icon {
    background: url('../../assets/images/jia1.png') 100% 100%;
    background-size: cover !important;
  }

  /deep/ .el-table__expand-icon--expanded {
    -webkit-transform: rotate(0deg) !important;
    transform: rotate(0deg) !important;
    background: url('../../assets/images/jian1.png') 100% 100% !important;
    background-size: cover !important;
  }

  /deep/ .el-table__expand-icon .el-icon-arrow-right:before {
    display: none;
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #21274D;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}
.title {
  height: 66px;
  line-height: 66px;
  /* border-bottom: 1px solid #eee; */
  /*margin-bottom: 10px;*/
}
el-table__expand-icon el-table__expand-icon--expanded .title {
  height: 66px;
  line-height: 66px;
  /* border-bottom: 1px solid #eee; */
  margin-bottom: 10px;
}

</style>
