<template>
  <div class="outDiv">
    <div class="titleHeader">待办事项</div>
    <div class="systems" style="margin-left: 15px">
      <p class="every-system" v-for="(item, index) in system" :class="{ activeName: activeIndex == index }"
         :key="index" @click="systemClick(item, index)">{{ item }}</p>
    </div>
    <div class="eventBg">
            <span class="kinds-of-events" v-for="(item, index) in options" :key="item.id"
                  :class="{ active: indexActive == index }" @click="optionsClick(item.label, index,item.category)">{{
                item.label
              }}</span>
    </div>

    <el-row style="margin-top: 15px; background-color: #21274d; padding-bottom: 10px">
      <el-table :data="tableData" v-if="!isQuick" v-loading="loading" element-loading-background="rgb(33,39,77, 0.8)"
                highlight-current-row style="width: 98%"
                class="tableT tableT1" height='calc(100vh - 290px)' @row-click="newPage" :cell-style="cellStyle">
        <el-table-column label="待办名称" min-width="200">
          <template slot-scope="props">
                        <span class="titleStyle">{{
                            props.row.title ? props.row.title : props.row.procInstTitle
                          }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="appToDoType" label="操作系统">
        </el-table-column>
        <el-table-column prop="taskName"  label="审批节点">
        </el-table-column>
        <el-table-column label="发起时间">
          <template slot-scope="props">
            <span style="margin-left:10px">{{ props.row.createTime | converTime("YYYY-MM-DD HH:mm") }}</span>
          </template>
        </el-table-column>

      </el-table>
      <el-pagination v-if="!isQuick" style="margin-top: 10px;text-align: right;" @size-change="handleSizeChange"
                     @current-change="handleCurrentChange" @prev-click="prevClick" @next-click="nextClick"
                     :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="currentPageSize"
                     layout="total, sizes, prev, pager, next,jumper" :total="totalPage">
      </el-pagination>
      <el-table :data="quickSysData" v-if="isQuick" v-loading="quickLoading"
                element-loading-background="rgb(33,39,77, 0.8)" highlight-current-row
                style="width: 98%" class="tableT tableT1" height='calc(100vh - 290px)' @row-click="quickSysClick"
                :cell-style="cellStyle">
        <el-table-column label="报销名称" width="380">
          <template slot-scope="props">
            <span class='reimNameStyle'>{{ props.row.reimName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="报销类型">
          <template slot-scope="props">
            <span>{{ props.row.reimbursementType == "rc" ? "日常" : "差旅" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="上级审批时间">
          <template slot-scope="props">
                        <span>{{
                            props.row.arriveTime | converTime("YYYY-MM-DD HH:mm")
                          }}</span>
          </template>
        </el-table-column>
        <el-table-column label="提交时间">
          <template slot-scope="props">
                        <span>{{
                            props.row.createTime | converTime("YYYY-MM-DD HH:mm")
                          }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-if="isQuick" style="margin-top: 10px;text-align: right;"
                     @size-change="handleQuickSizeChange" @current-change="handleQuickCurrentChange"
                     @prev-click="prevQuickClick" @next-click="nextQuickClick" :current-page="currentQuickPage"
                     :page-sizes="[5, 10, 15]" :page-size="currentQuickPageSize"
                     layout="total, sizes, prev, pager, next,jumper" :total="totalQuickPage">
      </el-pagination>
    </el-row>
  </div>
</template>
<script>
import {getWechatUserInfo, todoList, todoListQUICK, loginToken,getContractId} from "@/api/sino/sino";
import {createToken} from "@/api/login/login";
import {eossUrl, pmsUrl, ntdhUrl, quickUrl, ntdhccUrl} from "../../utils/const";
import lodash from "lodash";

export default {
  name: "waiting",
  data() {
    return {
      activeIndex: 0,
      indexActive: 0,
      value: "全部",
      checkContent: "",
      start: "",
      currentPage: 1,
      loading: true,
      quickLoading: true,
      currentPageSize: 15,
      totalPage: 0,
      currentQuickPage: 1,
      currentQuickPageSize: 15,
      totalQuickPage: 0,
      currentSys: "EOSS",
      system: ["EOSS", "QUICK", "NTDH"],
      // system: ["EOSS", "PMS", "EHR", "QUICK", "NTDH"],
      isQuick: false,
      flag: false,
      options: [
        {
          id: 1,
          label: "全部",
          value: "",
          category:''
        },
        {
          id: 2,
          label: "合同审批",
          value: "CONTRACT",
          category:'合同'
        },
        {
          id: 3,
          label: "合同变更",
          category:''
        },
        {
          id: 4,
          label: "合同发票",
          category:'合同发票'
        },
        {
          id: 5,
          label: "合同盖章",
          category:'合同盖章'
        },
        {
          id: 6,
          label: "订单审批",
          value: "ORDER",
          category:'订单审批'
        },
        {
          id: 7,
          label: "内部采购",
          category:'内部采购'
        },
        {
          id: 8,
          label: "付款审批",
          value: "ORDER_PAYMENT",
          category:'订单付款'
        },
        {
          id: 9,
          label: "发票审批",
          value: "ORDER_REIM_APPLY",
          category:'订单发票'
        },
        {
          id: 10,
          label: "项目",
          category:'项目'
        },
        {
          id: 11,
          label: "人力",
          value: "HUMAN_RESOURCE",
          category:'人力'
        },
        {
          id: 12,
          label: "财务借款",
          category:''
        },
        {
          id: 14,
          label: "投标审批",
          value: "BID",
          category:'投标'
        },
      ],
      tableData: [],
      quickSysData: [],
      payName:'',
      code:'',
    };
  },
  computed: {},
  created() {
    this.userName = window.localStorage.getItem("Susername");//获取登录名
    //待办事项的数据
    this.getData(
        this.currentSys,
        this.currentPage,
        this.currentPageSize,
        this.userName
    );
  },
  mounted() {
    // let that = this;
    // //监听visibilitychange的变化，
    // window.addEventListener("visibilitychange", function () {
    //     if (!document.hidden) {
    //         if (that.currentSys == "QUICK") {
    //             that.quickLoading = true;
    //             that.quickData(
    //                 that.currentQuickPage,
    //                 that.currentQuickPageSize,
    //                 that.userName,
    //                 that.value,
    //                 that.checkContent,
    //                 that.start
    //             );
    //         } else {
    //             that.loading = true;
    //             that.getData(
    //                 that.currentSys,
    //                 that.currentPage,
    //                 that.currentPageSize,
    //                 that.userName,
    //                 that.value,
    //                 that.checkContent,
    //                 that.start
    //             );
    //         }
    //     }
    // });
  },
  methods: {
    //搜索防抖
    getData: function (sys, pageNo, pageSize, username, value, checkContent, start) {
      value = value || "";
      checkContent = checkContent || "";
      start = start || "";
      if (value === "全部") {
        value = "";
      }
      if (sys == 'EHR') {
        sys = 'EOSS';
        value = '人力'
      }
      todoList(sys, {
        pageNo,
        pageSize,
        username,
        processName: value,
        title: checkContent,
        creator: start,
      }).then((res) => {
        if (res.code == 200) {
          if (res && res.data && res.data.list) {
            this.tableData = res.data.list;
          } else if (res && res.data && res.data.records) {
            this.tableData = res.data.records;
          } else {
            this.tableData = res && res.data && res.data.rows;
          }
          this.loading = false;
          this.totalPage = res && res.data && res.data.total;
        }
      });
    },
    quickData: function (pageNo, pageSize, username, value, checkContent, start) {
      value = value || "";
      checkContent = checkContent || "";
      start = start || "";
      if (value === "全部") {
        value = "";
      }
      todoListQUICK({
        pageNo,
        pageSize,
        username,
        processName: value,
        title: checkContent,
        creator: start,
      }).then((res) => {
        this.quickSysData = res.data.data;
        this.totalQuickPage = res.data.count;
        this.flag = res.data.flag;
        this.quickLoading = false;
      });
    },
    // 页码点击跳转
    prevClick(val) {
      this.loading = true;
      this.currentPage = val;
      this.getData(
          this.currentSys,
          this.currentPage,
          this.currentPageSize,
          this.userName
      );
    },
    nextClick(val) {
      this.loading = true;
      this.currentPage = val;
      this.getData(
          this.currentSys,
          this.currentPage,
          this.currentPageSize,
          this.userName
      );
    },
    handleSizeChange(val) {
      this.loading = true;
      this.currentPageSize = val;
      this.currentPage = 1
      this.getData(
          this.currentSys,
          this.currentPage,
          this.currentPageSize,
          this.userName
      );
    },
    handleCurrentChange(val) {
      this.loading = true;
      this.currentPage = val;
      this.getData(
          this.currentSys,
          this.currentPage,
          this.currentPageSize,
          this.userName
      );
    },
    prevQuickClick(val) {
      this.quickLoading = true;
      this.currentQuickPage = val;
      this.quickData(
          this.currentQuickPage,
          this.currentQuickPageSize,
          this.userName
      );
    },
    nextQuickClick(val) {
      this.quickLoading = true;
      this.currentQuickPage = val;
      this.quickData(
          this.currentQuickPage,
          this.currentQuickPageSize,
          this.userName
      );
    },
    handleQuickSizeChange(val) {
      this.quickLoading = true;
      this.currentQuickPageSize = val;
      this.currentQuickPage = 1;
      this.quickData(
          this.currentQuickPage,
          this.currentQuickPageSize,
          this.userName
      );
    },
    handleQuickCurrentChange(val) {
      this.quickLoading = true;
      this.currentQuickPage = val;
      this.quickData(
          this.currentQuickPage,
          this.currentQuickPageSize,
          this.userName
      );
    },

    search() {
      this.loading = true;
      this.quickLoading = true;
      if (this.currentSys == "QUICK") {
        this.quickData(
            this.currentQuickPage,
            this.currentQuickPageSize,
            this.userName,
            this.value,
            this.checkContent,
            this.start
        );
      } else {
        this.getData(
            this.currentSys,
            this.currentPage,
            this.currentPageSize,
            this.userName,
            this.value,
            this.checkContent,
            this.start
        );
      }
    },
    cellStyle(data) {
      if (data) {
        return 'cursor:pointer;'
      }
    },
    goEoss(row, token) {
      //投标/投标报备重新提交跳过添加
      if((row.processName == "投标报备" &&
          row.processKey == "biddingReport" && row.taskName == "重新提交")
          ||
          (row.category == "BID" &&  row.processName == "投标审批" && row.taskName == "重新提交")
      ){
        return
      }
      let openUrl = `?dbflag=1&dburl=${row.url}&username=${this.userName}&token=${token}`;
      let path = ''
      let isNeedContractId = false
      let query = {}
      if (row.formKey) {
        path = row.formKey;
        path = "/task" + path,
            query = {
              flowStep: row.flowStep,
              processId: row.processId,
              taskId: row.taskId,
              userName: row.taskName,
            }
            let goEossHr = ''
        goEossHr = row.eossUrl + path + openUrl
        if (query) {
          let queryStr = Object.keys(query).map((key) => {
            return key + "=" + query[key];
          }).join("&");
          goEossHr += "&" + queryStr;
        }
        window.open(goEossHr);
      }
      else {
        let test = row.title.indexOf("退票");
        const taskNameList = [
          "申请人", //合同审批流程  //0
          "重新提交", //1
          "合规性审批", //2
          "合规性审查", //3
          "销管部主管", //4
          "部门经理", //5
          "直属副总裁", //6
          "商务经理", //7
          "行业营销总监",
          "三级部门经理",
          "深圳分公司总经理",
          "深圳分公司总经理审批",
          "销售总监",
        ];

        if (row.category == "CONTRACT") {
          let taskState = taskNameList.indexOf(row.taskName);
          if (taskState >= 0) {
            if (taskState < 2) {
              isNeedContractId = true
            } else {
              path = "/task/check",
                  query = {
                    processId: row.processId,
                    taskId: row.taskId,
                    taskState: taskState,
                  }
            }
          } else {
            return;
          }
        }
        else if (row.category == "ORDER_PAYMENT") {
          let regex = /退款/g; // 创建一个正则表达式，匹配"退款"这个词
          let regex1 = /付款/g; // 创建一个正则表达式，匹配"退款"这个词
          let match = row.title.match(regex); // 使用match方法查找所有匹配项
          let match1 = row.title.match(regex1); // 使用match方法查找所有匹配项
          if (match) {
            this.payName = match[0];
          }
          if (match1) {
            this.payName = match1[0];
          }
          const startIndex = 0; // 开始索引
          const endIndex = row.title.indexOf("["); // 查找"["的位置作为结束索引
          this.code = row.title.substring(startIndex, endIndex);
          if (this.payName == "退款") {
            if (row.taskName == "重新提交") {
              path = "/payment/refundPaymentSubmit"
              query = {taskId: row.taskId, code: this.code}
            } else {
              //审批
              path = "/payment/refundApprovalPayment",
                  query = {
                    processId: row.processId,
                    taskId: row.taskId,
                    code: this.code,
                    taskName: row.taskName,
                  }
            }
          } else {
            if (row.taskName == "重新提交") {

              path = "/payment/detailPaymentSubmit"
              query = {taskId: row.taskId, code: this.code}

            } else {

              path = "/payment/approvalPayment"
              query = {
                processId: row.processId,
                taskId: row.taskId,
                code: this.code,
                taskName: row.taskName,
              }

            }
          }
        }
        else if (
            row.processKey == "bfp" &&
            row.taskName != "重新提交"
        ) {  //发票审批
          path = "/task/invoiceApproval"
          query = {
            taskId: row.taskId,
            processId: row.processId,
            test: test,
            type: 'approval',
            taskName: row.taskName,
          }
        }
        else if (row.processKey == "sfp" && row.taskName != "重新提交") {

          path = "/contractInvoice/InvoicedLogDetail"
          query = {
            processId: row.processId,
            taskId: row.taskId,
            type: 'approval',
          }

        }
        else if (row.processKey == "sfp" && row.taskName == "重新提交") {

          path = "/contractInvoice/invoceApply"
          query = {processId: row.processId}

        }
        else if (
            row.processName == "订单发票审批" &&
            row.taskName == "重新提交" &&
            test == -1
        ) {

          path = "/task/reReimbursement"
          query = {
            processId: row.processId,
            taskId: row.taskId,
          }

        }
        else if (
            row.processName == "订单发票审批" &&
            row.taskName == "重新提交" &&
            test != -1
        ) {

          path = "/task/invoiceRefund"
          query = {
            processId: row.processId,
            taskId: row.taskId,
          }

        }
        else if (
            row.processName == "订单审批" &&
            row.taskName != "重新提交"
        ) {

          path = "/task/order"
          query = {
            processId: row.processId,
            taskId: row.taskId,
            taskName: row.taskName,
          }

        }
        else if (
            row.processName == "订单审批" &&
            row.taskName == "重新提交"
        ) {

          path = "/task/resubmitOrder"
          query = {
            processId: row.processId,
            taskId: row.taskId
          }

        }
        else if( row.category == "BID" &&  row.processName == "投标审批" && row.taskName != "重新提交"
            ){
            path= "/task/bid",
            query= {
              processId: row.processId,
              taskId: row.taskId,
            }
        }
        else if (
            row.processName == "投标报备" &&
            row.processKey == "biddingReport" && row.taskName != "重新提交"
        ) {

          path = "/task/bidApprove"
          query = {
            processId: row.processId,
            taskId: row.taskId,
          }

        }
        else if (
            row.processName == "证书审批" &&
            row.processKey == "CERTSP"
        ) {

          path = "/task/hr/certificate"
          query = {
            processId: row.processId,
            taskId: row.taskId,
            flowStep: row.flowStep,
            userName: row.taskName,
          }

        }
        else if (
            row.processName == "证书审批" &&
            row.processKey == "CERTSP" &&
            row.taskName == "重新提交"
        ) {

          path = "/task/hr/certificateSubmit"
          query = {
            processId: row.processId,
            taskId: row.taskId,
            flowStep: row.flowStep,
            userName: row.taskName,
          }

        }
        let goEossUrl = '';
        if (isNeedContractId) {
          getContractId({
            processId: row.processId
          }).then(res=>{
            if(res.data){
              path= "/contract/edit",
                  query= {
                    id: res.data,
                    processId: row.processId,
                    taskId: row.taskId,
                  }
            }
            goEossUrl = row.eossUrl + path + openUrl
            if (query) {
              let queryStr = Object.keys(query).map((key) => {
                return key + "=" + query[key];
              }).join("&");
              goEossUrl += "&" + queryStr;
            }
              window.open(goEossUrl);
          })
        }
        else {
            goEossUrl = row.eossUrl + path + openUrl
            let queryStr = Object.keys(query).map((key) => {
              return key + "=" + query[key];
            }).join("&");
            goEossUrl += "&" + queryStr;
          window.open(goEossUrl);
        }
      }
    },
    //详情跳转
    newPage(row) {
      let cookie = document.cookie;
      let accessToken = "";
      if (cookie) {
        let arr = cookie.split("; ");
        arr.forEach((v) => {
          let arr2 = v.split("=");
          if (arr2[0] == "access_token") {
            accessToken = arr2[1];
          }
        });
      }
      let openUrl = "";
      if (row.appToDoType == "EOSS") {
        createToken({
          userName: this.userName,
          clientid: 'EOSS',
        }).then((result) => {
          this.goEoss(row,result.data.token)
          // openUrl = `${row.eossUrl}?dbflag=1&dburl=${row.url}&username=${this.userName}&token=${result.data.token}`;
        });
      } else if (row.appToDoType == "PMS") {
        createToken({
          userName: this.userName,
          clientid: 'PMS',
        }).then((result) => {
          openUrl = `${row.pmsUrl}?dbflag=1&dburl=${row.url}&username=${this.userName}&token=${result.data.token}&taskId=${row.taskId}`;
          window.open(openUrl);
        });
      } else if (row.appToDoType == "NTDH") {
        createToken({
          userName: this.userName,
          clientid: 'NTDH－EOSS',
        }).then((result) => {
          openUrl = `${ntdhUrl}?dbflag=1&dburl=${ntdhccUrl}?taskId=${row.taskId}%26procInstId=${row.procInstId}&username=${this.userName}&token=${result.data.token}`;
          window.open(openUrl);
        });
      }

    },
    systemClick(sys, ind) {
      this.indexActive = 0;
      switch (sys) {
        case "EOSS":
          this.options= [
            {
              id: 1,
              label: "全部",
              value: "",
              category:''
            },
            {
              id: 2,
              label: "合同审批",
              value: "CONTRACT",
              category:'合同'
            },
            {
              id: 3,
              label: "合同变更",
              category:''
            },
            {
              id: 4,
              label: "合同发票",
              category:'合同发票'
            },
            {
              id: 5,
              label: "合同盖章",
              category:'合同盖章'
            },
            {
              id: 6,
              label: "订单审批",
              value: "ORDER",
              category:'订单审批'
            },
            {
              id: 7,
              label: "内部采购",
              category:'内部采购'
            },
            {
              id: 8,
              label: "付款审批",
              value: "ORDER_PAYMENT",
              category:'订单付款'
            },
            {
              id: 9,
              label: "发票审批",
              value: "ORDER_REIM_APPLY",
              category:'订单发票'
            },
            {
              id: 10,
              label: "项目",
              category:'项目'
            },
            {
              id: 11,
              label: "人力",
              value: "HUMAN_RESOURCE",
              category:'人力'
            },
            {
              id: 12,
              label: "财务借款",
              category:''
            },
            {
              id: 14,
              label: "投标审批",
              value: "BID",
              category:'投标'
            },
          ]
          break;
        case "PMS":
          this.options = [
            {
              id: 1,
              label: "全部",
            },
            {
              id: 10,
              label: "项目",
            },
          ];
          break;
        case "EHR":
          this.options = [
            {
              id: 1,
              label: "全部",
            },
            {
              id: 13,
              label: "人力",
            },
          ];
          break;
        case "QUICK":
          this.options = [
            {
              id: 1,
              label: "全部",
            },
            {
              id: 11,
              label: "财务报销",
            },
          ];
          break;
        case "NTDH":
          this.options = [
            {
              id: 1,
              label: "全部",
            },
            {
              id: 2,
              label: "合同审批",
            },
            {
              id: 3,
              label: "合同变更",
            },
            {
              id: 4,
              label: "合同发票",
            },
            {
              id: 5,
              label: "合同盖章",
            },
            {
              id: 6,
              label: "订单审批",
            },
            {
              id: 7,
              label: "内部采购",
            },
            {
              id: 8,
              label: "付款审批",
            },
            {
              id: 9,
              label: "发票审批",
            },
            {
              id: 10,
              label: "项目",
            },
            {
              id: 11,
              label: "财务报销",
            },
            {
              id: 12,
              label: "财务借款",
            },
            {
              id: 13,
              label: "人力",
            },
            {
              id: 14,
              label: "投标审批",
            },
          ];
          break;
      }
      this.loading = true;
      this.quickLoading = true;
      this.activeIndex = ind;
      this.currentSys = sys;
      this.currentPage = 1;
      this.currentQuickPage = 1;
      if (sys === "QUICK") {
        this.quickData(this.currentPage, this.currentPageSize, this.userName);
        this.isQuick = true;
        getWechatUserInfo({
          params: {userId: this.userName},
        }).then((res) => {
          let obj = res.data;
          obj.flag = "reim";
          window.localStorage.removeItem("userInfo");
          window.localStorage.setItem("userInfo", JSON.stringify(obj.data));
        });
      } else {
        this.getData(
            this.currentSys,
            this.currentPage,
            this.currentPageSize,
            this.userName
        );
        this.isQuick = false;
      }
    },
    optionsClick(item, index,category) {
      this.indexActive = index;
      if(category && this.activeIndex === 0){
        this.value = category;
      }
      else this.value = item;
      this.currentPage = 1;
      this.currentQuickPage = 1;
      this.search();
    },
    //工单跳转
    quickSysClick(row) {
      createToken({
        userName: this.userName,
        clientid: 'RAMS',
      }).then((result) => {
        let url = `${row.quickUrl}?id=${row.reimId}&sid=${row.taskId}&processInstanceId=${row.processInstanceId}&userName=${this.userName}&flag=${this.flag}&token=${result.data.token}`;
        window.open(url);
      });
    },
  },
  watch: {},
};
</script>
<style scoped lang="less">
.outDiv {
  background-color: #21274D;
  margin: -20px 0;
  padding: 12px;
  height: calc(100vh - 110px);

  /deep/ .el-table thead {
    /*display: none;*/
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  /* Track */

  ::-webkit-scrollbar-track {
    background: #21274D;
  }

  /* Handle */

  ::-webkit-scrollbar-thumb {
    background: #888;
  }

  /* Handle on hover */

  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
}

.activeName {
  color: rgba(0, 128, 190, 1) !important;
  /*border: 0px !important;*/
  background-color: #21274D !important;
  border-bottom: 2px solid rgba(0, 128, 190, 1);
}

.active {
  //border-radius: 47px !important;
  background-color: rgba(0, 128, 190, 1) !important;
  /*color: rgba(16, 16, 16, 1) !important;*/
}

.every-system {
  display: inline-block;
  margin-right: 26px;
  font-size: 15px;
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background-color: #21274D;
  color: #D3D3D3;
  margin-bottom: 14px;
  cursor: pointer;
  /*border-radius: 4px;*/
}

.kinds-of-events {
  display: inline-block;
  margin-right: 14px;
  width: 72px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  background-color: rgba(27, 51, 95, 1);
  color: #D3D3D3;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
}

.titleHeader {
  height: 25px;
  line-height: 25px;
  font-size: 18px;
  color: #c3c9ff;
  margin-left: 15px;
  margin-bottom: 10px;
  padding-left: 15px;
  border-left: 5px solid #c3c9ff;
}

.titleStyle {
  display: block;
  width: 100%;
  cursor: pointer;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.eventBg {
  height: 47px;
  line-height: 47px;
  border-radius: 8px 8px 8px 8px;
  background-color: rgba(27, 51, 95, 1);
  text-align: center;
  display: flex;
  align-items: center;
  padding: 0 12px;
  margin: 0 15px;
}

.reimNameStyle {
  display: block;
  width: 100%;
  cursor: pointer;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* 13寸 */
@media screen and (min-width: 600px) and (max-width: 1300px) {
  .kinds-of-events {
    font-size: 12px;
  }

  .every-system {
    font-size: 12px;
  }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
  .tableT {
    margin-bottom: 20px;
  }
}
</style>
