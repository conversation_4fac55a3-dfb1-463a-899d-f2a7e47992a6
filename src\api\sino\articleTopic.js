import request from "@/utils/request";

/**
 *
 *列表 查询
 * **/
export const getRecordList = (param) => {
  return new Promise((resolve) => {
    request
      .get(
        "/articleTopic/getViewLog?pageSize=" +
          param.pageSize +
          "&pageNum=" +
          param.pageNum +
          "&articleTopicId=" +
          param.articleTopicId +
          "&type=" +
          param.type +
          "&name=" +
          param.name
      )
      .then((res) => {
        resolve(res);
      });
  });
};
export const attachPassStatistics = (param) => {
  return new Promise((resolve) => {
    request
      .get(
        "/articleTopic/attachPassStatistics?pageSize=" +
          param.pageSize +
          "&pageNum=" +
          param.pageNum +
          "&title=" +
          param.title +
          "&readFlag=" +
          param.readFlag +
          "&search=" +
          param.search +
          "&type=" +
          param.type
      )
      .then((res) => {
        resolve(res);
      });
  });
};

//获取详情
export const getPopUpArticle = () => {
  return new Promise((resolve) => {
    request.get("/articleTopic/getPopUpArticle").then((res) => {
      resolve(res);
    });
  });
};
export const getAllAttachPassTitle = (type) => {
  return new Promise((resolve) => {
    request.get("/articleTopic/getAllAttachPassTitle?type="+ type).then((res) => {
      resolve(res);
    });
  });
};
//导出

export const exportArticle = (param) => {
  return new Promise((resolve) => {
    request
      .get("/articleTopic/export?articleTopicId=" +param.articleTopicId +"&type=" +param.type +"&name=" +param.name)
      .then((res) => {
        resolve(res);
      });
  });
};
export const downloadArticle = (param) => {
  return new Promise((resolve) => {
    request
      .get("/common/download?fileName=" +param.fileName+"&delete=true")
      .then((res) => {
        resolve(res);
      });
  });
};