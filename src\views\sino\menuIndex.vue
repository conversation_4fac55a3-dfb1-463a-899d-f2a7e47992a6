<template>
  <div>
    <div class="container">
      <el-row>
        <el-col :span="24">
          <div class="mainBox">
            <div class="newsModule">
         
              <div class="homeBanner1" v-loading="menuLoading" element-loading-spinner="el-icon-loading"
                element-loading-text="拼命加载中" element-loading-background="rgb(33,39,77, 1)">
                <div class="Title">
                  <img style="
                  height: 1.125rem;
                  width: 4px;
                  margin-right: 8px;
                  margin-bottom: -2px;
                " src="@/assets/images/menuIndex/tixing.png" alt="" />应用导航
                </div>
                <el-carousel trigger="click" :autoplay="false">
                  <el-carousel-item class="BgCarousel" v-for="(list, index) in systems" :key="index">
                    <!--  -->
                    <div class="swiperBg" v-for="(item, index) in list" :key="index"
                      :style="{ background: getColor(item.referred) }">
                      <template v-if="item.num == '101'">
                        <div @click="goBladex(item)"
                          style="text-decoration:none;display: flex;align-items: center;width: 100%;height: 100%">

                          <img :src="baseImgUrl + item.icon" alt="" class="logo" style="margin-left: 0.5rem" />
                          <div class="sFont">
                            <div style="margin-bottom: 0.5rem; font-size: 1rem">
                              {{ item.referred }}
                            </div>
                            <div>{{ item.name }}</div>
                          </div>
                        </div>
                      </template>
                      <!-- <a :href="item.url" target="_blank" v-else -->
                      <!-- <a :href="item.url" target="_blank"
                        style="text-decoration:none;display: flex;align-items: center;width: 100%;height: 100%">
                        <img :src="item.icon" alt="" class="logo" style="margin-left: 0.5rem" />
                        <div class="sFont">
                          <div style="margin-bottom: 0.5rem; font-size: 1rem">
                            {{ item.referred }}
                          </div>
                          <div>{{ item.name }}</div>
                        </div>
                      </a> -->

                      <div v-else
                        style="text-decoration:none;display: flex;align-items: center;width: 100%;height: 100%"
                        @click="sysClick(index, item.url, item.referred)">
                        <img :src="baseImgUrl + item.icon" alt="" class="logo" style="margin-left: 0.5rem" />
                        <div class="sFont">
                          <div style="margin-bottom: 0.5rem; font-size: 1rem;">
                            {{ item.referred }}
                          </div>
                          <div>{{ item.name }}</div>
                        </div>
                      </div>
                      <!-- <el-button 
                      v-else
                       @click="sysClick(index,item.url,item.referred)"><img :src="item.icon" alt="" class="logo" style="margin-left: 0.5rem" />
                        <div class="sFont">
                          <div style="margin-bottom: 0.5rem; font-size: 1rem;color:#000;">
                            {{ item.referred }}
                          </div>
                          <div style="color:#000;">{{ item.name }}</div>
                      </div></el-button> -->
                    </div>
                  </el-carousel-item>
                </el-carousel>
              </div>
              <div class="homeBanner1" element-loading-background="rgb(33,39,77, 1)">
                <div class="newsTop">
                  <div class="Title1">
                    <img style="
                      height: 1.125rem;
                      width: 4px;
                      margin-right: 8px;
                      margin-bottom: -2px;
                    " src="@/assets/images/menuIndex/tixing.png" alt="" />通知公示
                  </div>
                  <div class="more" @click="goMoreNews('通知公示')">
                    更多<i class="el-icon-d-arrow-right"></i>
                  </div>
                </div>

                <div v-for="item in tableData" :key="item.id" class="newsTitleSon" @click="handleDetail(item)">
                  <div class="ellipsis">{{ item.title }}</div>
                  <div style="font-size: 0.8rem">{{ formatDate(item.CreateTime) }}</div>
                </div>
              </div>
            </div>
            <div class="newsModule1">
              <div class="newsCard">
                <div class="newsTop">
                  <div class="Title1">
                    <img style="
                      height: 1.125rem;
                      width: 4px;
                      margin-right: 8px;
                      margin-bottom: -2px;
                    " src="@/assets/images/menuIndex/tixing.png" alt="" />规章制度
                  </div>
                  <!-- <div class="more" @click="goMoreNews('规章制度')">
                    更多<i class="el-icon-d-arrow-right"></i>
                  </div> -->
                </div>
                <div class="newsTitleBox" v-loading="loading" element-loading-spinner="el-icon-loading"
                  element-loading-text="拼命加载中" element-loading-background="rgb(33,39,77, 1)">
                  <div v-for="item in systemList" :key="item.id" class="newsTitleSon" @click="openNews(item)">
                    <div class="ellipsis" style="display: flex;align-items: center;">
                      <span style="width:6px;height:6px;display: inline-block;background-color: #fff;border-radius: 50%;margin-right: 6px;"></span>
                      <span>{{ item.dmvaluemeaning }}</span> 
                      <!-- <span v-if="item.isNew"><i
                          class="iconfont icon-xiaoxizhongxin_zuixinxiaoxi"
                          style="color:#EE502F;font-size: 1.25rem"></i></span> -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="newsCard1">
                <div class="newsTop">
                  <div class="Title1">
                    <img style="
                      height: 1.125rem;
                      width: 4px;
                      margin-right: 8px;
                      margin-bottom: -2px;
                    " src="@/assets/images/menuIndex/tixing.png" alt="" />日历   
                  </div>
                  <!--                <div class="more" @click="goMoreLog()">-->
                  <!--                  更多<i class="el-icon-d-arrow-right"></i>-->
                  <!--                </div>-->
                </div>
                <div class="Calendar">
                  <el-calendar ref="calendar" @current-view="handleCurrentView">

                  </el-calendar>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <!-- 新闻标题的弹窗 -->
      <div class="tipStyle">
        <el-dialog title="查看规章制度" :visible.sync="dialogTipVisible" width="60%" class="addpup"
          :before-close="handleTipClose">
          <ul class="news-list">
            <li v-for="item in newsTitleArr" :key="item.id"  class="news-item">
              <div class="news-content">
                
                <div class="news-title"> {{ item.Title }}</div>
             
              </div>
              <div class="news-arrow" @click="openNewsDetail(item)">
                <i class="el-icon-arrow-right">查看详情</i>
              </div>
            </li>
          </ul>
      
          <span slot="footer" style="display: flex; justify-content: center">
            <el-button @click="handleTipClose()" class="cancelbtn" size="medium">关闭</el-button>
          </span>
        </el-dialog>
      </div>
      <el-dialog title="查看规章制度详情" :visible.sync="dialogTipVisibleDetail" width="80%" class="addpup"
          :before-close="handleTipCloseDetail">
    
          <div style="line-height: 2" class="dialogContent">
            <p>标题: {{ newsContent.Title }}</p>
           
            <EditorDteail id="recordContent" v-model="newsContent.contentText" :min-height="30" :showType="'table'" />
          </div>
          <span slot="footer" style="display: flex; justify-content: center">
            <el-button @click="dialogTipVisibleDetail= false" class="cancelbtn" size="medium">关闭</el-button>
          </span>
        </el-dialog>
      


      <el-dialog class="openNew" v-for="(dialog, index) in dialogs" :key="index" :title="dialog.Title" width="60%"
        :visible.sync="dialog.visible" :close-on-press-escape="false" :show-close="false" :close-on-click-modal="false"
        top="5vh">
        <div class="scroll-container" ref="modal" @scroll="handleScroll">

          <iframe v-if="dialog.fileUrl" :src="dialog.fileUrl" frameborder="0"
            style="width: 100%; height: 100%"></iframe>
          <div v-else v-html="dialog.contentText"></div>
        </div>
        <el-row style="width: 100%;text-align: center;margin-top: 15px;">
          <!--        :disabled="countdown > 0||disBtn"-->
          <el-button :disabled="dialog.count > 0 || countdown > 0" @click="closeCountdown(dialog, index)"
            :class="countdown > 0 ? 'readBtn' : 'agreeBtn'">
            {{ countdown > 0 ? `请上滑阅读${countdown}秒` : '同 意' }}
          </el-button>
        </el-row>
      </el-dialog>
    </div>
    <!--详情弹框-->
    <el-dialog ref="detailDialog" title="通知公示详情" :visible.sync="detailDialogVisible" width="900px" class="addP"
      :close-on-click-modal="false" :before-close="detailmenuClose" :show-close="false">
      <div style="text-align: left">
        <el-form label-position="left" label-width="120px">
          <el-col :span="24">
            <el-form-item label="标题">
              <span>{{ detailData.title }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="内容">
              <div v-html="detailData.content"></div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件">
              <div v-for="(item, index) in detailData.attach" :key="index" class="attachment-item">
                <div>{{ item.attachName }}</div>
                <el-button type="text" size="small" @click="handleDownload(item)">浏览</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="已读状态">
              <el-tag :type="detailData.readFlag === '1' ? 'success' : 'warning'">
                {{ detailData.readFlag === '1' ? '已读' : '未读' }}
              </el-tag>
              <el-button v-if="detailData.readFlag !== '1'" type="text" size="small" style="margin-left: 10px"
                @click="handleUpdateRead">
                标记为已读
              </el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <div style="width: 100%;text-align: right;">
          <el-button @click="detailmenuClose" class="CancButton">关 闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAppList, getNews, addviewLog,
  getlistWorkDaily, getlistWorkDailyForDay, isFirstLogin,
  getDomainList, getProList, saveWorkDaily, editWorkDaily, delWorkDaily, getPopUpArticle, lastDeliverer
} from "@/api/app/app";
import { createToken } from "@/api/login/login";
import EditorDteail from '@/components/Editor/detail.vue';
import CourseSelect from '@/components/SelectPage/selecteWithSearch.vue';
import moment from 'moment';
import { mapActions } from 'vuex';
import {
  getAttachPassByUserId, getDetailByUserId, updateAttachPassRead, downLoadFile, articleTopicGetDictList
} from "@/api/sino/newRelease";

export default {
  name: "menuIndex",
  components: {
    EditorDteail,
    CourseSelect
  },
  data() {
    return {
      dialogTipVisibleDetail:false,
      detailData: {
        title: '',
        content: '',
        attach: [],
        userNames: ''
      },
      detailDialogVisible: false,
      baseImgUrl: process.env.VUE_APP_BASE_URL,
      calendarDate: new Date(),
      calendarData: [0],
      countdown: 0, // 倒计时的秒数
      count: 0,//点击当前按钮次数
      timer: null, // 计时器
      pdfUrl: null,
      value1: null,
      systems: [],
      systems1: [[{
        icon: "group1/M00/03/85/wKiwCWRaJ3qATT9TAAAvMi4PeTQ513.png",
        id: 2305051503443122,
        name: "EOSS",
        num: "90",
        referred: "DM",
        url: "http://localhost"
      },
      {
        icon: "group1/M00/03/85/wKiwCWRaJ3qATT9TAAAvMi4PeTQ513.png",
        id: 2305051503443122,
        name: "报销",
        num: "90",
        referred: "DM",
        url: "http://localhost:9525"
      }
      ]
      ],
      dayTime: [],
      logRecords: [],
      textarea: "",
      loginName: "",
      avatar: "",
      userName: "",
      LoginName: "",
      positionName: "",
      userOrg: "",
      loading: true,
      menuLoading: true,
      logloading: false,
      disBtn: false,
      disDel: false,
      chooseVisible: false, //选择系统
      dialogNewVisible: false, //选择系统
      teachUrl: "", //外访讲师登录
      studUrl: "", //普通学员登录
      calendarValue: new Date(), //当前日期
      currentMonth: new Date(), //当前月份
      dialogTipVisible: false, // 新闻弹窗
      dialogTipVisibleLog: false, //日志弹窗
      dialogDetVisibleLog: false, //日志详情弹窗
      logTitle: "添加工作日志", //日志弹窗
      isDetail: false, //是否有详情
      adddis: false, //新增按钮
      isMore: false, //是否点开多次上传
      dateFormat: "HH:mm",
      dateTimeStart: "",
      dateTimeEnd: "",
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      deliverer: [],
      deliverer1: [],
      delivererList: [],
      delivererList1: [],
      ruleForm: {
        diarydate: "",
        starttime: "",
        endtime: "",
        workplace: "",
        manhoure: "",
        worktype: "",
        diarytype: "",
        projectid: "",
        projectname: "",
        projectcode: "",
        state: "20",
        content: "",
        userName: '',
        deliverer: ''
      },
      
      editForm: {
        diarydate: "",
        starttime: "",
        endtime: "",
        workplace: "",
        manhoure: "",
        worktype: "",
        diarytype: "",
        projectid: "",
        projectname: "",
        projectcode: "",
        state: "20",
        content: "",
        id: "",
        userName: '',
        deliverer: ''
      },
      rules: {
        starttime: [
          { required: true, message: "请选择开始时间", trigger: "change" },
        ],
        endtime: [
          { required: true, message: "请选择结束时间", trigger: "change" },
        ],
        workplace: [
          { required: true, message: "请填写工作地点", trigger: "blur" },
        ],
        worktype: [
          { required: true, message: "请选择工作类型", trigger: "change" },
        ],
        diarytype: [
          { required: true, message: "请选择日志类型", trigger: "change" },
        ],
        projectid: [{ required: true, message: "请选择项目名称", trigger: "change" }],
        projectcode: [{ required: true, message: "请选择项目阶段", trigger: "change" }],
        state: [{ required: true, message: "请选择工作进度", trigger: "change" }],
        content: [
          { required: true, message: "请输入工作内容", trigger: "change" },
        ],
      },
      showModal: false,
      buttonEnabled: false,
      scrollPosition: 0,
      listWorkType: [
        {
          dmvalue: "1000",
          dmvaluemeaning: "日常工作"
        },
        {
          dmvalue: "2000",
          dmvaluemeaning: "项目工作"
        },
      ],//工作类型
      systemList: [],
      listDailyType: [],//日志类型
      listProjectName: [],//项目名称
      projectNameList: [],//日志送达人
      listProjectStage: [],//项目阶段
      options: [
        {
          value: 10,
          label: "未完成",
        },
        {
          value: 20,
          label: "已完成",
        },
      ],
      newsTitleArr: [],
      dialogs: [],

      newsContent: {},
      newTitleS: null, // 弹窗的新闻标题
      newTimeS: null, // 弹窗的新闻时间
      curIndex: 0, //默认显示的索引
      swiperItems: [
        {
          id: 0,
          sm: require("@/assets/images/menuIndex/banner11.png"),
          lg: require("@/assets/images/menuIndex/banner11.png"),
        },
        {
          id: 1,
          sm: require("@/assets/images/menuIndex/banner22.png"),
          lg: require("@/assets/images/menuIndex/banner22.png"),
        },
        // {
        //   id: 2,
        //   sm: require("@/assets/images/menuIndex/banner33.jpg"),
        //   lg: require("@/assets/images/menuIndex/banner33.jpg"),
        // },
        // {
        //   id: 3,
        //   sm: require("@/assets/images/menuIndex/banner44.jpg"),
        //   lg: require("@/assets/images/menuIndex/banner44.jpg"),
        // },
        // {
        //   id: 4,
        //   sm: require("@/assets/images/menuIndex/banner55.jpg"),
        //   lg: require("@/assets/images/menuIndex/banner55.jpg"),
        // },
      ],
      tipContent: "上线通知：新EOSS\"合同变更\"以及\"工作日志\"功能已上线",
      userid: '',
      code: '',
      tableData: []
    };
  },
  created() {
    // alert('menuIndex')
    this.userName = window.localStorage.getItem("Susername");
    this.avatar = window.localStorage.getItem("Avatar");
    this.LoginName = window.localStorage.getItem("LoginName");
    this.positionName = window.localStorage.getItem("PositionName");
    this.userOrg = window.localStorage.getItem("UserOrg");
    if (this.userName) {

      setTimeout(() => {

        this.getSystem(this.userName);
        this.getRegulations()
        this.getWorkDailylist();
        this.getDomList();
        // this.authBladex();
      }, 500);
    }
  },
  watch: {
    calendarDate(val, oldVal) {

      if (
        val &&
        moment(val).toDate() < moment(oldVal).startOf("month").toDate()
      ) {
        this.currentMonth = val;
        this.getWorkDailylist();
        console.log("点击了'上个月'按钮");
      } else if (
        val &&
        moment(val).toDate() > moment(oldVal).endOf("month").toDate()
      ) {
        this.currentMonth = val;
        this.getWorkDailylist();
        console.log("点击了'下个月'按钮");
      } else {
        this.isDetail = false;
        this.ruleForm.diarydate = val;
        this.editForm.diarydate = val;
        this.dayTime.map(item => {
          if (moment(item).format("YYYY-MM-DD") == moment(val).format("YYYY-MM-DD")) {
            this.isDetail = true;
          }
        })
        this.writeLog();
        this.getWorkDailylistForDay(val);
      }
    },
  },
  mounted() {
    window.addEventListener('keydown', this.handleKeyDown);
    this.getListDate()
    
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleKeyDown);
    this.clearTimer(); // 清除计时器，防止内存泄露
  },
  methods: {
    ...mapActions(['SET_NOTICE_NUM']),
    handleKeyDown(event) {
      console.log(event)
    },
    getRegulations() {
      articleTopicGetDictList("topicType").then((res) => {
        // this.getFirstArticlesByType(res.data)
        this.systemList = res.data;
        console.log('  this.systemList',  this.systemList)
      });
    },
    openNewsDetail(item) {
      this.dialogTipVisibleDetail = true;
     
      this.newsContent =    item ;
      this.statisticalReading(item)
    },
    getListDate() {
      this.loading = true;
      let param = {
        title: '',
        pageNum: 1,
        pageSize: 10000,
        userId: window.localStorage.getItem("Susername"),
        type: 'type_2'
      }
      getAttachPassByUserId(param).then(res => {

        this.tableData = res.data.subject.slice(0, 7);
        let len = res.data.subject.filter((item) => item.readFlag == "0");
        this.SET_NOTICE_NUM(len.length);

      })
    },
    changeWork() {
      if (this.editForm.worktype != 2000) {
        this.editForm.projectid = "";
        this.editForm.projectname = "";
        this.editForm.projectcode = "";
      }
      if (this.ruleForm.worktype != 2000) {
        this.ruleForm.projectid = "";
        this.ruleForm.projectname = "";
        this.ruleForm.projectcode = "";
      }
    },
    handleScroll(event) {
      // 这里处理滚动事件
      console.log(event.target.scrollTop); // 输出滚动位置
    },
    showPdf() {
      this.showModal = true;
      this.$nextTick(() => {
        for (let i = 0; i < this.dialogs.length; i++) {
          if (this.dialogs[i].visible) {
            this.curIndex = i;
          }
        }
        this.dialogs.map(item => {
          item.visible = true
          if (item.fileUrl.indexOf('group') === -1) {
            let temp = item.fileUrl
            item.fileUrl = temp
          } else {
            item.fileUrl = 'http://192.168.177.7:8888/' + item.fileUrl; //测试环境地址
          }
        })
      })
      // this.dialogNewVisible = true;
      // this.pdfUrl = 'group1/M00/03/88/wKiwCWRd-ZiADoOuABkM35LDfAc905.pdf' + "#scrollbars=0&toolbar=0&statusbar=0";
      this.startCountdown();
    },
    // 更新已读状态
    handleUpdateRead() {
      updateAttachPassRead(this.detailData.id, window.localStorage.getItem("Susername")).then(res => {
        if (res.code === 200) {
          this.$message.success('已更新为已读状态');
          this.detailData.readFlag = '1';
          // 刷新列表
          this.getListDate();
        } else {
          this.$message.error(res.message || '更新已读状态失败');
        }
      }).catch(() => {
        this.$message.error('更新已读状态失败');
      });
    },
    startCountdown() {
      if (this.timer) return;
      this.countdown = 3; // 倒计时30秒
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown -= 1;
        } else {
          this.clearTimer();
        }
      }, 1000);
    },
    closeCountdown(val, i) {
      this.disBtn = true;
      this.$nextTick(() => {
        this.dialogs[i].visible = false;
        this.dialogs[i].count = 1
        this.clearTimer();
        this.countdown = 3;
        this.startCountdown()
      })
      this.statisticalReading(val)
    },
    statisticalReading(val){
      console.log(val)
      addviewLog({
        userName: this.userName,
        name: this.LoginName,
        id: val.id,
      }).then((res) => {
        console.log(res)
      });
    },
    clearTimer() {
      clearInterval(this.timer);
      this.timer = null;
    },
    handleCurrentView(date) {
      const calendar = this.$refs.calendar;
      const currentViewDate = calendar.currentViewDate;

      // 通过比较日历视图的日期与当前日历视图的日期来判断是否有月份切换
      if (currentViewDate.getMonth() !== date.getMonth()) {
        // 月份切换了，执行你需要的操作
        console.log('月份切换了', currentViewDate, date);
      }
    },
    isWithinLastMonth(timestamp) {
      const oneMonthAgo = new Date().getTime() - (30 * 24 * 60 * 60 * 1000); // 计算一个月前的时间戳
      const givenDate = new Date(timestamp);
      return givenDate.getTime() >= oneMonthAgo;
    },
    chooseClose() {
      this.chooseVisible = false;
    },
    getNewsData(type = '') {
      this.newsTitleArr = [];
      getNews(type).then((res) => {
        if (res.code == 200) {
         
          this.newsTitleArr = res.data;
        }
      });
    },
    getFirstArticlesByType(data) {
      const validArticles = this.newsTitleArr.filter(article => 
        article.hasOwnProperty('topic_type') && article.topic_type
      );
      
      const firstArticlesObj = {};
      validArticles.forEach(article => {
        const typeId = article.topic_type;
        // 只存储每个类型的第一篇文章
        if (!firstArticlesObj[typeId]) {
          firstArticlesObj[typeId] = article;
        }
      });
      Object.keys(firstArticlesObj).forEach(typeId => {
        const article = firstArticlesObj[typeId];
        article.isNew = this.isWithinLastMonth(article.CreateTime);
      });
      this.systemList = data.map(type => {
        const firstArticle = firstArticlesObj[type.dmvalue] || null;
        return {
          ...type,
          firstArticle: firstArticle,
          isNew: firstArticle ? firstArticle.isNew : false
        };
      });
      // console.log( this.systemList,'systemListrticlesArray')
    },
    handleDownload(item) {
      const isPdf = item.attachName.toLowerCase().endsWith('.pdf');
      downLoadFile(item.filePath).then(res => {
        if (isPdf) {
          const blob = new Blob([res], { type: 'application/pdf' });
          const pdfUrl = window.URL.createObjectURL(blob);
          window.open(pdfUrl, '_blank'); // 新标签页打开
          // 可选：延迟释放 URL，避免 PDF 未加载完就被释放
          setTimeout(() => {
            window.URL.revokeObjectURL(pdfUrl);
          }, 10000); // 10秒后释放
        } else {
          const blob = new Blob([res], {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.setAttribute("download", item.attachName);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }
      }).catch(() => {
        this.$message.error('下载失败');
      });
    },
    getArticleData(user) {
      getPopUpArticle(user).then((res) => {
        if (res.code == 200) {
          this.dialogs = res.data;
          this.dialogs.map(item => {
            if (item.fileUrl) {
              item.fileUrl = item.fileUrl + '#scrollbars=0&toolbar=0&statusbar=0';
              item.visible = false;
            }
          })
          this.dialogs = this.dialogs.map(item => ({
            ...item,
            count: 0,
          }))

          this.firstLogin(user);
        }
      });
    },
    //时间格式化
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD');
    },
    //时间格式化
    formatMonth(dateStr) {
      return moment(dateStr).format('YYYY-MM');
    },
    getSystem(user) {
      getAppList(user).then((res) => {
        // res = {
        //     "code": 200,
        //     "message": "操作成功",
        //     "data": [
        //         {
        //             "num": "99",
        //             "name": "报销管理系统",
        //             "icon": "group1/M00/03/85/wKiwCWRZ4tiARzf_AABBCPHNpVY391.png",
        //             "id": 24,
        //             // "url": "https://scrm.sino-bridge.com:8098/oa/quik-reim2/#/",
        //             // "url": "http://localhost:9525/oa/quik-reim2/#/",
        //             // "url": "http://localhost:9525/",
        //             "url": "http://***************:9525/oa/quik-reim2/#/",
        //             "referred": "RMS"
        //         },
        //         {
        //             "num": "98",
        //             "name": "报销审批系统",
        //             "icon": "group1/M00/03/85/wKiwCWRZ4ueAU7msAAA-namy2MQ631.png",
        //             "id": 22,
        //             "url": "https://eossdemo.sino-bridge.com:8086/approval/#/",
        //             "referred": "RAMS"
        //         },
        //         {
        //             "num": "100",
        //             "name": "神州新桥EOSS",
        //             "icon": "group1/M00/03/8B/wKiwCWRmyYWAVWRRAAAsjnn4Ybg179.png",
        //             "id": 18,
        //             "url": "https://scrm.sino-bridge.com:8098/oa/eoss-server/#/",
        //             "referred": "EOSS"
        //         },
        //     ],
        //     "map": null
        // }
        this.menuLoading = false;
        if (res.code == 200) {
          this.systems = res.data && res.data.sort(this.compare("num"));
          let newDataList = [];
          let current = 0;
          if (this.systems && this.systems.length > 0) {
            for (let i = 0; i <= this.systems.length - 1; i++) {
              if (i % 8 !== 0 || i === 0) {
                if (!newDataList[current]) {
                  newDataList.push([this.systems[i]]);
                } else {
                  newDataList[current].push(this.systems[i]);
                }
              } else {
                current++;
                newDataList.push([this.systems[i]]);
              }
            }
          }
          this.systems = [...newDataList];
          this.loading = false
          this.getArticleData(user);

        }
      });
    },
    firstLogin(user) {
      let i = '';
      let articleTopicId = '';
      if (this.dialogs.length > 0) {
        i = this.dialogs.length - 1;
        articleTopicId = this.dialogs[i].articleTopicId
      }


      isFirstLogin(user, articleTopicId).then((res) => {
        if (res.data == 'Y') {
          this.showPdf();
        }
      });
    },
    changeRange() { },
    //排序，根据num降序排列
    compare(property) {
      return function (a, b) {
        var value1 = a[property];
        var value2 = b[property];
        return value2 - value1;
      };
    },
    //根据简称获取对应的颜色
    getColor(name) {
      if (name == "PMS") {
        return "#e2ae6a";
      } else if (name == "EHR") {
        return "#35b4ac";
      } else if (name == "EOSS") {
        return "#ee8936";
      } else if (name == "TSM") {
        return "#0080be";
      } else if (name == "TMS") {
        return "#728cd7";
      } else if (name == "CRM") {
        return "#3bc9e8";
      } else if (name == "FMS") {
        return "#4280E7";
      } else if (name == "RAMS") {
        return "#9f7aeb";
      } else if (name == "NTDH－EOSS") {
        return "#ee8936";
      } else if (name == "RMS") {
        return "#34cf8f";
      } else if (name == "应用配置管理") {
        return "#0080BE";
      } else if (name == "组织机构管理") {
        return "#1BB1DE";
      } else if (name == "TRAIN") {
        return "#728cd7";
      } else if (name == "DM") {
        return "#1D4AA1";
      } else if (name == "TRAINMGT") {
        return "#FFB18F";
      } else {
        return "#0080BE";
      }
    },
    //点击跳转到相应的系统
    async sysClick(index, url, appShortName) {
      // this.loading = true;
      //获取用户信息
      // getWechatUserInfo(this.userName).then((res) => {
      //   this.loginName = res.data.userId;
      //   this.loading = false;
      //   if (this.loginName == this.userName) {
      //     //单点登录1
      //     //window.open(url)

      //单点登录2
      createToken({
        userName: this.userName,
        clientid: appShortName,
      }).then((ret) => {
        let openUrl = `${url}?token=${ret.data.token}`;
        window.open(openUrl);
      });
      //   }
      // });
    },
    // 打开新闻
    openNews(item) {
      this.dialogTipVisible = true;
      this.activename = item.dmvaluemeaning;

      // addviewLog({
      //   userName: this.userName,
      //   name: this.LoginName,
      //   id: item.id,
      // }).then((res) => {
      //   console.log(res);
      // });
      this.getNewsData(item.dmvalue);
    },
    // 弹窗的关闭按钮
    handleTipClose() {
      this.dialogTipVisible = false;
    },
    handleTipCloseDetail() {
      this.dialogTipVisibleDetail = false;
    },
    goMoreNews(str) {
      if (str === '通知公示') {
        this.$router.push({ name: "NoticePreview" });
      } else {
        this.$router.push({ name: "menuIndexMoreNews" });
      }
    },
    //根据月份获取日志数据
    getWorkDailylist() {
      const currentMonth = this.formatMonth(this.currentMonth);
      // const currentMonth = date.getMonth() + 1; // 注意月份是从0开始计数，所以需要加1
      console.log(currentMonth); // 输出当前月份
      var params = {
        username: this.userName,
        queryMonth: currentMonth,
      }
      getlistWorkDaily(params).then((res) => {
        if (res.code == 200) {
          const arr = res.data.dayTime;
          this.dayTime = Array.from(new Set(arr));
        }
      });
    },
    getDomList() {
      getDomainList(this.userName).then((res) => {
        if (res.code == 200) {
          // this.listWorkType = res.data.listWorkType;
          this.listDailyType = res.data.listDailyType;
          this.listProjectStage = res.data.listProjectStage;
        }
      });
      getProList(this.userName).then((res) => {
        if (res.code == 200) {
          this.listProjectName = res.data;
        }
      });
    },
    changeProName() {
      if (this.editForm.projectid) {
        this.editForm.projectname = this.getProjectName(this.editForm.projectid)
      }
      if (this.ruleForm.projectid) {
        this.ruleForm.projectname = this.getProjectName(this.ruleForm.projectid)
      }
    },
    //根据日期获取日志数据
    getWorkDailylistForDay(time) {
      const currentDay = this.formatDate(time);
      var params = {
        username: this.userName,
        queryDay: currentDay,
      }
      getlistWorkDailyForDay(params).then((res) => {
        if (res.code == 200) {
          this.logRecords = res.data;
          this.logloading = false;
        }
      });
    },
    // 开始时间
    changeStart() {
      let dateTime = this.formatDate(this.ruleForm.diarydate);
      // 开始时间
      this.dateTimeStart = this.isMore == true ? this.editForm.starttime : this.ruleForm.starttime;
      this.dateTimeEnd = this.isMore == true ? this.editForm.endtime : this.ruleForm.endtime;
      let durationStart = new Date(dateTime.replace(/-/g, "/") + " " + this.dateTimeStart).getTime();
      // 12~13点之间不算工作时间
      // 12点验证
      let twelveTime = new Date(dateTime + " " + "12:00").getTime();
      // 13点验证
      let thirteenTime = new Date(dateTime + " " + "13:00").getTime();
      if (durationStart > twelveTime && durationStart < thirteenTime) {
        this.$message({
          message: '12~13点之间不算工作时间',
          type: 'warning'
        });
        this.ruleForm.starttime = ''; // 重置时间
        this.editForm.starttime = ''; // 重置时间
      }
      if (this.dateTimeEnd !== "") {
        // 结束时间
        const startHours = parseInt(this.dateTimeStart.substring(0, 2), 10);
        const startMinutes = parseInt(this.dateTimeStart.substring(3, 5), 10);
        const endHours = parseInt(this.dateTimeEnd.substring(0, 2), 10);
        const endMinutes = parseInt(this.dateTimeEnd.substring(3, 5), 10);
        if (startHours > endHours || (startHours === endHours && startMinutes > endMinutes)) {
          this.$message({
            message: '开始时间不能大于结束时间',
            type: 'warning'
          });
          this.ruleForm.starttime = ''; // 重置时间
          this.editForm.starttime = ''; // 重置时间
        } else {
          // 开始时间
          let durationStart = new Date(dateTime.replace(/-/g, '/') + " " + this.dateTimeStart).getTime();
          // 结束时间
          let durationEnd = new Date(dateTime.replace(/-/g, '/') + " " + this.dateTimeEnd).getTime();
          // 工作时长
          let duration = null;
          if (durationEnd <= twelveTime || durationStart >= thirteenTime) {
            duration = Number((durationEnd - durationStart) / 1000 / 3600).toFixed(2);
          } else if (
            durationStart <= twelveTime &&
            durationEnd >= thirteenTime
          ) {
            duration = (Number((durationEnd - durationStart) / 1000 / 3600) - 1).toFixed(2);
          }
          if (this.isMore == true) {
            this.editForm.manhoure = Number(duration);
          } else {
            this.ruleForm.manhoure = Number(duration);
          }
        }
      }
    },
    handleDetail(row) {

      getDetailByUserId(row.id, window.localStorage.getItem("Susername"), 'type_2').then(res => {
        if (res.code === 200) {
          this.detailData = res.data;
          this.detailDialogVisible = true;
        } else {
          this.$message.error(res.message || '获取详情失败');
        }
      }).catch(() => {
        this.$message.error('获取详情失败');
      });
    },

    detailmenuClose() {
      this.detailDialogVisible = false
      this.detailData = {
        title: '',
        content: '',
        attach: [],
        userNames: ''
      }
    },

    // 结束时间
    changeEnd() {
      let dateTime = this.formatDate(this.ruleForm.diarydate);
      // 开始时间
      this.dateTimeStart = this.isMore == true ? this.editForm.starttime : this.ruleForm.starttime;
      this.dateTimeEnd = this.isMore == true ? this.editForm.endtime : this.ruleForm.endtime;// 12~13点之间不算工作时间
      // 12点验证
      let twelveTime = new Date(dateTime + " " + "12:00").getTime();
      // 13点验证
      let thirteenTime = new Date(dateTime + " " + "13:00").getTime();
      let durationStart = new Date(dateTime.replace(/-/g, "/") + " " + this.dateTimeStart).getTime();
      let durationEnd = new Date(dateTime.replace(/-/g, "/") + " " + this.dateTimeEnd).getTime();
      if (!this.dateTimeStart) {
        this.$message({
          message: '请选择开始时间',
          type: 'warning'
        });
      } else if (durationEnd <= durationStart) {
        this.$message({
          message: '结束时间不能小于开始时间',
          type: 'warning'
        });
        this.ruleForm.endtime = ''; // 重置时间
        this.editForm.endtime = ''; // 重置时间
      } else if (durationEnd > twelveTime && durationEnd < thirteenTime) {
        this.$message({
          message: '12~13点之间不算工作时间',
          type: 'warning'
        });
        this.ruleForm.endtime = ''; // 重置时间
        this.editForm.endtime = ''; // 重置时间
      } else {
        // 开始时间
        let durationStart = new Date(dateTime.replace(/-/g, '/') + " " + this.dateTimeStart).getTime();
        // 结束时间
        let durationEnd = new Date(dateTime.replace(/-/g, '/') + " " + this.dateTimeEnd).getTime();
        // 工作时长
        let duration = null;
        if (durationEnd <= twelveTime || durationStart >= thirteenTime) {
          duration = Number((durationEnd - durationStart) / 1000 / 3600).toFixed(2);
        } else if (
          durationStart <= twelveTime &&
          durationEnd >= thirteenTime
        ) {
          duration = (Number((durationEnd - durationStart) / 1000 / 3600) - 1).toFixed(2);
        }
        if (this.isMore == true) {
          this.editForm.manhoure = Number(duration);
        } else {
          this.ruleForm.manhoure = Number(duration);
        }
      }
    },
    // getLogPeople(){
    //   let param = {
    //     orgId:this.ruleForm.orgId,
    //     pageNum:this.queryParams.pageNum,
    //     pageSize:this.queryParams.pageSize,
    //     state:1
    //   }
    //   getLogPeople(param).then((res) => {
    //     if (res.code == 200) {
    //       this.projectNameList = res.data.staffSubject
    //     }
    //   });
    // },
    //日志推送人
    handleSelectByClick(item) {
      this.deliverer1 = []
      if (item.length > 0) {
        // 处理子组件选中事件，可以在这里更新父组件的数据或做其他操作
        item.map(v => {
          this.deliverer1.push(v.userName)
        })
        this.deliverer1 = this.fn2(this.deliverer1)
        this.editForm.userName = this.deliverer1.join(',')
      }
    },
    //日志推送人
    handleSelectByClick1(item) {
      this.deliverer = []
      if (item.length > 0) {
        let arr = item
        // 处理子组件选中事件，可以在这里更新父组件的数据或做其他操作
        arr.map(v => {
          this.deliverer.push(v.userName)
        })
        this.deliverer = this.fn2(this.deliverer)
        this.ruleForm.userName = this.deliverer.join(',')
      }
    },
    //去重
    fn2(tempArr) {
      let idMap = {};
      for (let i = 0; i < tempArr.length; i++) {
        let item = tempArr[i];
        idMap[item] = item; // 覆盖旧的记录，保留最新的数据
      }
      // 将 idMap 转换回数组
      let newArr = Object.values(idMap);
      return newArr;
    },
    // 填写日志
    writeLog() {
      this.delivererList = []
      if (this.isDetail == true) {
        this.logTitle = "添加工作日志";
        this.dialogDetVisibleLog = true;
        this.logloading = true
        this.isMore = true
        this.getLastDeliverer1()
      } else {
        this.dialogTipVisibleLog = true;
        this.isMore = false;
        this.ruleForm.starttime = "09:00";
        this.ruleForm.endtime = "18:00";
        this.ruleForm.manhoure = 8;
        this.getLastDeliverer()
      }
    },
    //新增时上一次日志送达人回显
    getLastDeliverer() {
      lastDeliverer(this.userName).then(res => {
        if (res.code == 200) {
          this.deliverer = res.data.map(staff => staff.userName);
          this.$set(this, 'delivererList', res.data)
          this.ruleForm.userName = this.deliverer.join(',')
          this.$nextTick(() => {
            this.$refs.selecteWithSearch.setSelect(this.delivererList);
          })
        }
      })
    },
    //新增时上一次日志送达人回显
    getLastDeliverer1() {
      lastDeliverer(this.userName).then(res => {
        if (res.code == 200) {
          this.deliverer1 = res.data.map(staff => staff.userName);
          this.$set(this, 'delivererList', res.data)
          this.editForm.userName = this.deliverer1.join(',')
          this.$nextTick(() => {
            this.$refs.selecteWithSearch.setSelect(this.delivererList);
          })
        }
      })
    },
    //获取项目名称
    getProjectName(val) {
      const foundOption = this.listProjectName.find(option => option.projectid == val);
      return foundOption ? foundOption.projectname : '';
    },
    editLog(val) {
      this.logTitle = "编辑工作日志";
      this.editForm = {
        diarydate: val.DiaryDate,
        starttime: val.StartTime,
        endtime: val.EndTime,
        manhoure: val.Manhoure,
        workplace: val.WorkPlace,
        id: val.id,
        worktype: val.WorkType + '',
        diarytype: val.DiaryType + '',
        projectid: val.ProjectId ? Number(val.ProjectId) : "",
        projectname: val.ProjectName,
        projectcode: val.ProjectCode,
        state: val.State,
        content: val.Content,
        employeeid: val.EmployeeId,
      }
    },
    deleteLog(val, i) {
      this.disDel = true;
      delWorkDaily(
        { id: val.id }
      ).then((res) => {
        if (res.code == 200) {
          this.$message({
            message: "该记录已删除！",
            type: "success",
          });
          this.disDel = false;
          this.logRecords.splice(i, 1);
          this.getLastDeliverer1()
          this.$refs.editForm.resetFields();
          if (this.logRecords.length == 0) {
            this.getWorkDailylist();
            this.dialogDetVisibleLog = false;
            document.documentElement.style.overflow = "auto";
          }
        } else {
          this.disDel = false;
          this.$message.error({
            message: res.message || "失败！",
          });
        }
      });
    },
    //编辑记录
    edit() {
      if (this.logTitle == "编辑工作日志") {
        this.$refs.editForm.validate((valid) => {
          if (valid) {
            if (this.editForm.content.length < 20) {
              this.$message({
                message: '请填写工作内容，长度为20个字以上',
                type: 'warning'
              });
              return;
            }
            this.adddis = true
            this.editForm.username = this.userName;
            this.editForm.diarydate = moment(this.editForm.diarydate).format("YYYY-MM-DD");
            editWorkDaily(this.editForm).then((res) => {
              if (res.code == 200) {
                this.adddis = false;
                this.$message({
                  message: "成功！",
                  type: "success",
                });
                this.getWorkDailylist();
                this.$refs.editForm.resetFields();
                this.dialogDetVisibleLog = false;
                document.documentElement.style.overflow = "auto";
              } else {
                this.adddis = false;
                this.$message.error({
                  message: res.message || "失败！",
                });
              }
            });
          }
        })
      } else {
        this.$refs.editForm.validate((valid) => {
          if (valid) {
            if (this.editForm.content.length < 20) {
              this.$message({
                message: '请填写工作内容，长度为20个字以上',
                type: 'warning'
              });
              return;
            }
            this.adddis = true
            this.editForm.username = this.userName;
            this.editForm.employeeid = "";
            this.editForm.id = "";
            //日志送达人
            if (this.deliverer1 && this.deliverer1.length > 0) {
              this.editForm.deliverer = this.deliverer1.join(',')
              delete this.editForm.userName
            } else {
              delete this.editForm.userName
              delete this.editForm.deliverer
            }
            saveWorkDaily(this.editForm).then((res) => {
              if (res.code == 200) {
                this.adddis = false;
                this.$message({
                  message: "成功！",
                  type: "success",
                });
                this.getWorkDailylist();
                this.$refs.editForm.resetFields();
                this.dialogDetVisibleLog = false;
                document.documentElement.style.overflow = "auto";
              } else {
                this.adddis = false;
                this.$message.error({
                  message: res.message || "失败！",
                });
              }
            });
          }
        })
      }
    },
    //添加记录
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.ruleForm.content.length < 20) {
            this.$message({
              message: '请填写工作内容，长度为20个字以上',
              type: 'warning'
            });
            return;
          }
          this.adddis = true
          this.ruleForm.username = this.userName;
          this.ruleForm.employeeid = "";
          //日志送达人
          if (this.deliverer && this.deliverer.length > 0) {
            this.ruleForm.deliverer = this.deliverer.join(',')
            delete this.ruleForm.userName
          } else {
            delete this.ruleForm.userName
            delete this.ruleForm.deliverer
          }
          saveWorkDaily(this.ruleForm).then((res) => {
            if (res.code == 200) {
              this.adddis = false;
              this.$message({
                message: "成功！",
                type: "success",
              });
              this.getWorkDailylist();
              this.deliverer = []
              this.$refs.ruleForm.resetFields();
              this.dialogTipVisibleLog = false;
              document.documentElement.style.overflow = "auto";
            } else {
              this.adddis = false;
              this.$message.error({
                message: res.message || "失败！",
              });
            }
          });
        }
      })
    },
    handleTipLogClose() {
      this.dialogTipVisibleLog = false;
      document.documentElement.style.overflow = "auto";
      this.$refs.ruleForm.resetFields();
    },
    handleDetLogClose() {
      this.dialogDetVisibleLog = false;
      document.documentElement.style.overflow = "auto";
      this.$refs.editForm.resetFields();
    },

    goBladex(item) {
      // 获取临时token
      createToken({
        userName: this.userName,
        // clientid: appShortName,
        clientid: 'BLADEX',
      }).then((res) => {
        this.code = res.data.token
        if (this.code) {
          let openUrl = `${item.url}?code=${res.data.token}`;
          window.open(openUrl);
        }
      });
    }
  },
};
</script>

<style scoped lang="less">
.container {
  padding: 0;
  margin: 0;
  overflow: hidden;
  min-height: calc(100vh - 120px);
}

/deep/ .el-image-viewer__img {
  transform: scale(1) rotate(0deg);
  margin-left: 0px;
  margin-top: 0px;
  max-height: 1200px !important;
  max-width: 100%;
}

.readBtn {
  text-align: center;
}

.agreeBtn {
  width: 83px;
  color: #fff;
  border: none;
  background-color: #31C97E !important;
}

.sFont {
  font-size: 1rem;
  font-weight: 400;
  color: white;
  margin: 0.875rem;
  cursor: pointer;
}

.swiperBg {
  display: flex;
  // justify-content: center;
  align-items: center;
  width: 23%;
  margin: 0 0.4rem;
  margin-bottom: 0.4rem;
  height: 12.5vh;
}

.swiperBg_ys {
  width: 70%;
  border-radius: 3px;
  padding: 0.5rem 0;
  margin: 0 auto 0.5rem;
}

.swiperBg_title {
  font-size: 0.825rem;
  font-weight: 400;
  color: #fff;
}

.swiperBg img {
  width: 2.125rem;
  height: 2.125rem;
  //margin-left: 1rem;
  cursor: pointer;
}

.openNew {
  .el-dialog__body {
    padding: 15px 20px;
  }
}

.logRecords {
  padding: 0 10px;
  margin: 0;
  line-height: 18px;
  color: #333;
  font-size: 14px;
  text-align: justify;
  word-wrap: break-word;
  max-height: 50vh;
  overflow-y: auto;
}

iframe {
  border: none;
  /* 隐藏边框 */
  width: 100%;
  /* 根据需要设置宽度 */
  height: 100vh;

  /* 根据需要设置高度 */
  ::-ms-scrollbar {
    width: 12px;
    /* 滚动条宽度 */
  }

  ::-ms-scrollbar-track {
    background-color: #f1f1f1;
    /* 滚动条轨道颜色 */
  }

  ::-ms-scrollbar-thumb {
    background-color: #888;
    /* 滚动条滑块颜色 */
  }

}

.scroll-container {
  width: 100%;
  height: 66vh;
  overflow-y: scroll;
}

::-webkit-scrollbar {
  width: 6px;
  /*滚动条宽度*/
  height: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #21274D;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0px white;
  -webkit-box-shadow: inset 0 0 0px white;
  background-color: rgb(193, 193, 193);
  /*滚动条的背景颜色*/
  border-radius: 20px;
}

/*!* Handle *!*/
/*::-webkit-scrollbar-thumb {*/
/*    background: #888;*/
/*}*/

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.carousel /deep/ .el-carousel__button {
  display: none;
}

.BgCarousel {
  display: flex;
  flex-wrap: wrap;
}

.mainBox {
  display: flex;
  flex-direction: row;
  margin-right: 1rem;
}

.newsModule {
  display: flex;
  flex-direction: column;
  width: 80%;
}

.newsModule1 {
  display: flex;
  flex-direction: column;
  width: 30%;
}

.dividerSty {
  border-bottom: 1px solid #ffffff4d;
  height: 1rem;
  margin-bottom: 1rem;
}

.systemLogo {
  display: flex;
  flex-direction: row;
  margin-bottom: 0.5rem;
  height: 1.75rem;
  line-height: 1.75rem;
}

.systemLogoIcon {
  position: relative;
  width: 10%;
  height: 1.75rem;
  line-height: 1.75rem;
  padding: 0 0.75rem;
  background: #53aed9;
  min-width: 80px;
}

.systemLogoIcon::before {
  //这里就是小三角了
  content: "";
  display: block;
  position: absolute;
  right: 0;
  top: 0.5rem;
  border-width: 7px;
  border-style: dashed solid dashed dashed; //四个参数分别代表向下，左，上右的小三角(对应方向的小三角设置为solid，其他的为dashed。我这里演示向左的小三角，所以设置第二个）
  border-color: transparent #606283 transparent transparent; //四个参数分别代表向下，左，上右的小三角。对应方向的小三角设置你想要的颜色（为了直观我这里设置黑色），其他的为transparent。我这里演示向左的小三角，所以设置第二个。
}

.system-scroll-container {
  width: 100%;
  /* 根据需要调整宽度 */
  //height: 30px; /* 根据需要调整高度 */
  overflow: hidden;
  position: relative;
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.11);
}

.text-wrapper {
  position: absolute;
  width: 100%;
  animation: scroll 32s linear infinite;
}

.text-wrapper p {
  display: inline;
  float: left;
  width: 50%;
}

.no-style {
  color: inherit;
  /* 继承父元素的文字颜色 */
  /*text-decoration: none; !* 去除下划线 *!*/
  /* 添加其他需要的样式以保持链接的可读性和可区分性 */
}

@keyframes scroll {
  0% {
    transform: translateX(100%);
  }

  50% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}

.systemName2 {
  padding-left: 1.25rem;
  position: absolute;
  white-space: nowrap;
  animation: systermScroll 30s linear infinite;
  /* 调整动画时间和速度 */
}

@keyframes systermScroll {
  0% {
    left: 100%;
    /* 从右侧开始 */
  }

  100% {
    left: -100%;
    /* 滚动到左侧结束 */
  }
}

.homeBanner {
  height: 56%;
  margin-right: 1rem;
}

.homeBanner1 {
  //margin-top: 1rem;
  margin-right: 1rem;
  background-color: rgba(255, 255, 255, 0.11);
  margin-top: 0.5rem;

  padding: 1rem;
}

.newsCard {
  height: 35vh;
  padding: 1.25rem 1.25rem 1.5rem 1.25rem;
  background-color: rgba(255, 255, 255, 0.11);
  border-radius: 2px;
}

.newsCard1 {
  height: 39.5vh;
  margin-top: 0.5rem;
  padding: 1.25rem 1.25rem 1.5rem 1.25rem;
  background-color: rgba(255, 255, 255, 0.11);
  border-radius: 2px;
}

.timeRi {
  font-size: 0.875rem;
  color: #cdced5;
}

.newsTitleBox {
  height: 32vh;
  overflow: auto;
  margin: 1rem 0;
}

.newsTop {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  align-items: center;
}

.newsTitle {
  height: 1.875rem;
  line-height: 1.875rem;
  color: #aaaaaa;
  background-color: #393d5d;
  display: flex;
  justify-content: space-between;
  padding: 0 0.625rem;
  border-radius: 2px;
  margin: 6px 0;
  font-size: 0.75rem;
}

.newsTitleSon {
  display: flex;
  justify-content: space-between;
  height: 1.875rem;
  line-height: 1.875rem;
  font-size: 0.9rem;
  color: #fff;
  cursor: pointer;
}

.carousel {
  background-color: rgba(255, 255, 255, 0.11);
  margin-top: 0.5rem;
  height: 32vh;
  padding: 1rem;
}

.Title {
  font-weight: 600;
  font-size: 1.125rem;
  color: #fff;
  margin-bottom: 1rem;
}

.Title1 {
  font-weight: 600;
  font-size: 1.125rem;
  color: #fff;
}

.application {
  display: flex;
  flex-wrap: wrap;
}

.applicationSon {
  width: 22%;
  color: white;
  margin-right: 1.25rem;
  background-color: #2e325f;
  height: 3.125rem;
  text-align: center;
  line-height: 3.125rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: transform 0.2s;
  /* 添加过渡动画 */
}

.applicationSon:hover {
  transform: scale(1.1);
  /* 鼠标悬停时放大按钮 */
}

.applicationBtn {
  width: 22%;
  margin-right: 1.25rem;
  background-color: #2e325f;
  height: 3.125rem;
  text-align: center;
  line-height: 3.125rem;
  margin-bottom: 1.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: transform 0.5s;
  /* 添加过渡动画 */
  color: #409eff;
}

.applicationBtn:hover {
  background-color: #409eff;
  color: #fff;
}

.headSculpture {
  background-color: #409eff;
  width: 3.75rem;
  height: 3.75rem;
  border-radius: 50%;
  line-height: 3.75rem;
  text-align: center;
  color: white;
  font-size: 1rem;
  /*font-weight: 600;*/
  margin-right: 1.25rem;
}

.avatar {
  width: 3.75rem;
  height: 3.75rem;
  border-radius: 50%;
}

.loginInformation {
  height: 7.5vh;
  display: flex;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.11);
  margin-bottom: 1rem;
  align-items: center;
}

.user {
  padding-top: 0px;
}

.userPosition {
  display: flex;
  color: #cdced5;
  height: 1.125rem;
  line-height: 1.125rem;
  text-align: center;
}

.userName {
  font-size: 1.125rem;
  color: #fff;
  font-weight: 500;
  margin-bottom: 0.875rem;
}

.userPo {
  font-size: 0.75rem;
  line-height: 1.25rem;
  color: #cdced5;
}

.userLine {
  /*height: 12px;*/
  /*border-right: 2px solid #fff;*/
  margin: 0 0.5rem;
}

.more {
  color: #409eff;
  font-size: 0.875rem;
  font-weight: 400;
  cursor: pointer;
}

.riLiFa {
  display: flex;
  margin-top: 1rem;
}

/deep/ .el-calendar {
  background-color: transparent;
}

/deep/ .el-calendar-table thead th {
  color: white !important;
  font-size: 1rem !important;
}

/deep/ .el-calendar__title {
  color: white !important;
  font-size: 0.875rem !important;
}

/deep/ .addRiZhi {
  background-color: rgba(255, 255, 255, 0.11);
  border: transparent;
  width: 100%;
  color: #409eff;
  cursor: pointer;
  transition: transform 0.5s;
  /* 添加过渡动画 */
}

.addRiZhi:hover {
  background-color: #409eff;
  color: #fff;
}

.budge {
  width: 0.425rem;
  height: 0.425rem;
  background: #00FFFF;
  border-radius: 50%;
  margin: 0 auto;
  margin-top: 0.05rem;
}

/deep/ .el-divider {
  background-color: rgba(255, 255, 255, 0.3);
}

/deep/.el-divider--horizontal {
  margin: 1rem 0 !important;
}

.numRz {
  font-size: 1rem;
  font-weight: 400;
  color: #fff;
  margin-bottom: 0.6rem;
}

.riLi {
  height: 3.125rem;
  width: 3.125rem;
  background-color: rgba(255, 255, 255, 0.11);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.riZhi {
  display: flex;
  justify-content: space-between;
  width: 14vw;
  align-items: center;
}

/deep/ .el-carousel {
  height: 29vh;
}

/deep/ .el-carousel__container {
  height: 28.5vh;
}

/deep/ .el-carousel__arrow {
  background-color: rgba(255, 255, 255, 0.3);
}

.image-swiper {
  height: 44vh;
}

.ellipsis {
  overflow: hidden;
  /* 确保超出容器的内容被裁剪 */
  white-space: nowrap;
  /* 确保文本在一行内显示 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}

.dialogTitle {
  font-size: 1.5rem;
  color: #000;
}

.dialogTime {
  font-size: 0.8rem;
  color: #606266;
}

.dialogContent {
  border: 1px solid rgba(62, 62, 62, 0.2);
  box-shadow: rgba(62, 62, 62, 0.2) 0px 0px 12px 0px;
  padding: 0.5rem 1.25rem;
  margin-bottom: 1rem;
}

@media screen and (min-width: 600px) and (max-width: 1300px) {}

@media screen and (min-width: 961px) and (max-width: 1100px) {
  html {
    font-size: 8px !important;
  }
}

@media screen and (min-width: 1101px) and (max-width: 1200px) {
  html {
    font-size: 10px !important;
  }
}

@media screen and (min-width: 1201px) and (max-width: 1350px) {
  html {
    font-size: 12px !important;
  }

  .newsTitleSon {
    height: 1.75rem;
    line-height: 1.75rem;
  }
}

@media screen and (min-width: 1351px) and (max-width: 1500px) {
  html {
    font-size: 14px !important;
  }

  .newsTitleSon {
    height: 1.875rem;
    line-height: 1.875rem;
  }

  .addRiZhi {
    // margin-top: -0.625rem;
  }
}

@media screen and (min-width: 1501px) and (max-width: 1600px) {
  html {
    font-size: 15px !important;
  }

  .newsTitleSon {
    height: 2.125rem;
    line-height: 2.125rem;
  }
}

@media screen and (min-width: 1601px) and (max-width: 1800px) {
  html {
    font-size: 18px !important;
  }

  .newsTitleSon {
    height: 2.25rem;
    line-height: 2.25rem;
    font-size: 1rem;
  }
}

@media screen and (min-width: 1801px) and (max-width: 1920px) {
  html {
    font-size: 18px !important;
  }

  .newsTitleSon {
    height: 2.25rem;
    line-height: 2.25rem;
  }
}

@media screen and (min-width: 1920px) {
  html {
    font-size: 20px !important;
  }

  .newsTitleSon {
    height: 2.5rem;
    line-height: 2.5rem;
  }
}
</style>

<style lang="less">
.Calendar {
  .el-button--mini:nth-child(2) {
    display: none;
  }

  .el-button--mini {
    background-color: #383d5f;
    border: 1px solid #5f6382;
    color: #cdced5;
    font-size: 0.75rem;
    padding: 0.4375rem 0.875rem;
  }

  .el-calendar-table__row {
    /*height: 50px !important;*/
  }

  .el-calendar__body {
    padding: 0.875rem 0 1rem;
  }

  .el-calendar-day {
    height: 1.85rem !important;
    border-radius: 2px;
    position: relative;
    text-align: center;
    font-size: 0.8rem;
    padding: 0.5rem !important;
  }

  .el-calendar {
    height: 100%;
    width: 100%;
  }

  .current {
    color: #fff;
  }

  .el-calendar-table tr td:first-child {
    border-left: transparent;
  }

  .el-calendar-table tr:first-child td {
    border-top: transparent;
  }

  .el-calendar-table td {
    border-bottom: transparent;
    border-right: transparent;
  }

  .el-calendar__header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding: 4px 20px 8px !important;
  }

  .el-calendar-table td.is-selected {
    background-color: #409eff;
    color: #fff;
  }

  .el-calendar-table .el-calendar-day:hover {
    background-color: rgba(255, 255, 255, 0.3) !important;
  }
}

@media screen and (min-width: 1101px) and (max-width: 1200px) {
  .Calendar {
    .el-calendar__title {
      font-size: 0.7rem !important;
    }

    .el-button--mini {
      font-size: 0.7rem !important;
    }

    .el-calendar-table thead th {
      font-size: 0.7rem !important;
    }

    .el-calendar__body {
      padding: 0rem 0 1rem !important;
    }

    .el-calendar-day {
      height: 1.25rem !important;
      line-height: 0.25rem !important;
      font-size: 0.7rem;
    }
  }
}

@media screen and (min-width: 1201px) and (max-width: 1350px) {
  .Calendar {
    .el-calendar__title {
      font-size: 0.7rem !important;
    }

    .el-button--mini {
      font-size: 0.7rem !important;
    }

    .el-calendar-table thead th {
      font-size: 0.7rem !important;
    }

    .el-calendar__body {
      padding: 0rem 0 1rem !important;
    }

    .el-calendar-day {
      height: 1.8rem !important;
      line-height: 0.8rem !important;
      font-size: 0.7rem;
    }
  }
}

@media screen and (min-width: 1351px) and (max-width: 1500px) {
  .Calendar {
    .el-calendar__title {
      font-size: 0.7rem !important;
    }

    .el-button--mini {
      font-size: 0.7rem !important;
    }

    .el-calendar-table thead th {
      font-size: 0.7rem !important;
    }

    .el-calendar__body {
      padding: 0rem 0 1rem !important;
    }

    .el-calendar-day {
      height: 2rem !important;
      line-height: 1rem !important;
      font-size: 0.7rem;
    }
  }
}

@media screen and (min-width: 1601px) and (max-width: 1800px) {
  .Calendar {
    .el-calendar__title {
      font-size: 0.7rem !important;
    }

    .el-button--mini {
      font-size: 0.7rem !important;
    }

    .el-calendar-table thead th {
      font-size: 0.7rem !important;
    }

    .el-calendar__body {
      padding: 0rem 0 1rem !important;
    }

    .el-calendar-day {
      height: 1.9rem !important;
    }
  }
}

@media screen and (min-width: 1601px) and (max-width: 1800px) {
  .Calendar {
    .el-calendar__title {
      font-size: 0.8rem !important;
    }

    .el-button--mini {
      font-size: 0.8rem !important;
    }

    .el-calendar-table thead th {
      font-size: 0.8rem !important;
    }

    .el-calendar__body {
      padding: 0rem 0 1rem !important;
    }

    .el-calendar-day {
      height: 2.5rem !important;
      line-height: 1.8rem !important;
      font-size: 0.8rem;
    }
  }
}

@media screen and (min-width: 1801px) and (max-width: 1920px) {
  .Calendar {
    .el-calendar__title {
      font-size: 0.8rem !important;
    }

    .el-button--mini {
      font-size: 0.8rem !important;
    }

    .el-calendar-table thead th {
      font-size: 0.8rem !important;
    }

    .el-calendar__body {
      padding: 0rem 0 1rem !important;
    }

    .el-calendar-day {
      height: 2.5rem !important;
      line-height: 1.8rem !important;
      font-size: 0.8rem;
    }
  }
}

@media screen and (min-width: 1920px) {
  .Calendar {
    .el-calendar-day {
      height: 3rem !important;
      line-height: 2rem !important
    }
  }
}


/* 新闻列表样式 */
.news-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.news-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  margin-bottom: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #e9ecef;
  
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.news-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: linear-gradient(180deg, #409eff 0%, #67c23a 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.news-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.news-item:hover::before {
  transform: scaleY(1);
}

.news-content {
  flex: 1;
  min-width: 0;
  display: flex;
  justify-content: space-between;
}

.news-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.news-item:hover .news-title {
  color: #409eff;
}

.news-time {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6c757d;
  margin-right: 12px;
}

.news-time i {
  margin-right: 6px;
  font-size: 14px;
  color: #409eff;
}

.news-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
 //  width: 32px;
  // height: 32px;
  cursor: pointer;
  border-radius: 4px;
  background: rgba(64, 158, 255, 0.1);
  transition: all 0.3s ease;
  padding:12px 16px;
}

.news-arrow i {
  font-size: 16px;
  color: #409eff;
  transition: transform 0.3s ease;
}

.news-item:hover .news-arrow {
  background: rgba(64, 158, 255, 0.2);
  transform: scale(1.1);
}

.news-item:hover .news-arrow i {
  transform: translateX(2px);
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .news-item {
    padding: 12px 16px;
    margin-bottom: 8px;
  }
  
  .news-title {
    font-size: 14px;
  }
  
  .news-time {
    font-size: 12px;
  }
  
  .news-arrow {
    width: 28px;
    height: 28px;
  }
  
  .news-arrow i {
    font-size: 14px;
  }
}
</style>
