import request from "@/utils/request";

export const listRoles = () => {
    return new Promise((resolve) => {
        request.get("/role/listRoles").then((res) => {
            resolve(res);
        });
    });
};
//获取所有角色
export const getAllRole = (param) => {
    return new Promise((resolve) => {
        request.get("/role/getAllRole?pageSize=" + param.pagesize + "&&pageNum=" +
            param.currentPage + "&&sSearch=" + param.searchword
        ).then((res) => {
            resolve(res);
        });
    });
};
//新增角色
export const addRole = (param) => {
    return new Promise((resolve) => {
        request.post("/role/addRole", param).then((res) => {
            resolve(res);
        });
    });
};
//修改角色
export const updateRole = (param) => {
    return new Promise((resolve) => {
        request.post("/role/updateRole", param).then((res) => {
            resolve(res);
        });
    });
};
//获取单个角色详情
export const getSingleRole = (roleId) => {
    return new Promise((resolve) => {
        request.get("/role/getSingleRole?roleId=" + roleId).then((res) => {
            resolve(res);
        });
    });
};
//删除角色
export const delRole = (roleId) => {
    return new Promise((resolve) => {
        request.get("/role/delRole?roleIds=" + roleId).then((res) => {
            resolve(res);
        });
    });
};
//获取单个菜单详情
export const getSingleMenu = (roleId) => {
    return new Promise((resolve) => {
        request.get("/role/getSingleMenu?roleId=" + roleId).then((res) => {
            resolve(res);
        });
    });
};
//新增菜单
export const addMenu = (param) => {
    return new Promise((resolve) => {
        request.post("/role/addMenu", param).then((res) => {
            resolve(res);
        });
    });
};
//获取单个角色权限配置详情
export const getSinglePower = (roleId) => {
    return new Promise((resolve) => {
        request.get("/role/getSinglePower?roleId=" + roleId).then((res) => {
            resolve(res);
        });
    });
};
//新增角色权限配置
export const addPower = (param) => {
    return new Promise((resolve) => {
        request.post("/role/addPower", param).then((res) => {
            resolve(res);
        });
    });
};