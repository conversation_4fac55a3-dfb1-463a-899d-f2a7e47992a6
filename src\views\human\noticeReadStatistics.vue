<template>
    <div class="tab" id="version" v-loading="downLoading" element-loading-text="拼命下载中"
        element-loading-spinner="el-icon-loading" element-loading-background="rgb(33,39,77, 0.8)">
        <el-row type="flex" justify="center" class="title">
            <el-col :span="23">
                <div class="title_header">规章制度阅读统计</div>
            </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="search">
            <el-col :span="24" class="searchBox">
                <template>
                    <el-col :span="12" style="text-align: left">
                        <el-select v-model="query.articleTopicId" style="width: 40%;margin-left: 25px;"
                            @change="search()">
                            <el-option v-for="(item, index) in systemList" :label="item.Title"
                                :value="item.articleTopicId" :key="index" />
                        </el-select>
                        <el-button-group style="margin-left: 15px;">
                            <el-button type="primary" @click="searchType(1)"
                                :style="query.type == '1' ? this.activeButtonStyle : this.normalButtonStyle">
                                已读
                            </el-button>
                            <el-button type="primary" @click="searchType(2)"
                                :style="query.type == '2' ? this.activeButtonStyle : this.normalButtonStyle">
                                未读
                            </el-button>
                        </el-button-group>
                    </el-col>
                    <el-col :span="12" style="text-align: right">
                        <el-input placeholder="请输入员工姓名/登陆账号" @keyup.enter.native="search()" style="width: 40%"
                            v-model.trim="query.name" @input="search()">
                            <i slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer"
                                @click="search()"></i>
                        </el-input>
                        <el-button class="btnStyle" type="primary" 
                            :disabled = "totalcount == 0"
                            style="background-color: #31C97E; width: 80px;margin: 0 15px;" size="medium"
                            @click="onExport">导出
                        </el-button>
                    </el-col>
                </template>
            </el-col>
        </el-row>

        <el-row type="flex" justify="center">
            <el-col :span="23">
                <el-table ref="versionTable" :data="tableData" element-loading-background="rgb(33,39,77, 0.8)"
                    height="58vh" style="width: 100%; " class="tableT" highlight-current-row v-loading="loading">
                    <el-table-column label="员工工号" prop="employeeNumber">
                    </el-table-column>
                    <el-table-column label="登陆账号" prop="userName">
                    </el-table-column>
                    <el-table-column label="员工姓名" prop="staffName">
                    </el-table-column>
                    <el-table-column label="部门" prop="depart" width="380px">
                    </el-table-column>
                    <el-table-column label="阅读时间" prop="time">
                        <template slot-scope="props" v-if="query.type == '1'">
                            <span>{{ props.row.time }}</span>
                        </template>
                        <template v-if="query.type == '2'">
                            <span>/</span>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pager">
                    <el-pagination @size-change="handleSizeChange" @current-change="pagehandleCurrentChange"
                        :current-page="currentPage" :page-sizes="[5, 10, 15]" :page-size="pagesize"
                        layout="total, sizes, prev, pager, next, jumper" :total="totalcount"></el-pagination>
                </div>
            </el-col>
        </el-row>

    </div>
</template>

<script>
import {
    getRecordList, downloadArticle,
    getPopUpArticle, exportArticle
} from "@/api/sino/articleTopic";
import axios from "axios";
import moment from 'moment';
export default {
    name: "noticeReadStatistics",
    data() {
        return {
            currentRow: null,
            //版本更新列表
            tableData: [],
            //版本更新列表查询参数
            query: {
                name: '',
                articleTopicId: '',
                type: '1',
            },
            //loading
            loading: false,
            currentPage: 1,
            pagesize: 10,
            totalcount: null,
            activeButtonStyle: {
                background: '#0E61C9',
                color: '#101010 100%',
                width: '72px',
                fontSize: ' 14px',
                border: '0px',
                borderRadius: '0px'
            },
            normalButtonStyle: {
                background: '#21274D',
                color: '#D3D3D3 100%',
                border: '0px solid rgb(227, 227, 227)',
                margin: '0 2px',
                borderRadius: '0px'
            },
            systemList: [],
            downLoading: false
        }
    },
    computed: {

    },

    created() {
        //列表页获取
        getPopUpArticle().then((res) => {
            this.systemList = res.data
            this.query.articleTopicId = res.data && res.data[0] && res.data[0].articleTopicId
            this.search();
        })
    },
    mounted() {
    },

    methods: {
        //查询置空
        handResertSearch() {
            this.query = {
                name: '',
                type: '1'
            }
        },
        searchType(num) {
            this.query.type = num
            this.search()
        },
        //版本列表查询
        search() {
            this.currentPage = 1;
            this.getListDate()
        },

        //修改表格分页的展示条数
        handleSizeChange(val) {
            this.pagesize = val;
            this.getListDate();
        },
        //修改表格分页的当前页
        pagehandleCurrentChange(val) {
            this.currentPage = val;
            this.getListDate();
        },
        // 修改表格分页的展示条数
        handleCurrentChange(val) {
            this.currentRow = val;
        },
        //获取列表数据
        getListDate() {
            this.loading = true;
            let param = {
                ...this.query,
                pageNum: this.currentPage,
                pageSize: this.pagesize,
            }
            getRecordList(param).then(res => {
                this.tableData = res.data.handbookLogVOList;
                this.totalcount = res.data.totalCount;
                this.loading = false;
            })
        },

        //时间格式化
        formatDate(dateStr) {
            return moment(dateStr).format('YYYY-MM-DD');
        },
        onExport() {
            window.location.href = process.env.VUE_APP_BASE_API + "/articleTopic/export?articleTopicId=" + this.query.articleTopicId + '&type='+this.query.type+'&name='+this.query.name;
               
        },

    }
};
</script>

<style scoped lang="less">
.tab {
    background-color: #21274D;
    margin: -20px 0;
    height: calc(100vh - 90px);

    ::-webkit-scrollbar {
        width: 6px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #21274D;
    }

    /* Handle */

    ::-webkit-scrollbar-thumb {
        background: #888;
    }

    /* Handle on hover */

    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
}

.pager {
    width: 100%;
    overflow: hidden;
    position: relative;
    margin-top: 10px;
    margin-bottom: 10px;
}

.el-pagination {
    float: right;
}

.title {
    height: 66px;
    line-height: 66px;

    /deep/ .el-input .el-input__inner {
        //border-radius: 34px !important;
        background-color: rgba(255, 255, 255, 0.25) !important;
        color: rgba(199, 199, 199, 1) !important;
        border: 0px;

    }
}


.search {
    height: 70px;
    line-height: 40px;

    .searchBox {
        padding: 10px 0;
        position: relative;
    }

    /deep/ .el-input .el-input__inner,
    .el-range-editor.el-input__inner,
    .el-range-editor.el-range-input,
    .el-range-editor.el-range-separator {
        //border-radius: 34px !important;
        background-color: rgba(255, 255, 255, 0.25) !important;
        color: rgba(199, 199, 199, 1) !important;
        border: 0px;
    }
}


/* 13寸 */
@media screen and (min-width: 600px) and (max-width: 1300px) {
    .tableT {
        margin-bottom: -5px
    }
}

/* 15寸 */
@media screen and (min-width: 1301px) and (max-width: 1920px) {
    .tableT {
        margin-bottom: 30px
    }
}

/deep/ .addP .el-dialog__footer {
    text-align: center !important;
}

ul {
    list-style-type: disc !important;
}

/deep/ .el-loading-mask {
    background-color: #21274d !important;
}

/deep/ #haneleDetailId .el-loading-mask {
    background-color: rgba(255, 255, 255, 1) !important;
}

.myIcon {
    color: #409eff;
    padding: 0 5px;
    cursor: pointer;
}

/deep/ .el-icon-d-arrow-right {
    font-size: 15px !important;
    color: white !important;
}


/deep/ .addP .el-form-item__label {
    color: #939393;
}

/deep/ .search .el-range-editor.el-input__inner {
    width: 100%;
}
</style>