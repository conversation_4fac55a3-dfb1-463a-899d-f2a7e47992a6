<template>
  <div style="width: 100% "  ref="mySelectContainer">
    <el-select
      multiple
      v-model="selectValue"
      placeholder="按账号、姓名搜索"
      :clearable="canClear"
      style="width: 100%;"
      @change="handleSelect"
      class="mySelectContainers"
    >
      <el-input
        class="mySelectInput"
        style="width: 100%; margin-bottom: 10px;"
        :placeholder="placeholder"
        v-model="selectName"
        @input="querySearch2"
      ></el-input>
      <el-option v-if="totalCount>0" aria-readonly="true"  value="">
        <span></span>
      </el-option>
      <el-option
        v-for="item in dataList"
        :key="item.userName"
        :label="item.staffName + '(' + item.userName + ')'"
        :value="item.staffName + '(' + item.userName + ')'"
      >
      </el-option>
      <el-option v-if="totalCount>0" aria-readonly="true"  value="">
        <span></span>
      </el-option>
      <div class="pagination-container" ref="paginationContainer"
      >
        <el-pagination
          small
          class="select-pagination"
          layout="prev, pager, next"
          :total="totalCount"
          :current-page="currentPage"
          @current-change="querySearch"
        >
        </el-pagination>
      </div>

      <div slot="empty" class="empty-slot">
        <el-input
          class="my-select-input"
          :placeholder="placeholder"
          v-model="selectName"
          @input="querySearch2"
        ></el-input>
        <span class="no-data-text">暂无数据</span>
      </div>
    </el-select>
  </div>

</template>

<script>
import request from "@/utils/request";
import {debounce} from "@/utils";

export default {
  name:"CourseSelect",
  props: {
    canClear:{
      type:Boolean,
      default:false
    },
    searchText:{
      type:String,
      default:''
    },
    hadnleshowList:{
      type: Boolean,
      default: true
    },
    //产品型号 和 厂商联动
    partnerId:{
      type: String,
      default: ''
    },
    //供应商类型 和 供应商联动
    supplierType:{
      type: String,
      default: ''
    },
    //请求地址
    getUrl:{
      type:Number,
      require:true,
      default:1
    },
    placeholder: {
      type: String,
      default: '按账号姓名搜索'
    },
    size: {
      type: String,
      default: 'small'
    },
    width:{
      type:String,
      default:'100%'
    },
    meta:{
      type:String,
      default:''
    },
    flag:{
      type:Number,
      default:null
    },
    delivererList:{
      type:Array,
      default: function () {
        return [];
      },
      immediate: true,
      deep:true
    },
  },
  data() {
    return {
      //下拉框绑定值
      selectValue:[],
      //下拉框 查询框 input值
      selectName:this.searchText || '',
      selevctSupplierType:this.supplierType || '',
      dataList: [],
      currentPage: 1,
      pageSize: 10,
      totalCount:0,
      totalPages:0,
      oldTotalCount:0,
      oldTotalPages:0,
      debounceQuerySearch: debounce(()=>{
        this.querySearch()
      },300),
      isPaginationClicked:false,// 追踪是否点击了分页
    };
  },
  computed: {

  },
  mounted(){
    this.selectValue = []
    this.selectName = this.searchText || ''
    this.selevctSupplierType=this.supplierType || '',
    this.querySearch(1)
  },
  beforeDestroy() {
  },
  watch:{
    partnerId:function(val){
      //选择产品 回填厂商
      if(val && this.getUrl == 3){
        this.querySearch(1)
        console.log(this.getUrl,val,"选择产品 回填厂商")
      }
      //厂商置空 查询产品型号
      if(!val && this.getUrl == 4){
        this.querySearch(1)
        console.log(this.getUrl,val,"厂商置空 查询产品型号")
      }
    },
    supplierType:function(val){
      console.log(this.getUrl,val,"选择供应商类型")

      //选择供应商类型 回填供应商
      if(this.getUrl == 5){
        this.selevctSupplierType = val
        this.querySearch(1)
        console.log(this.getUrl,val,"供应商联动")
      }
    },
    delivererList: {
      handler (newVal, oldVal) {
        if(newVal.length>0){
          this.selectValue = newVal.map(item=>item.staffName + '(' + item.userName + ')')
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods:{
    //清空
    clearSelect(){
      this.selectValue = []
      this.selectName = ''
      this.selevctSupplierType = ''
    },
    setSelect(delivererList){
      console.log(this.delivererList,"分页组件")
      this.selectValue = delivererList.map(item=>item.staffName + '(' + item.userName + ')')

      this.querySearch(1)
    },
    // setSelect(getUrl,searchId,searchText,partnerId){
    //   console.log(getUrl,searchId,searchText,partnerId,"分页组件")
    //   this.getUrl = getUrl
    //   if(partnerId){
    //     this.selectName = searchText
    //   }
    //   else{
    //     this.selectValue =[]
    //     this.selectName = searchText
    //   }
    //   this.querySearch(1)
    // },
    //选项回到顶部
    goTop() {
      this.$nextTick(() => {
        const dropdown = this.$refs.mySelectContainer.querySelector('.el-select-dropdown__wrap');
        if (dropdown) {
          dropdown.scrollTop = 0;
        }
      });
    },
    handleSelect(value){
      if(value){
        this.selectValue = value
        const result = this.selectValue.map(item => {
          const [staffName, userName] = item.replace("（", "(").replace("）", ")").split("(");
          return { staffName: staffName.trim(), userName: userName.slice(0, -1).trim() };
        });
        this.$emit('selectItem', result)
      }
      else{
        this.clearSelect()
        this.querySearch(1)
        this.$emit('selectItem', [])
      }


    },
    //输入搜索关键字 查询
    querySearch2(val){
      //this.getUrl 父组件接收的接口地址 这样设计利于不同页面使用 更灵活
      let params = {}
      let url=''
      let name = ''
      let method ='post'
      this.currentPage = 1
      name = val
      switch(this.getUrl){
        case 1: {
          //客户经理
          url = "/staff/getAllStaff",
              method = "get"
          break;
        }
        case 2: {
          //客户名称
          url = "/contract/salescontract/getCustomerByName",
              method = "post"
          break;
        }
        case 5: {
          url = "/business/order/getSupplier",
              method = "post"
          break;
        }
      }
      if(this.getUrl == 5){
        params = {
          url,
          method,
          data:{
            id:this.selevctSupplierType,
            name:name,
            pageNum: this.currentPage,
            pageSize: this.pageSize
          }
        }
        return request(params).then(res => {
          this.dataList = res.rows
          this.totalCount=Number(res.total);
          this.totalPages = Math.ceil(this.totalCount / this.pageSize);
          this.goTop()
        })
      }
      else{
        if(this.meta == '合并开票申请'){
          params = {
            url,
            method,
            data:{
              userId:this.$store.getters.userId,
              name:name,
              pageNum: this.currentPage+'',
              pageSize: this.pageSize+''
            }
          }
        }else
        if(method == 'get'){
          params = {
            url,
            method,
            params:{
              state:1,
              orgId:'',
              search:name,
              pageNum: this.currentPage+'',
              pageSize: this.pageSize+''
            }
          }
        }
        else{
          if(this.getUrl == 3){
            params = {
              url,
              method,
              params:{
                text:name,
                pageNo: this.currentPage+'',
                pageSize: this.pageSize+''
              }
            }
          }
          if(this.getUrl == 4){
            params = {
              url,
              method,
              params:{
                name:name,
                partnerId:this.partnerId || '',
                pageNo: this.currentPage+'',
                pageSize: this.pageSize+''
              }
            }
          }
        }
        return request(params).then(res => {
          this.$nextTick(()=>{
            this.dataList = res.data.staffSubject
            this.totalCount=Number(res.data.totalCount);
            this.totalPages = res.data.pages
            this.goTop()
          })
        })
      }

    },
    //分页查询
    querySearch(val){
      //this.getUrl 父组件接收的接口地址 这样设计利于不同页面使用 更灵活
      let params = {}
      let url=''
      let name = ''
      let method ='post'
      this.currentPage = val || 1
      name = this.selectName
      switch(this.getUrl){
        case 1: {
          //客户经理
          url = "/staff/getAllStaff",
            method = "get"
          break;
        }
        case 2: {
          //客户名称
          url = "/contract/salescontract/getCustomerByName",
            method = "post"
          break;
        }
        case 5: {
          url = "/business/order/getSupplier",
            method = "post"
          break;
        }
      }
      if(this.getUrl == 5){
        params = {
          url,
          method,
          data:{
            id:this.selevctSupplierType,
            name:name,
            pageNum: this.currentPage,
            pageSize: this.pageSize
          }
        }
        return request(params).then(res => {
          this.dataList = res.rows
          this.totalCount=Number(res.total);
          this.totalPages = Math.ceil(this.totalCount / this.pageSize);
          this.goTop()
        })
      }
      else{
        if(this.meta == '合并开票申请'){
          params = {
            url,
            method,
            data:{
              userId:this.$store.getters.userId,
              name:name,
              pageNum: this.currentPage+'',
              pageSize: this.pageSize+''
            }
          }
        }else
        if(method == 'get'){
          params = {
            url,
            method,
            params:{
              state:1,
              orgId:'',
              search:name,
              pageNum: this.currentPage+'',
              pageSize: this.pageSize+''
            }
          }
        }
        else{
          if(this.getUrl == 3){
            params = {
              url,
              method,
              params:{
                text:name,
                pageNo: this.currentPage+'',
                pageSize: this.pageSize+''
              }
            }
          }
          if(this.getUrl == 4){
            params = {
              url,
              method,
              params:{
                name:name,
                partnerId:this.partnerId || '',
                pageNo: this.currentPage+'',
                pageSize: this.pageSize+''
              }
            }
          }
        }
        return request(params).then(res => {
          this.$nextTick(()=>{
            this.dataList = res.data.staffSubject
            this.totalCount=Number(res.data.totalCount);
            this.totalPages = res.data.pages
            this.goTop()
          })
        })
      }

    },
  }
};
</script>

<style scoped>
.empty-slot {
  color: #c0c4cc;
  text-align: center;
  line-height: 35px;
}

.my-select-input {
  width: 100%;
}

.no-data-text {
  display: block;
}
.mySelectContainers {
  position: relative;
}
.mySelectInput{
  width: 100%;
  position: absolute;
  top: 0;
  z-index: 2006;
}

.pagination-container {
  position: absolute;
  bottom: -10px;
  width: 100%;
  height: 40px;
  background: #fff;

}

::v-deep.pagination-container .el-pagination{
    left: 0 !important;
    bottom: -12px !important;
    position: absolute;
    height: 45px !important;
    display: inline-block;
    width: 100%;
}
::v-deep .el-pagination button:disabled{
    background-color: #f5f5f5!important;
    color: #5e5f60!important;
}
::v-deep .el-pager li{
    background-color: #f5f5f5!important;
    color:#5e5f60!important
}
::v-deep .el-pager li.active{
    color:#409eff!important
}
::v-deep .el-pagination .btn-next{
  background-color: #f5f5f5!important;
  color: #5e5f60!important;
}
::v-deep.el-pagination .btn-prev{
  background-color: #f5f5f5!important;
  color: #5e5f60!important;
}
::v-deep .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon{
  color: #5e5f60!important;
}
</style>
