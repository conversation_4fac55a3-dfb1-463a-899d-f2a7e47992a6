import request from "@/utils/request";

/**
 *
 * 版本更新列表 查询
 * **/
export const getRecordList = (param) => {
    return new Promise((resolve) => {
        request.get("/record/list?pageSize=" +
            param.pageSize +
            "&&pageNum=" +
            param.pageNum +
            "&&applicationName=" +
            param.applicationName +
            "&&allName=" +
            param.allName +
            "&&recordLabel=" +
            param.recordLabel +
            "&&recordTheme=" +
            param.recordTheme +
            "&&versionNum=" +
            param.versionNum+
            "&&startDate=" +
            param.startDate +
            "&&endDate=" +
            param.endDate
        ).then((res) => {
            resolve(res);
        });
        // request.get("/record/list?pageSize=" +
        //     param.pageSize +
        //     "&&pageNum=" +
        //     param.pageNum +
        //     "&&applicationName=" +
        //     param.applicationName +
        //     "&&labelThemeName=" +
        //     param.labelThemeName).then((res) => {
        //     resolve(res);
        // });
    });
}
/**
 *
 * 版本更新列表 删除
 * **/
export const delRecord = (param) => {
    return new Promise((resolve) => {
        let str = `/record/delete?recordId=${param}`
        request.get(str).then(res => {
            resolve(res)
        })
    })
}

/**
 *
 * 版本更新列表
 * **/
export const versionSearch = (param) => {
    return new Promise((resolve) => {
        let str = `/record/getVersionNum?applicationName=${param}`
        request.get(str).then(res => {
            resolve(res)
        })
    })
}

/**
 *
 * 版本更新列表 添加
 * **/
export const versionAdd = (param) => {
    return new Promise((resolve) => {
        let str = `/record/insertRecord`
        request.post(str, param).then(res => {
            resolve(res)
        })
    })
}
/**
 *
 * 版本更新列表 单项数据 修改
 * **/
export const versionEdir = (param) => {
    return new Promise((resolve) => {
        let str = `/record/updateRecord`
        request.post(str, param).then(res => {
            resolve(res)
        })
    })
}
/**
 *
 * 版本更新列表 单项数据查询
 * **/
export const versionDetail = (param) => {
    return new Promise((resolve) => {
        let str = `/record/recordDetail?recordId=${param}`
        request.get(str).then(res => {
            resolve(res)
        })
    })
}
/**
 *
 * 版本更新列表 文件下载
 * **/
export const versionDownLoad = (param) => {
    return new Promise((resolve) => {
        let str = `/record/download?id=${param.id}`
        request.get(str).then(res => {
            resolve(res)
        })
    })
}
/**
 *
 * 版本更新列表 添加附件
 * **/
export const versionAddFiles = (param) => {
    return new Promise((resolve) => {
        let str = `sysAttach/insertAttach`
        request.post(str, param).then(res => {
            resolve(res)
        })
    })
}


/**
 *
 * 版本更新列表 下载次数刷新
 * **/
export const versionAddTimes = (param) => {
    return new Promise((resolve) => {
        let str = `/record/addTimes?id=${param}`
        request.get(str).then(res => {
            resolve(res)
        })
    })
}
/**
 *
 * 版本更新列表 获取预览地址
 * **/
export const getPreviewPath = (param) => {
    return new Promise((resolve) => {
        let str = `/record/preview?path=${param}`
        request.get(str).then(res => {
            resolve(res)
        })
    })
}

/**
 *
 * 版本更新列表 获取所有版本
 * **/
export const checkVersionIsExist  = (param) => {
    return new Promise((resolve) => {
        let str = `/record/checkVersionIsExist?applicationName=${param.applicationName}&&versionNum=${param.versionNum}`
        request.get(str).then(res => {
            resolve(res)
        })
    })
}

/**
 *
 * 版本更新列表 获取用户操作权限
 * **/

export const checkUser = (param) => {
    return new Promise((resolve) => {
        let str = `/record/checkUser?userName=${param}`
        request.get(str).then(res => {
            resolve(res)
        })
    })
}




