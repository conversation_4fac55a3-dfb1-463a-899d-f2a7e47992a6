import request from "@/utils/request";
//获取所有角色
export const getAllRole = (param) => {
  return new Promise((resolve) => {
      request.get("/role/getAllRole?pageSize=" + param.pagesize + "&&pageNum=" +
          param.currentPage + "&&sSearch=" + param.searchword
      ).then((res) => {
          resolve(res);
      });
  });
};
//获取列表
export const getCertList = (param) => {
    return new Promise((resolve) => {
        request.get("/certificate/getCertList?userName=" + param.userName).then((res) => {
            resolve(res);
        });
    });
};

//厂商数据
export const getCertFirm = () => {
    return new Promise((resolve) => {
        request.get("/certificate/getCertFirm").then((res) => {
            resolve(res);
        });
    });
};

//名称级别
export const getCertLevel = (param) => {
    return new Promise((resolve) => {
        request.get("/certificate/getCertLevel?firm=" + param.firm).then((res) => {
            resolve(res);
        });
    });
};

//名称级别
export const getCertOrientation = (param) => {
    return new Promise((resolve) => {
        request.get("/certificate/getCertOrientation?firm=" + param.firm + "&&certLevel=" + param.certLevel).then((res) => {
            resolve(res);
        });
    });
};

// 新增提交
export const addCert = (param) => {
    return new Promise((resolve) => {
        request.post("/certificate/addCert", param).then((res) => {
            resolve(res);
        });
    });
};

// 变更提交
export const updateCert = (param) => {
    return new Promise((resolve) => {
        request.post("/certificate/updateCert", param).then((res) => {
            resolve(res);
        });
    });
};


// 重新提交
export const resubmitCert = (param) => {
    return new Promise((resolve) => {
        request.post("/certificate/resubmitCert", param).then((res) => {
            resolve(res);
        });
    });
};
