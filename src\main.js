import Vue from 'vue'
import App from './App.vue'
import router from './router'
import server from './utils/request'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import store from './store'
import './assets/fonts/iconfont.css'
import './assets/css/reset.css'
import './assets/css/font.css'
import 'animate.css'
import './assets/icons/iconfont.css'
import Moment from 'moment'
import NProgress from 'nprogress'
import { clearCookie, getCookie ,setCookie } from './utils/cookie'
import axios from 'axios';
import {
  thirdLogin,
} from "@/api/sino/sino";
import Print from 'vue-print-nb'
Vue.use(Print)
Vue.prototype.$axios = server

axios.defaults.withCredentials=true;
Vue.use(ElementUI)

Vue.config.productionTip = false

Vue.filter('converTime',function(data,formateStr){
  return Moment(data).format(formateStr)
})

router.beforeEach(async (to, from, next) => {
  let token = getCookie("access_token");
  
  // 🔥 第一优先级：处理企业微信登录回调（无论访问什么路径）
  if (window.location.href.indexOf("code=") != -1 && !token) {
    try {
      NProgress.start();
      
      // 提取code
      let code = window.location.href.split('?')[1];
      code = code.substring(5, code.indexOf('&'));
      
      console.log('🚀 检测到企业微信登录回调, code:', code);
      
      // 🔥 直接调用登录接口
      const res = await thirdLogin({ code: code });
      
      if (res && res.code == 200) {
        console.log('✅ 企业微信登录成功',res);


  
        // 安全访问res.data中的属性
        const userData = res.data || {};
     
        
        // 提取关键数据
        const accessToken = userData.access_token;
        const refreshToken = userData.refresh_token;
        const userId = userData.userId;
    
        
        console.log('🔑 提取的关键数据:', {
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          hasUserId: !!userId,
        
        });
        
        if (accessToken && userId) {
          console.log('✅ 数据完整，设置登录状态...');
          
          // 设置登录信息
          setCookie("access_token", accessToken, 0.125);
          if (refreshToken) {
            setCookie("refresh_token", refreshToken, 0.125);
          }
          
          window.localStorage.setItem("Susername", userId);
          // window.localStorage.setItem("LoginName", userName);
          window.localStorage.setItem("isLogin", "false");  // 改为 false，让 Sino.vue 重新处理登录
          window.localStorage.setItem("ifFirstLogin", "true");
          window.localStorage.setItem("scanLogin", "true");
          window.localStorage.setItem("status", "false");
          
          console.log('🚀 企业微信登录完成，跳转到主页...');

          // 清理URL参数（移除code等参数）
          const cleanUrl = window.location.origin + window.location.pathname;
          window.history.replaceState({}, document.title, cleanUrl);

          NProgress.done();
          next('/sino/index');
          return; // 🔥 重要：直接返回，不继续执行后续逻辑
          
        } else {
          console.error('❌ 关键数据缺失');
          alert('登录失败：无法获取访问令牌或用户信息');
          NProgress.done();
          next('/scanSign');
          return;
        }
      } else {
        console.error('❌ 企业微信登录失败:', res ? res.message : '无响应');
        alert('企业微信登录失败：' + (res ? res.message : '网络错误'));
        NProgress.done();
        next('/scanSign');
        return;
      }
    } catch (error) {
      console.error('❌ 企业微信登录请求失败:', error);
      alert('企业微信登录请求失败，请重试');
      NProgress.done();
      next('/scanSign');
      return;
    }
  }
  
  // 🔥 第二优先级：sino路径的token验证
  if (/\/sino/.test(to.path)) {
    if (token) {
      NProgress.start();
      next();
    } else {
      NProgress.start();
      next("/");
    }
    return; // 处理完sino路径后直接返回
  }
  
  // 🔥 第三优先级：首页路径处理
  if (to.path == "/") {
    console.log('当前token:', token);
    
    if (token) {
      // 原有的token验证逻辑保持不变
      server.post("/oauth/check_token", {}, {
        params: {
          token: token,
        },
      })
      .then(() => {
        if (window.location.href.indexOf("clientid") != -1) {
          let url = window.location.href.split("clientid=")[1];
          window.sessionStorage.setItem("clientid", url.split("#")[0]);
          window.localStorage.setItem("ifFirstLogin", false);
          NProgress.start();
          next("/sino/index");
        } else {
          window.sessionStorage.removeItem("clientid");
          NProgress.start();
          next("/sino/index");
        }
      })
      .catch(() => {
        console.log('token验证失败');
        NProgress.start();
        next("/");
        clearCookie("access_token");
        clearCookie("refresh_token");
      });
    } else {
      // 没有token且没有code参数，正常显示登录页
      NProgress.start();
      next();
    }
    return;
  }
  
  // 🔥 其他路径的处理逻辑保持不变
  if(/\/msgError/.test(to.path)){
    NProgress.start();
    next();
    return;
  }
  
  if(/\/404/.test(to.path)){
    NProgress.start();
    next();
    return;
  }
  
  if(/\/accLogin/.test(to.path)){
    console.log("accLogin token:", token);
    if(token){
      NProgress.start();
      next('/sino/index');
    }else{
      NProgress.start();
      next();
    }
    return;
  }
  
  if(/\/scanSign/.test(to.path)){
    if(token){
      NProgress.start();
      next('/sino/index');
    }else{
      NProgress.start();
      next();
    }
    return;
  }
  
  if(/\/countUser/.test(to.path)){
    if(token){
      NProgress.start();
      next();
    }else{
      NProgress.start();
      next('/');
    }
    return;
  }
  
  // 🔥 默认情况：如果没有匹配到任何路径规则
  NProgress.start();
  next();
})
function GetQueryString(name) {
  var after = window.location.search;
  // 由于#原因search可能为“”，所以地址参数先通过search取值如果取不到就通过hash来取
  after = after.substr(1) || window.location.hash.split("?")[1];
  if (after) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = after.match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    else {
      return null;
    }
  }
}
router.afterEach(()=>{
  return;
})

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
