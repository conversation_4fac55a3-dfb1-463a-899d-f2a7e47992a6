import Vue from 'vue'
import App from './App.vue'
import router from './router'
import server from './utils/request'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import store from './store'
import './assets/fonts/iconfont.css'
import './assets/css/reset.css'
import './assets/css/font.css'
import 'animate.css'
import './assets/icons/iconfont.css'
import Moment from 'moment'
import NProgress from 'nprogress'
import { clearCookie, getCookie ,setCookie } from './utils/cookie'
import axios from 'axios';
import {
  thirdLogin,
} from "@/api/sino/sino";
import Print from 'vue-print-nb'
Vue.use(Print)
Vue.prototype.$axios = server

axios.defaults.withCredentials=true;
Vue.use(ElementUI)

Vue.config.productionTip = false

Vue.filter('converTime',function(data,formateStr){
  return Moment(data).format(formateStr)
})

router.beforeEach((to, from, next) => {
  // console.log(to.path)
  // console.log(from)
  // console.log('main.js',getCookie("access_token"),/\/sino/.test(to.path))

  let token = getCookie("access_token");
  if (/\/sino/.test(to.path)) {
    // alert(1)
    // if(store.state.userName == ""){
    //   NProgress.start();
    //   next('/')
    // }else {
    //   NProgress.start();
    //   next()
    // }
    if (token) {
      // alert(2)
      NProgress.start();
      next();
    } else {
      // alert(3)
      if(window.location.href.indexOf("code=") != -1){
        // alert(4)
        NProgress.start();
        next()
      }else{
        NProgress.start();
        next("/");
      }
    }
  }
  if (to.path == "/") {
    // alert(5)
    let token = getCookie("access_token");
    console.log(token);
    if (token) {
      // alert(6)
      server.post("/oauth/check_token",{},
          {
            params: {
              token: token,
            },
          }
        )
        .then(() => {
          if (window.location.href.indexOf("clientid") != -1) {
            // let url = window.location.href.split("clientid=")[1];
            // let name = window.localStorage.getItem("Susername");
            // createToken({
            //   userName: name,
            //   clientid: url.split("#")[0],
            // }).then((res) => {
            //   console.log("1111111111",res.data.token)
            //   let openUrl = `${res.data.url}?token=${res.data.token}`;
            //   window.location.href = openUrl;
            //   // window.open(openUrl)
            // });
            let url = window.location.href.split("clientid=")[1];
            window.sessionStorage.setItem("clientid", url.split("#")[0]);
            window.localStorage.setItem("ifFirstLogin", false);//非第一次登陆
            NProgress.start();
            next("/sino/index");
          } else {
            // alert(7)
            window.sessionStorage.removeItem("clientid");
            NProgress.start();
            next("/sino/index");
          }
          // NProgress.start();
          // next('/sino/index')
        })
        .catch(() => {
          // alert('失败')
          console.log('shibai')
          NProgress.start();
          next("/");
          clearCookie("access_token");
          clearCookie("refresh_token");
        });
    }else {
      if(window.location.href.indexOf("code=") != -1){
          NProgress.start();
          let code = window.location.href.split('?')[1];
          code = code.substring(5, code.indexOf('&'));  // 截取字符串第6位开始截取直到&出现截取成功
          sessionStorage.setItem('weChatcode', code);
          next('/sino/index')
         
      }else{
        NProgress.start();
        next()
      }
    }
  }
  if(/\/msgError/.test(to.path)){
    NProgress.start();
    next()
  }
  if(/\/404/.test(to.path)){
    NProgress.start();
    next()
  }
  if(/\/accLogin/.test(to.path)){
    console.log("token",token)
    if(token){
      NProgress.start();
      next('/sino/index')
    }else{
      NProgress.start();
      next()
    }

  }
  if(/\/scanSign/.test(to.path)){
    if(token){
      NProgress.start();
      next('/sino/index')
    }else{
      NProgress.start();
      next()
    }
  }
  if(/\/countUser/.test(to.path)){
    if(token){
      NProgress.start();
      next()
    }else{
      NProgress.start();
      next('/')
    }
  }
})
function GetQueryString(name) {
  var after = window.location.search;
  // 由于#原因search可能为“”，所以地址参数先通过search取值如果取不到就通过hash来取
  after = after.substr(1) || window.location.hash.split("?")[1];
  if (after) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = after.match(reg);
    if (r != null) {
      return decodeURIComponent(r[2]);
    }
    else {
      return null;
    }
  }
}
router.afterEach(()=>{
  return;
})

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
