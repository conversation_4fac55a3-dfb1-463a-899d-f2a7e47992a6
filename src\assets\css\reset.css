html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
main,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section {
  display: block;
}

/* HTML5 hidden-attribute fix for newer browsers */
*[hidden] {
  display: none;
}

body {
  line-height: 1;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

.avatar-uploader .el-upload {
  border: 1px dashed #409eff;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.el-submenu__title i {
  color: #B7B6BB;
}
.tableT {
  background-color: #21274d !important;
}

.tableT.el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: transparent;
}

.tableT.el-table thead {
  color: #d3d3d3 75%;
}

.tableT.el-table th {
  background-color: #383d5f;
}
.tableT1{
  margin-left: 15px;
}
.tableT1.el-table th {
  font-weight: bold;
}
.tableT .el-table__row {
  background-color: #21274d !important;
  color: #fff;
}

.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf {
  border-bottom: 0px !important;
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #1B335F  !important;
}
.el-table__body tr.current-row > td.el-table__cell {
  background-color: #1B335F  !important;
}
.details .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #F5F7FA  !important;
}
/* 加载样式 */
/* .el-loading-mask {
  background-color: rgb(33,39,77, 0.8) !important;
} */
/* 页码样式 */
/* .el-input__inner{
  background-color: #21274d !important;
  border:0px  !important;
  color: rgba(255, 255, 255, 0.61)  !important;
} */
.el-pagination .el-select .el-input .el-input__inner {
  background-color: #21274d !important;
  border: 0px !important;
  color: rgba(255, 255, 255, 0.61) !important;
}
.el-pagination .el-pagination__total,
.el-pagination .el-pagination__jump {
  color: rgba(255, 255, 255, 0.61) !important;
}
.el-pagination__editor.el-input .el-input__inner {
  background-color: rgba(255, 255, 255, 0.25) !important;
  border: 0px;
  color: rgba(255, 255, 255, 0.61) !important;
}
.el-pagination button:disabled {
  background-color: #21274d !important;
}
.el-pagination .btn-next,
.el-pagination .btn-prev {
  background-color: #21274d !important;
  color: rgba(255, 255, 255, 0.61) !important;
}
.el-pager li {
  background-color: #21274d !important;
  color: rgba(255, 255, 255, 0.61) !important;
}
.el-pager li.active {
  color: #409eff !important;
}
/* .el-pager{
  background-color: #21274d !important;
} */
.el-aside {
  text-align: unset !important;
}

.last-form {
  margin-bottom: 0;
}

.tips .el-dialog__header {
  background: linear-gradient(to left, #409afb 0%, #38439c 100%);
  padding: 0;
  padding-left: 20px;
  height: 50px;
  line-height: 50px;
}
.tips  .el-dialog__title {
  color: #fff;
}
.addP .el-dialog__body {
  padding: 8px 70px 3px;
  border-top: 1px solid #eee;
}

.addP .el-dialog__header {
  background: linear-gradient(to left, #409afb 0%, #38439c 100%);
  /* background-color: #5a76e1; */
  /* text-align: center; */
  padding: 0;
  padding-left: 20px;
  height: 50px;
  line-height: 50px;
}

.addP  .el-dialog__headerbtn {
  top: 15px;
}
.addP  .el-dialog__headerbtn .el-dialog__close {
  color: #fff;
}
.addP .el-dialog__title {
  color: #fff;
  font-size: 20px;
}

.addP .el-form--label-left .el-form-item__label {
  text-align: right;
}

.addP .el-dialog__footer {
  /* text-align: center; */
}

.deletemenu .el-dialog__body {
  border-top: 1px solid #eee;
}

.deletemenu .el-dialog__footer {
  /* text-align: center; */
}

.deletemenu .el-dialog__header {
  /* background-color: #5a76e1; */
  /* text-align: left; */
  padding: 0;
  padding-left: 30px;
  height: 50px;
  line-height: 50px;
}

.deletemenu .el-dialog__title {
  color: #000;
  font-size: 20px;
}

.powerlabel .el-transfer-panel__body {
  height: 400px;
}

.powerlabel .el-transfer-panel__list.is-filterable {
  height: 380px !important;
  padding-top: 0;
}

.add-button,
.edit-button,
.delete-button {
  color: #fff;
  margin-left: 15px;
  border: none;
}

.add-button {
  background-color: #037cb3;
}
.buttonStyle {
  width: 83px;
  color: #fff;
  margin-left: 15px;
  border: none;
  /*border-radius: 54px;*/
}
.btnStyle {
  width: 83px;
  color: #fff;
  border: none;
  /*border-radius: 54px;*/
}
.el-button--success.is-plain {
  border: 0px !important;
  color: #31C97E !important;
  background: #1F3F4E !important;
  border-color: rgb(0,0,0) !important;
}
.el-button--danger.is-plain {
  border: 0px !important;
  color:rgba(253, 82, 82, 1) !important;
  background: rgba(253, 82, 82, 0.15) !important;
  border-color: rgb(0,0,0) !important;
}
.edit-button {
  background-color: #17b3d9;
}

.delete-button {
  background-color: #04a6ef;
}

.haverole .el-input--suffix .el-input__inner {
  padding-right: 87px;
}

.addP .el-transfer-panel {
  width: 295px;
}
.OKButton {
  height: 38px;
  border-radius: 3px;
  background-color: rgba(14, 97, 201, 1);
  color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(14, 97, 201, 1);
}

.CancButton {
  height: 38px;
  border-radius: 3px;
  background-color: rgba(255, 255, 255, 1);
  color: rgba(16, 16, 16, 1);
  border: 1px solid rgba(236, 236, 236, 1);
}
.addpup{
  border-radius: 3px;
}
.addpup .el-dialog__header {
  background: linear-gradient(to left, #409afb 0%, #38439c 100%);
  padding: 10px 20px;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}
.addpup .el-dialog__header .el-dialog__title {
  color: #fff;
  font-weight: 700;
  font-size: 16px;
}
.addpup  .el-dialog__headerbtn {
  top: 15px;
}
.addpup  .el-dialog__headerbtn .el-dialog__close {
  color: #fff;
}
.addpup .el-dialog__body {
  padding-top: 20px;
  /*padding-left: 50px;*/
  /*padding-right: 50px;*/
  padding-bottom: 15px;
}
.addpup .el-form-item {
  margin-bottom: 16px;
}
.cancelbtn {
   height: 38px;
   border-radius: 3px;
   background-color: #fff;
   color: #aaaaaa;
   border: 1px solid #aaaaaa;
 }
.title_header{
  height: 25px;
  line-height: 25px;
  font-size: 18px;
  color: #c3c9ff;
  margin: 20px 2px 15px;
  padding-left: 15px;
  border-left: 5px solid #c3c9ff;
  float: left;
}
@media screen and (min-width: 1600px) and (max-width: 1920px) {
  .addP .el-transfer-panel {
    width: 384px;
  }
}

@media screen and (min-width: 1400px) and (max-width: 1600px) {
  .addP .el-transfer-panel {
    width: 295px;
  }
}

@media only screen and (max-width: 1400px) {
  .addP .el-transfer-panel {
    width: 248px;
  }
  .tableT{
    font-size: 12px;
  }
  .el-pager li{
    font-size: 12px !important;
  }
  .el-pagination .el-pagination__total, .el-pagination .el-pagination__jump{
    font-size: 12px !important;
  }
  .el-pagination__editor.el-input .el-input__inner{
    font-size: 12px !important;
  }
  .el-pagination .el-select .el-input .el-input__inner{
    font-size: 12px !important;
  }
  .el-form-item__label {
    font-size: 12px !important;
  }
  .el-input__inner{
    font-size: 12px !important;
  }
}

.details .el-dialog__header {
  background: linear-gradient(to left, #409afb 0%, #38439c 100%);
  padding: 0;
  padding-left: 20px;
  height: 50px;
  line-height: 50px;
}

.details .el-dialog__title {
  color: #fff;
  font-size: 20px;
}

.details .el-dialog__headerbtn .el-dialog__close {
  color: #909399;
  /* opacity: 0; */
  display: none;
}
::v-deep .el-image-viewer__img{
  width: 80% !important;
  height: auto !important;
}
/* 隐藏底部操作栏 */
.el-image-viewer__actions{
  display: none;
}